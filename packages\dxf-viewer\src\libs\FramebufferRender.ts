
import { <PERSON>x<PERSON><PERSON><PERSON><PERSON> } from "../viewer"
import { Scene, WebGLRenderer, WebGLRenderTarget } from "three"

const tolerance = 0

export class FramebufferRender{
    public viewer:DxfViewer
    public renderer:WebGLRenderer
    public width:number
    public height:number
    public renderTarget:WebGLRenderTarget
    public idBufferData:Uint32Array
    public idScene:Scene
    constructor(viewer){
        this.viewer = viewer
        this.renderer = viewer.GetRenderer()

        const canvas = viewer.GetCanvas()
        this.width = canvas.width
        this.height = canvas.height
        this.renderTarget = new WebGLRenderTarget(this.width, this.height)
        
        this.idScene = new Scene()
        
        const idBufferSize = (tolerance * 2 + 1) * (tolerance * 2 + 1)
        this.idBufferData = new Uint32Array(idBufferSize * Uint32Array.BYTES_PER_ELEMENT);
    }

    Resize(width, height){
        this.width = width
        this.height = height
        this.renderTarget = new WebGLRenderTarget(this.width, this.height)
    }

    RenderIdBuffer(){
        this.renderer.setRenderTarget(this.renderTarget)
        // TODO idScene
        this.renderer.render(this.idScene, this.viewer.camera!)
        this.renderer.setRenderTarget(null)
    }

    GetIdByMousePosition(x, y){
        const startX = Math.max(x - tolerance, 0)
        const startY= Math.max(y - tolerance, 0)
        const width = Math.min(2 * tolerance + 1, this.width - startX)
        const height = Math.min(2 * tolerance + 1, this.height - startY)
        this.renderer.readRenderTargetPixels(
            this.renderTarget,
            startX,
            startY,
            width,
            height,
            this.idBufferData
            );
    }
    
}