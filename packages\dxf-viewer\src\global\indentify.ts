import { IdentifyScopeType, SlScopeType } from "./constant";
import { cloneDeep } from "lodash";
import { splitBlockId } from "dxf-viewer/src/libs";

export const dxflSobj = reactive({
  //出图
  isdxfPop: false, //出图弹窗显示
  dxfisDraw: false, //是否开始绘制
  ids: [],
});

export const dxfZsObj = reactive({
  dxfBoxselection: [], //v1  v2
  drawingId: "5656", //图纸ID
});
export const pickupInfo = reactive({
  isSLflag: false,
  slcurrentstate: SlScopeType.Empty,
  pickupFalg: false, //图元识别中的 选择图元 控制是否生成图片
  pickupAttributeFalg:false,//根据图元属性批量识别图元  控制是否获取图元的属性
  pickupIdlist: [], //选择图元ID集合
  pickupOBjImgurl: "", //选择图元图片

  identifiedIds: [], //搜图结果ID集合
  identifiedInfo: [], //搜索结果所有点的中心位置XY及列表数据
  hashCodestr: "", //搜图图元标识

  identifyScope: [], //识图矩形多边形范围
  selectedID: [], //已选择id   9,10,11,12

  otherSelectedID: [],

  otherSelectedIdObj: {
    //打开图纸传入的已选择图元id信息
    elements: [],
    groups: [],
  },
  hideSelectedIdObj: {
    elements: [],
    groups: [],
  }, //是否隐藏后的传入的已选择图元id信息

  isSelected: false, //cad中是否选中

  bluePrintCount: "", //底图最大图元数

  exportDxfText: "", //自定义图元的字符串

  fenceIds: [], //传入进来的已创建围栏ID

  layerObj: {
    element: [], //自定义图元的id 围栏支架  例 A12
  },
  customID: [], //处理后自定义的ID 例如围栏支架例 12+bluePrintCount

  fenceDrawlist: [], //绘制状态下，绘制的围栏集合

  blockgls: [], //以识别图元创建块，包含关系
  structureTypeName: "围栏", //当前操作构建类型名字
  
  gcdPointList:[],//图纸高程点
  baseMapLayers:[],//底图的所有图层
  csbl:1,
});

export const modifyOtherSelectedIdObj = (otherSelectedIdObj) => {
  if (otherSelectedIdObj) {
    if (otherSelectedIdObj.elements || otherSelectedIdObj.groups) {
      let newotherSelectedIdObj = cloneDeep(otherSelectedIdObj);
      let arr: any = [];
      let layerelement: any = [];
      if (newotherSelectedIdObj.elements.length) {
        newotherSelectedIdObj.elements.forEach((item) => {
          let lsarr: any = [];
          item.idList.forEach((ite) => {
            ite.ids.forEach((id) => {
              if (id.toString().includes("A")) {
                layerelement.push(id);
              } else {
                arr.push(Number(id));
                lsarr.push(Number(id));
              }
            });
          });
          item.ids = lsarr;
          // let lsarr:any=[]
          // item.ids.forEach(it=>{
          //   if(it.toString().includes('A')) {
          //     console.log(it,'it');
          //     layerelement.push(it)
          //   }else {
          //     arr.push(Number(it))
          //     lsarr.push(Number(it))
          //   }
          // })
          // item.ids=lsarr
        });
      }
      if (newotherSelectedIdObj.groups.length) {
        newotherSelectedIdObj.groups.forEach((item) => {
          // item.idList.forEach(ite=>{
          //   ite.ids=ite.ids.map(i=>{
          //     arr.push(Number(i))
          //     return Number(i)
          //   })
          // })
          item.graphGroups.forEach((ite) => {
            ite.idList.forEach((it) => {
              it.ids = it.ids.map((id) => {
                arr.push(Number(id));
                return Number(id);
              });
            });
          });
        });
      }
      pickupInfo.otherSelectedIdObj = newotherSelectedIdObj;
      console.log(
        pickupInfo.otherSelectedIdObj,
        "pickupInfo.otherSelectedIdObj"
      );

      pickupInfo.layerObj.element = layerelement; //自定义图元 围栏 支架id 例 A12
      pickupInfo.selectedID = arr;
      console.log(pickupInfo.selectedID, "pickupInfo.selectedID");
    }
  } else {
    pickupInfo.selectedID = [];
    pickupInfo.layerObj.element =[]
    pickupInfo.selectedID =[]
  }
};

export const modifylayerObj = (modifylayerObj) => {
  const bluePrintCount = Number(pickupInfo.bluePrintCount);
  if (modifylayerObj) {
    let newmodifylayerObj = cloneDeep(modifylayerObj);
    const all: any = [];
    newmodifylayerObj.element.forEach((it) => {
      if (it.toString().includes("A") && bluePrintCount) {
        let id = Number(it.slice(1)) + bluePrintCount;
        it = id;
      }
      all.push(it);
      pickupInfo.selectedID.push(...all);
    });
    pickupInfo.customID = all;
  }else {
    pickupInfo.customID =[]
  }
};

export const slbatchAddLayer = (dxfview) => {
  let list = slLayerClassify();
  let gpts = dxfview.transaction;
  const dxfbase = gpts.gpObj.getDxfBaseData();
  list.forEach((item) => {
    const layer = new sg.SGLayer();
    layer.setName(item.layerName);
    layer.setColor(new sg.SGColor(231, 44, 48, 255));
    dxfbase.addLayer(layer);
    item.ids.forEach((id) => {
      // let {entityId,blockId}=splitBlockId(id) //entityId块里面的图元ID  blockId快ID
      let obj = gpts.gpObj.getPBaseObjById(id);
      obj.setLayer(item.layerName);
    });
  });
};

// export const slLayerClassify=()=>{
//   let transformedArray:any=[]
//     // 遍历原始数组
//   let originalArray:any=pickupInfo.selectedIDlayer
//   originalArray.forEach(item => {
//     // 查找是否有已存在的项具有相同的 layerName
//     let existingItem = transformedArray.find(obj => obj.layerName === item.layerName);

//     if (existingItem) {
//       // 如果存在，则添加 id 到 ids 数组中
//       existingItem.ids.push(item.id);
//     } else {
//       // 如果不存在，则创建新的项并添加到转换后的数组
//       transformedArray.push({
//         layerName: item.layerName,
//         ids: [item.id]
//       });
//     }
//   });
//   return transformedArray
// }

export const handleidentifiedObj = () => {
  let newOtherSelectedIdObj = cloneDeep(pickupInfo.otherSelectedIdObj);
  if (newOtherSelectedIdObj.elements && newOtherSelectedIdObj.elements.length) {
    newOtherSelectedIdObj.elements = newOtherSelectedIdObj.elements.filter(
      (item) => {
        let falg = true;
        item.idList.forEach((ite) => {
          let lsarr: any = [];
          ite.ids.forEach((id) => {
            if (id.toString().includes("A")) {
              falg = false;
            } else {
              lsarr.push(Number(id));
            }
          });
          ite.ids = lsarr;
        });
        return falg;
      }
    );
  }
  return newOtherSelectedIdObj;
};

export const insertBlockXData = (viewer, namelist) => {
  console.log(namelist, "viewer");

  let arr: any = [];
  const parseString = (str) => {
    return str
      .split("&&")
      .map((s) => s.trim())
      .filter(Boolean)
      .map(Number);
  };
  const zname = (namels) => {
    let arr1 = [];
    namels.forEach((name) => {
      pickupInfo.blockgls.forEach((ite) => {
        if (name === ite.bkName) {
          arr1.push(...ite.bklist);
        }
      });
    });
    return arr1;
  };
  let identifiedGp = viewer.slIdentifiedscene.identifiedGp;
  let insertCount = identifiedGp.insertBlockCount();
  let arrname = zname(namelist);
  for (let j = 0; j < insertCount; j++) {
    let ibk = identifiedGp.getInsertBlock(j);
    let ibkName = ibk.getReferredBlockName();
    // console.log(ibkName,'ibkName');
    if (arrname.includes(ibkName)) {
      arr.push(...parseString(ibk.getStringXData("id")));
    }
  }
  return arr;
};

export const dynamicLoadByTimeStamp = (path, mark="?") => {
  return `${path}${mark}t=${new Date().getTime()}`;
};


