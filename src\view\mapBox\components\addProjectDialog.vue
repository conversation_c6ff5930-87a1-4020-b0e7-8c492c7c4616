<script setup lang="ts">
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import { apiCreateProject, apiUpdateProject } from "../api/listPageApi";
import FileUploader from "./fileUploader.vue";
import mapboxgl from "mapbox-gl";

import { ref } from "vue";
import { mitts, mittEvents } from "sgdraw/mittBus";
import { debounce } from "lodash";

const emit = defineEmits(["refresh"]);

const dialogVisible = ref(false);
const uploadCADRef = ref();
const uploadTIFFRef = ref();
const dialogTitle = ref("创建项目"); // 默认弹窗标题
const isEdit = ref(false); // 编辑状态

// 城市数据API配置
const CITY_API_URL = ref("https://restapi.amap.com/v3/geocode/geo");
const API_KEY = ref("6f3347e545d44c4193a8f8f1b8b9ea90");
const filteredCities = ref<{name: string, longitude: number, latitude: number}[]>([]);
const showCityDropdown = ref(false);
const lastSelectedCity = ref("");

// 新增地图相关状态
const mapDialogVisible = ref(false);
const mapInstance = ref<mapboxgl.Map | null>(null);
const marker = ref<mapboxgl.Marker | null>(null);
const selectedLngLat = ref<{ lng: number; lat: number } | null>(null);
const confirmDialogVisible = ref(false);
const mapCenter : [number, number]= [117.20861541288662,31.836393226525608]
mapboxgl.accessToken = 'pk.eyJ1IjoiamVmZnN0ZXJuIiwiYSI6IlAzRFFiN0EifQ.mNWvayrLEw9wULuq0sopyA'; 

interface ProjectForm {
  projectName: string;
  projectType: string;
  projectCity:string;
  projectIntro: string;
  projectLatitude: number | null;
  projectLongitude: number | null;
  projectCadPath: string;
  projectTiffPath: string;
  projectCode: string | null;
  tiffStatus: string;
  cadStatus: string;
}

const form = ref<ProjectForm>({
  projectName: "",
  projectType: "",
  projectCity:"",
  projectIntro: "", // 项目简介
  projectLatitude: null,
  projectLongitude: null,
  projectCadPath: "", // CAD图纸下载路径
  projectTiffPath: "", // TIF图像下载路径
  projectCode: null, // 添加项目编码字段用于更新
  tiffStatus: "",
  cadStatus: "",
});

const mapSvgIcon = "map";

const options = ref([
  {
    value: "fishery",
    label: "渔光",
  },
  {
    value: "montane",
    label: "山地",
  },
  {
    value: "plains",
    label: "平地",
  },
]);

const formRules = ref({
  projectName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
  projectType: [{ required: true, message: "请输入项目类型", trigger: "blur" }],
});

// 城市搜索方法
const searchCities = (query: string) => {
  if (!query) {
    filteredCities.value = [];
    showCityDropdown.value = false;
    return;
  }
  fetchCities(query)
};

// 从高德地图API获取城市数据
const fetchCities = async (query: string) => {
  try {
    const response = await fetch(`${CITY_API_URL.value}?key=${API_KEY.value}&address=${encodeURIComponent(query)}`);
    const data = await response.json();
    
    if (data.status === "1" && data.geocodes) {
      filteredCities.value = data.geocodes.map((item: any) => {
        const [longitude, latitude] = item.location.split(",").map(Number);
        return {
          name: item.formatted_address,
          longitude,
          latitude
        };
      });
      showCityDropdown.value = filteredCities.value.length > 0;
    } else {
      filteredCities.value = [];
      showCityDropdown.value = false;
    }
  } catch (error) {
    ElMessage.error("城市数据获取失败，请检查网络或API配置");
    filteredCities.value = [];
    showCityDropdown.value = false;
  }
};

// 选择城市
const selectCity = (city: {name: string, longitude: number, latitude: number}) => {
  form.value.projectCity = city.name;
  form.value.projectLongitude = city.longitude;
  form.value.projectLatitude = city.latitude;
  lastSelectedCity.value = city.name;
  showCityDropdown.value = false;
};

// 点击外部关闭城市下拉框
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.city-search-container')) {
    showCityDropdown.value = false;
  }
};

watch(() => form.value.projectCity, (newVal, oldVal) => {
  if ( lastSelectedCity.value !== "" && newVal !== lastSelectedCity.value) {
    form.value.projectLongitude = null;
    form.value.projectLatitude = null;
  }
});

// 地图图标点击处理
const handleMapIconClick = () => {
  mapDialogVisible.value = true;
  // 在下一次DOM更新后初始化地图
  nextTick(() => {
    initMap();
  });
};

// 初始化地图
const initMap = () => {
  // 如果已有地图实例，先移除
  if (mapInstance.value) {
    mapInstance.value.remove();
    mapInstance.value = null;
  };

  const config= {
    container: 'map-container',
    style: 'mapbox://styles/mapbox/streets-v11',
    center: mapCenter, 
    zoom: 15,
    doubleClickZoom: false,
    dragRotate: false,
    touchZoomRotate: false,
    touchPitch: false,
    attributionControl: false
  };

  // 创建地图实例
  const map = new mapboxgl.Map(config);
  mapInstance.value = map;

  // 监听地图加载完成事件
  map.on('load', () => {

    map!.on('contextmenu', (e) => {
      // 阻止默认右键菜单
      e.preventDefault();
      
      // 保存选中的经纬度
      selectedLngLat.value = {
        lng: e.lngLat.lng,
        lat: e.lngLat.lat
      };
      
      if (marker.value) {
        marker.value.remove();
      }
     const newMarker = new mapboxgl.Marker()
        .setLngLat([e.lngLat.lng, e.lngLat.lat])
        .addTo(map);
      
      marker.value = newMarker;
      confirmDialogVisible.value = true;
    });
  });
};

// 确认选点
const confirmLocation = () => {
  if (selectedLngLat.value) {
    form.value.projectLongitude = selectedLngLat.value.lng;
    form.value.projectLatitude = selectedLngLat.value.lat;
    mapDialogVisible.value = false;
    confirmDialogVisible.value = false;
  }
};

// 取消选点
const cancelLocation = () => {
  selectedLngLat.value = null;
  if (marker.value) {
    marker.value.remove();
    marker.value = null;
  }
  confirmDialogVisible.value = false;
};

/* 打开弹窗 */
const openDialog = (projectData: any) => {
  // 先重新绑定mitt事件
  mitts.off(mittEvents["uploadSuccessCAD"]); // 先移除之前的监听
  mitts.off(mittEvents["uploadSuccessTif"]);

  // 重新绑定事件并关联到当前项目
  mitts.on(mittEvents["uploadSuccessCAD"], (fileInfo) => {
    if (fileInfo && fileInfo.storageFileId) {
      form.value.projectCadPath = fileInfo.storageFileId;
      form.value.cadStatus = fileInfo.cadStatus;
      handleUploadSuccess(fileInfo);
    }
  });

  mitts.on(mittEvents["uploadSuccessTif"], (fileInfo) => {
    if (fileInfo && fileInfo.filePath) {
      form.value.projectTiffPath = fileInfo.filePath;
      form.value.tiffStatus = fileInfo.tiffStatus;
      handleUploadSuccess(fileInfo);
    }
  });

  if (projectData && projectData.isEdit) {
    // 编辑模式
    dialogTitle.value = "编辑项目";
    // 编辑状态
    isEdit.value = true;
    // 预填表单数据
    form.value = {
      projectName: projectData.name || "",
      projectType: projectData.type || "",
      projectCity: projectData.city || "",
      projectIntro: projectData.projectIntro || "",
      projectLatitude: projectData.latitude
        ? Number(projectData.latitude)
        : null,
      projectLongitude: projectData.longitude
        ? Number(projectData.longitude)
        : null,
      projectCadPath: projectData.cadUrl || "",
      projectTiffPath: projectData.tiffUrl || "",
      projectCode: projectData.code || null, // 添加项目编码字段用于更新
      tiffStatus: projectData?.tiffStatus || "SUCCESS",
      cadStatus: projectData?.cadStatus || "SUCCESS",
    };
  } else {
    // 新建模式
    dialogTitle.value = "创建项目";
    // 编辑状态
    isEdit.value = false;
    // 重置表单
    form.value = {
      projectName: "",
      projectType: "",
      projectCity: "",
      projectIntro: "",
      projectLatitude: null,
      projectLongitude: null,
      projectCadPath: "",
      projectTiffPath: "",
      projectCode: null,
      tiffStatus: "",
      cadStatus: "",
    };
  }
  dialogVisible.value = true;
};
/* 关闭弹窗 */
const closeDialog = () => {
  dialogVisible.value = false;

  // 重置文件上传组件
  if (uploadCADRef.value) {
    uploadCADRef.value.resetUpload();
  }
  if (uploadTIFFRef.value) {
    uploadTIFFRef.value.resetUpload();
  }

  // 清除mitt事件监听器，避免事件残留
  mitts.off(mittEvents["uploadSuccessCAD"]);
  mitts.off(mittEvents["uploadSuccessTif"]);

  // 重置表单
  form.value = {
    projectName: "",
    projectType: "",
    projectCity: "",
    projectIntro: "",
    projectLatitude: null,
    projectLongitude: null,
    projectCadPath: "",
    projectTiffPath: "",
    projectCode: null,
    tiffStatus: "",
    cadStatus: "",
  };
  showCityDropdown.value = false;
  lastSelectedCity.value = "";
};

// 在setup中定义防抖函数
const showUploadMessage = debounce((message) => {
  ElMessage.success(message);
}, 1000);

/* 修改上传成功处理函数 */
const handleUploadSuccess = (fileInfo) => {
  // 防抖消息提示
  const fileExtension = fileInfo.fileName.split(".").pop().toLowerCase();
  if (fileExtension === "dwg") {
    showUploadMessage(`DWG图纸 ${fileInfo.fileName} 上传成功`);
  } else if (fileExtension === "dxf") {
    showUploadMessage(`DXF图纸 ${fileInfo.fileName} 上传成功`);
  } else {
    showUploadMessage(`TIF图纸 ${fileInfo.fileName} 上传成功`);
  }
};

/* 确定 */
const handleConfirm = () => {
  //TODO: 检查经纬度是否为空
  // if (form.value.projectLongitude === null || form.value.projectLatitude === null) {
  //   ElMessage.warning('请输入项目地址或经纬度信息');
  //   return;
  // }

  // 确保经纬度字段是数字类型
  const formData = {
    ...form.value,
    projectLatitude:
      form.value.projectLatitude !== null
        ? Number(form.value.projectLatitude)
        : null,
    projectLongitude:
      form.value.projectLongitude !== null
        ? Number(form.value.projectLongitude)
        : null,
  };
  // 创建或更新项目
  if (isEdit.value) {
    // 检查文件上传状态
    if (!form.value.projectCadPath && form.value.cadStatus !== "success") {
      ElMessage.warning("请上传CAD图纸");
      return;
    }
    if (!form.value.projectTiffPath && form.value.tiffStatus !== "success") {
      ElMessage.warning("请上传TIF图像");
      return;
    }

    const actionText = "更新";

    apiUpdateProject(formData)
      .then((res) => {
        console.log(`${actionText}项目`, res);
        if (res.code === 200) {
          ElMessage.success(`${actionText}项目成功`);
          // 关闭弹窗
          dialogVisible.value = false;
          // 刷新项目列表
          emit("refresh");
        } else {
          ElMessage.error(res.message || `${actionText}项目失败`);
        }
      })
      .catch((err) => {
        ElMessage.error(`${actionText}项目失败，请稍后重试`);
      });
  } else {
    // 判断是创建还是更新项目
    const actionText = "创建";
    apiCreateProject(formData)
      .then((res) => {
        console.log(`${actionText}项目`, res);
        if (res.code === 200) {
          ElMessage.success(`${actionText}项目成功`);
          // 关闭弹窗
          dialogVisible.value = false;
          // 刷新项目列表
          emit("refresh");
        } else {
          ElMessage.error(res.message || `${actionText}项目失败`);
        }
      })
      .then(() => {
        // 派发时间更新列表
        emit("refresh");
      })
      .catch((err) => {
        ElMessage.error(`${actionText}项目失败，请稍后重试`);
      });
  }
};
/* 取消 */
const handleCancel = () => {
  closeDialog(); // 调用closeDialog重置所有状态
};
/* 暴露方法 */
defineExpose({
  openDialog,
  closeDialog,
});
</script>

<template>
  <el-dialog v-model="dialogVisible" width="50%" @closed="closeDialog">
    <template #header>
      <h3 class="add-project-title">{{ dialogTitle }}</h3>
    </template>
    <div class="add-project-content">
      <el-form
        :model="form"
        label-width="120px"
        size="default"
        :rules="formRules"
      >
        <div class="form-item-wrap">
          <el-form-item
            label="项目名称:"
            class="form-item-left"
            prop="projectName"
          >
            <el-input v-model="form.projectName" />
          </el-form-item>
          <el-form-item
            label="项目类型:"
            class="form-item-right"
            prop="projectType"
          >
            <el-select
              v-model="form.projectType"
              placeholder="请选择项目类型"
              style="width: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item-wrap">
          <el-form-item
            label="项目地址:"
            class="form-item-left"
          >
            <el-input 
              v-model="form.projectCity" 
              @input="searchCities(form.projectCity)"
            />
            <div v-if="showCityDropdown" class="city-dropdown">
              <div 
                v-for="city in filteredCities" 
                :key="city.name"
                class="city-item"
                @click="selectCity(city)"
              >
                {{ city.name }}
              </div>
            </div>
          </el-form-item>
          <el-form-item 
            label="地图选址:" 
            class="form-item-right">
            <div>
              <svg-icon
                :iconClass="mapSvgIcon"
                @click="handleMapIconClick"
              />
            </div>
          </el-form-item>
        </div>
        <div class="form-item-wrap">
          <el-form-item label="项目经度:" class="form-item-left">
            <div class="coordinate-input">
              <el-input-number
                v-model="form.projectLongitude"
                :min="-180"
                :max="180"
                :precision="6"
                :step="0.000001"
                style="width: 100%"
                placeholder="输入经度，范围-180~180"
                class="coord-number"
              />
              <el-tooltip content="经度范围：-180~180度">
                <el-icon class="coord-info"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="项目纬度:" class="form-item-right">
            <div class="coordinate-input">
              <el-input-number
                v-model="form.projectLatitude"
                :min="-90"
                :max="90"
                :precision="6"
                :step="0.000001"
                style="width: 100%"
                placeholder="输入纬度，范围-90~90"
                class="coord-number"
              />
              <el-tooltip content="纬度范围：-90~90度">
                <el-icon class="coord-info"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </el-form-item>
        </div>
        <!-- 第一次新增项目时，不需要上传CAD图纸和TIF图像，当准备进入项目详情时，需要上传CAD图纸和TIF图像 -->
        <div class="form-item-wrap" v-if="isEdit">
          <el-form-item label="CAD图纸上传:" class="form-item-left">
            <FileUploader
              ref="uploadCADRef"
              :buttonText="'上传CAD图纸'"
              :fileType="'cad'"
              :projectCode="form.projectCode || ''"
            />
          </el-form-item>
          <el-form-item label="TIF图像上传:" class="form-item-right">
            <FileUploader
              ref="uploadTIFFRef"
              :buttonText="'上传TIF图像'"
              :fileType="'tif'"
              :projectCode="form.projectCode || ''"
            />
          </el-form-item>
        </div>

        <el-form-item label="项目简介:">
          <el-input
            v-model="form.projectIntro"
            type="textarea"
            placeholder="请输入项目简介"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="add-project-footer">
        <el-button @click="handleCancel" color="#e8e8e8" size="default"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="handleConfirm"
          color="#ff923f"
          style="color: #fff"
          size="default"
          >{{ isEdit ? "更新" : "创建" }}</el-button
        >
      </div>
    </template>
  </el-dialog>
  <!-- 地图弹窗 -->
    <el-dialog
      v-model="mapDialogVisible"
      width="80%"
      top="5vh"
      @closed="mapDialogVisible = false"
    >
      <div id="map-container" style="width: 100%; height: 70vh;"></div>
    </el-dialog>
    
    <!-- 确认选点弹窗 -->
    <el-dialog
      v-model="confirmDialogVisible"
      title="地图选点"
      width="20%"
    >
      <el-form-item
        label="经纬度"
        class="form-item-left"
      >
        <el-input 
          :value="`${selectedLngLat.lng.toFixed(6)}, ${selectedLngLat.lat.toFixed(6)}`" readonly
        />
      </el-form-item>
      <template #footer>
        <el-button @click="cancelLocation">取消</el-button>
        <el-button type="primary" @click="confirmLocation">确定</el-button>
      </template>
    </el-dialog>
</template>

<style scoped lang="scss">
.add-project-content {
  .form-item-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .form-item-left {
      flex: 1;
    }
    .form-item-right {
      flex: 1;
    }
  }

  .city-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 100; /* 确保在表格上层 */
    margin-top: 4px; /* 与输入框保持一点间距 */
  }

  .city-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.3s;
    &:hover {
      background-color: #f5f7fa;
    }
  }

  .coordinate-input {
    display: flex;
    align-items: center;
    width: 100%;
    .coord-number {
      width: 100%;
    }

    .coord-info {
      margin-left: 8px;
      color: #909399;
      cursor: pointer;
    }
  }
}
</style>
