import { pickupInfo,dxfZsObj,splitBlockId } from 'dxf-viewer'
import { getEntityInfo, getEntityInfo2, getEntityRgba, getInterpNum, 
    getSgBufInf, getSGColorRGB, getSGColorRgba } from "dxf-viewer/src/scene/sceneUtils"
import { Transaction } from "./transaction"

export const selectTy=ref([])
export const attributeBarFalg=ref(false)
export const lineTypelist:any=ref([])
export const sections=ref([])
export const xData=ref([])
export const currentTy=ref('')
let elementInfo:any=[]
let currentItem:any={}

const elementtypelist={
    'Arc2d':'圆弧',
    'Circle2d':'圆',
    'Ellipse2d':'椭圆',
    'BSpline2d':'样条曲线',
    'EllipseArc2d':'椭圆弧',
    'LineSegment2d':'直线',
}
const Curve={
    'SGObjCurve':"",
    'SGObjPoint':'点',
    'SGObjComBinCurve':"多段线",
    'SGObjText':'单行文字',
    'SGObjMText':'多行文字',
    'SGObjHatch':'填充',
    'SGObjInsert':'块',
    'SGObjAlignDim':'标注',
    'SGObjOrdinateDim':'标注',
    'SGObjDiametricDim':'标注',
    'SGObjAngular2LDim':'标注',
    'SGObjRadialDim':'标注',
    'SGObjLinearDim':'标注',
}
watch(()=>pickupInfo.pickupIdlist,()=>{
    if(pickupInfo.isSLflag || !attributeBarFalg.value) return
    initattributeBar()
})
const initattributeBar=()=>{
    let sgBDG=Transaction.sgBDG
    let lsarr:any=[]
    pickupInfo.pickupIdlist.forEach(id=>{
        let {entityId,blockId}=splitBlockId(id)
        let newobj:any=null
        
        if(blockId) {
            newobj=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
        }else {
            newobj=sgBDG.getPBaseObjById(id)
        }
        xData.value= getIntXData(newobj)

        let entInf = getEntityInfo(newobj)
        const curvetype =newobj.getObjType()
        let sgCurve:any=''
        let name=''
        let length=''
        let area=''
        let properties:any=[]
        
        if(curvetype === 'SGObjCurve'){
            sgCurve = sg.SGObjTool.convertToCurve(newobj).getCurve()
            let type = sg.SGObjTool.convertToCurve(newobj).getCurveType();
            name=elementtypelist[type] || '未知'
            area=sgCurve.area() || 0
            length=sgCurve.length() || 0
            properties.push({label:'长度', value: length ,type:1,})
            properties.push({label:'面积', value: area ,type:1,})
        }else if(curvetype === 'SGObjComBinCurve'){
            sgCurve=sg.SGObjTool.convertToComBinCurve(newobj).getCurve()
            name=Curve[curvetype] || '未知'
            area=sgCurve.area() || 0
            length=sgCurve.length() || 0
            properties.push({label:'长度', value: length ,type:1,})
            properties.push({label:'面积', value: area ,type:1,})
        }else {
            name=Curve[curvetype] || '未知'
        }
        properties.push({label:'名称', value: name ,type:1,})
        for (let key in entInf) {
            // console.log(key + ': ' + entInf[key]);
            let label=''
            let type='1'
            let value=entInf[key]
            if(key==='color') {
                label='颜色'
                type='2'
            }else if(key==='id') {
                label='其他信息'
                value="id: "+entInf[key]
            }else if(key==='lWidth') {
                label='线宽'
            }else if(key==='layer') {
                label='图层'
                type='2'
            }else if(key==='lineType') {
                label='线型'
                type='2'
            }
            properties.push({
                label, value ,type,
            })
        }
        
        lsarr.push({
            name,
            properties,
            gpobj:newobj,
        })
    })
    elementInfo=lsarr
    lineTypelist.value=summary(lsarr) 
    sections.value=generateSections(lsarr)//lsarr[currentidx.value]
    if(!pickupInfo.pickupIdlist.length){
        xData.value=[]
    } 
}

const generateSections=(lsarr)=>{
    console.log(lsarr,'lsarr');
    let arr:any=[
        {
        title: '基础',
        properties: [
        ]
        },
        {
        title: '曲线参数',
        properties: [
        ]
        },
    ]
    if(lsarr.length>1) {
        lsarr[0].properties.forEach(ite=>{
            if(ite.label==='面积' || ite.label==='长度'){
                let lsitem={...ite}
                lsitem.value='多种'
                arr[1].properties.push(lsitem)
            }else{
                let lsitem={...ite}
                lsitem.value='多种'
                arr[0].properties.push(lsitem)
            }
        })
        
    }else {
        lsarr[0].properties.forEach(ite=>{
            if(ite.label==='面积' || ite.label==='长度'){
                arr[1].properties.push(ite)
            }else{
                arr[0].properties.push(ite)
            }
        })
    }
    return arr
}

const summary=(datalist)=>{
    console.log(datalist,'datalist');
    let lsarr:any=[]
    datalist.forEach(item=>{
        if(lsarr.map(it=>it.name).includes(item.name)){
            let idx=lsarr.map(it=>it.name).indexOf(item.name)
            lsarr[idx].cunt +=1
        }else {
            lsarr.push({name:item.name,cunt:1})
        }
    })
    // console.log(lsarr,'lsarr');
    return lsarr
}

const getIntXData=(gpObj)=>{
    let arr:any=[]
    let XDatakey=gpObj.getStringXDataKeys()
    let count=XDatakey.size()
    for (let i = 0; i < count; i++) {
        let label=XDatakey.get(i)
        let value=gpObj.getStringXData(label)
        arr.push({label,value})
    }
    return arr
}
export const setInXData=(data)=>{
    let sgBDG=Transaction.sgBDG
    function set(gpobj){
        data.forEach(item=>{
            gpobj.writeStringXData(item.label,item.value)
        })
    }
    let lsarr=elementInfo.filter(item=>{
        return item.name===currentItem.name
    })
    lsarr.forEach(obj=>{
        set(obj.gpobj)
    })
    initattributeBar()
}

watch(()=>currentTy.value,()=>{
    if(pickupInfo.isSLflag || !attributeBarFalg.value) return
    currentItem=currentTy.value
    let lsarr=elementInfo.filter(item=>{
        return item.name===currentItem.name
    })
    console.log(lsarr,'lsarr');
    
    sections.value=generateSections(lsarr)
    let lsxdata:any=[]
    console.log(currentItem.gpobjlist,'currentItem.gpobjlist');
    lsarr.forEach(obj=>{
        lsxdata.push(...getIntXData(obj.gpobj))
    })
    xData.value=lsxdata
    
})


