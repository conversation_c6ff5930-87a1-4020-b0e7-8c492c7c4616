{"name": "sungrow-cad", "version": "0.0.1", "private": true, "scripts": {"dev": "vite --mode locality", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "preview": "vite preview --mode production"}, "workspaces": ["PriumCAD/*", "packages/*"], "dependencies": {"@codemirror/theme-one-dark": "^6.1.2", "@salusoft89/planegcs": "^1.1.5", "@types/lodash": "^4.14.202", "axios": "^1.3.4", "c-scrollbar": "^1.0.2", "clipper-lib": "^6.4.2", "codemirror": "^5.65.17", "codemirror-editor-vue3": "^2.7.1", "core-js": "^3.8.3", "echarts": "^5.6.0", "element-plus": "^2.3.0", "element-resize-detector": "^1.2.4", "exceljs": "^4.4.0", "fast-glob": "^3.3.2", "fflate": "^0.8.2", "file-saver": "^2.0.5", "gsap": "^3.11.5", "js-md5": "^0.8.3", "jspdf": "^2.5.2", "jszip": "^3.10.1", "less": "^4.1.3", "libtess": "^1.2.2", "lodash": "^4.17.21", "lru-cache": "^7.18.3", "mapbox-gl": "3.8", "mitt": "^3.0.1", "numeric": "^1.2.6", "ol": "^10.6.1", "pinia": "^2.0.33", "postcss": "^8.4.21", "proj4": "^2.19.3", "sass": "^1.69.7", "sass-loader": "^13.3.3", "signals": "^1.0.0", "simple-web-worker": "^1.2.0", "spark-md5": "^3.0.2", "spectorjs": "^0.9.30", "splitpanes": "^3.1.5", "style-loader": "^3.3.4", "tdesign-vue-next": "^1.13.2", "threads": "^1.7.0", "three": "^0.161.0", "tiny-worker": "^2.3.0", "vue": "^3.2.41", "vue-router": "^4.2.5", "vue-worker": "^1.2.1", "vue3-menus": "^1.1.2", "vue3-perfect-scrollbar": "^1.6.1", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@types/codemirror": "^5.60.15", "@types/three": "^0.161.2", "@vitejs/plugin-vue": "^2.3.3", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "pug": "^3.0.2", "rollup-plugin-copy": "^3.5.0", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.3", "unplugin-vue-components": "^0.26.0", "vite": "^2.9.9", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-workers": "^0.0.0-alpha.0", "worker-loader": "^3.0.8"}, "description": "import sgddFullJS from \"./SGDrawingDesign.js\";", "main": "vite.config.js", "keywords": [], "author": "", "license": "ISC", "peerDependencies": {"element-plus": "^2.3.0"}}