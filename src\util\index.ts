import md5 from 'js-md5';

// interface Item {
//     value: any;
//     label: string;
// }

// export const getListValue=(str:string,arr:Item[])=>{
//   let colorstr=''
//   arr.forEach(item=>{
//     if(item.label===str) {
//       colorstr=item.value
//     }
//   })
//   return colorstr
// }


export const deepClone=(obj)=> {
    if (obj === null) return null;
    let clone = Object.assign({}, obj);
    for (let i in clone) {
        if (typeof clone[i] == "object" && clone[i] != null) {
            clone[i] = deepClone(clone[i]);
        }
    }
    if (Array.isArray(obj)) {
        clone.length = obj.length;
        return Array.from(clone);
    }
    return clone;
}

export const generateFileMD5=(files) =>{
    return  new Promise((resove)=>{
        const reader = new FileReader();
        reader.onloadend =async ( e: any )=> {
            // 从事件对象中获取已读取的文件内容（转换为字节数组）
            const content = e.target.result;
            // 使用js-md5计算文件内容的MD5散列值
            //@ts-ignore
            let id =await md5( content as string );
            resove(id)
        };
        reader.readAsArrayBuffer( files);
    })
}