<template lang="pug">
.row
    .rowtitle(:style="styWid") {{ props.title }}
    div(:style="styWid2")
      slot
        span {{ props.val }}
</template>

<style lang="scss">
.rowtitle{
  text-align: left;
}
</style>

<script setup>
import { computed } from "vue"
const props=defineProps({
    title:{
      type:String,
      default:'xxx'
    },
    val:String|Number,
    twid:{
      type:String,
      default:'60'
    },
    cwid:{
      type:String,
      default:'80'
    }
})
const styWid=computed(()=>{
  return {
    "min-width":`${props.twid}px`,
  }
})
const styWid2=computed(()=>{
  return {
    "min-width":`${props.cwid}px`,
  }
})
</script>