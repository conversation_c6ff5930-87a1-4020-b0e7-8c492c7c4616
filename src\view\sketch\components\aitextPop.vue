<template>
  <draggable v-if="AitextObj.AitextPopflag" @draggableClose="draggableClose">
    <template #title>
    </template>

    <template #body>
      <div style="margin-top: 20px;width: 100%;">
        <el-input
          style="font-size: 16px;"
          v-model="AitextObj.value"
          :rows="5"
          type="textarea"
        />
      </div>
    </template>
  </draggable>
</template>
  
<script lang="ts" setup>
  import { ref,onMounted,nextTick,reactive } from 'vue'
  import { ElLoading ,ElMessage } from "element-plus";
  import draggable from './draggable.vue'

  import {AitextObj,} from '../slMethod/measurementCategory'

  import { pickupInfo,SlScopeType} from 'dxf-viewer'
  import { PubSub } from 'emitter';

  const props = defineProps({
    measurementCategoryObj:{
      type:Object
    },
  })
  const AitextPopflag=ref(false)

  onMounted(()=>{
  })

  const ceshi=()=>{
    AitextPopflag.value=true
  }
  
  const draggableClose=()=>{
    AitextObj.AitextPopflag=false
  }


  defineExpose({

  })
</script>

<style lang="scss" scoped>
.close {
   position: absolute;
   right: 5px;
   top: 5px;
   cursor: pointer;
   padding:5px 10px;
   &:hover{
    background-color: #ecf5ff;
   }
 }
</style>
  