{
    "compilerOptions": {
        "target": "ESNext",             // 目标语言的版本
        "module": "ESNext",        // 指定生成代码的模板标准
        "noImplicitAny": false,       // 不允许隐式的 any 类型
        "removeComments": true,      // 删除注释 
        "preserveConstEnums": true,  // 保留 const 和 enum 声明
        "sourceMap": false,            // 生成目标文件的sourceMap文件
        "esModuleInterop": true,
        "moduleResolution": "Node",
        "allowJs": true,
        "baseUrl": ".",
        "noImplicitOverride": true,
        "noPropertyAccessFromIndexSignature": true,
        "experimentalDecorators": true, //装饰器stage1版本
        "forceConsistentCasingInFileNames": true,
        "strict": true,
        "resolveJsonModule": true,
        "paths": {
            "*": [
              "modules/*"
            ]
          }
      },
    "include": [
        "packages/**/*",
        "PriumCAD/**/*",
        "sgdraw/**/*",
        "src/**/*",
        "src/api/*",
        "src/store/**/*",
        "src/types/*",
    ]
}