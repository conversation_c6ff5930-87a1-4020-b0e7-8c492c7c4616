import { PubSub } from "emitter";
import { DrawState, dxflSobj, pickupInfo,globalDxf } from "../global";
import { DxfViewer } from "../viewer";
import {  KeyboardKey, MouseButton } from "./constant";
import { EditGrips } from "../../../dxfdraw"
import { MouseButtonLeft } from "./mouseButtonLeft";
import { SelectboxHelp } from "./selectboxHelp";

import { screen2wcs } from ".";
import { debounce,throttle } from '../libs'
import { TDiff } from "../libs/timediff";
/**
 * ismousedown 移动过程中判断鼠标是否按下
 */
export class ViewContorls{
    public viewer:DxfViewer
    public ismousedown:boolean=false
    public mousebutton:MouseButton=MouseButton.None
    public get canvas() {
        return this.viewer.canvas
    }
    public selectBoxHelp:SelectboxHelp

    //public editGrips:EditGrips
    public leftMouseButton:MouseButtonLeft
    constructor(viewer:DxfViewer){
        this.viewer=viewer
        this.addListener()
        this.selectBoxHelp=new SelectboxHelp(this.viewer)

        //this.editGrips=new EditGrips(this.viewer)
        this.leftMouseButton=new MouseButtonLeft(this.viewer)
        
    }

    public onKeyDown(event:KeyboardEvent){
        PubSub.default.pub("KeyboardKey",event);
        switch(event.key){
            case KeyboardKey.Delete:
                if(pickupInfo.exportDxfText) {
					// PubSub.default.pub("sl_measure_del");
				}else {
                    // this.viewer.selectionScene.clear()
					// this.viewer.transaction.del(toRaw(pickupInfo.pickupIdlist))
				}
                break;
			case KeyboardKey.Escape:
                this.viewer.selector.clearSelect()//esc清空选择集及图元夹点
				break;
        }
    }

    public onPointerDown(event:PointerEvent) {
        this.ismousedown=true
        this.mousebutton=event.button
        if(this.mousebutton==MouseButton.Left){
            this.leftMouseButton.btnType(event)
        }
        if(this.mousebutton==MouseButton.Right){
            if(this.selectBoxHelp.isSelecting){
                this.selectBoxHelp.del()
            }
            
        }
    }
    public onMove(event){
        // TDiff.log('onPointerMove')
        Object.assign(globalDxf.screenpos,{x:event.offsetX,y:event.offsetY}) 
        let wcs=screen2wcs(event,this.viewer.camera)
        Object.assign(globalDxf.mouse,{x:wcs.x,y:wcs.y})
        Object.assign(globalDxf.wcsmouse,{x:wcs.x+globalDxf.wcsOff.x,y:wcs.y+globalDxf.wcsOff.y})
        if(this.selectBoxHelp.isSelecting){
            this.selectBoxHelp.setV2(event)
            this.viewer.Render()
        }
        if(globalDxf.isdraw && globalDxf.isSnapOn) {//绘制过程中需要打开捕捉吸附
			this.viewer.snap.showMarker(event)
            if(this.viewer.grapicControl.current){
                this.viewer.grapicControl.current.getmove(wcs)
            }
            this.viewer.Render()
		}
        if(globalDxf.drawstate==DrawState.Pick) {
            this.viewer.selector.onMouseMoveHighL(event.offsetX,  event.offsetY)	
		}
    }
    public onPointerMove=throttle(this.onMove.bind(this),40) 


    public onPointerUp(event) {
        this.mousebutton=MouseButton.None
        this.ismousedown=false
    }

    public onMouseWheel(event) {
        if(this.viewer.grapicControl.current) {
			this.viewer.snap.showMarker(event)
		}
    }
    
    public onContextMenu(event){
        event.preventDefault();

        if(this.viewer.grapicControl.current) {
            // this.viewer.grapicControl.current.complete()
            // this.viewer.scene.remove(this.viewer.grapicControl.current[this.viewer.grapicControl.type])

        //   if(this.viewer.grapicControl.current instanceof Fence){
        //         const gps=this.viewer.grapicControl.current.gps
        //         const n = gps.length
        //         for(let i = 0; i < n; i++){
        //             const gp = gps[i]
        //             this.viewer.transaction.gpObj.addBaseObj(gp)
        //             const id = gp.getDrawObjectId()
        //             pickupInfo.pickupIdlist.push(id)
        //         }
        //         this.viewer.transaction.gpRender()
        //     }else {
        //         this.viewer.transaction.add(this.viewer.grapicControl.current.gp)
        //     }
            
            globalDxf.isdraw=false
            this.viewer.grapicControl.current=null

            globalDxf.drawstate=DrawState.Pick
            // this.viewer.Render()
        }
    }

    public addListener() {
        this.canvas.addEventListener('pointerdown', this.onPointerDown.bind(this))
        this.canvas.addEventListener('pointermove', this.onPointerMove.bind(this))
        this.canvas.addEventListener('pointerup', this.onPointerUp.bind(this))
        this.canvas.addEventListener('wheel', this.onMouseWheel.bind(this));
        this.canvas.addEventListener('contextmenu', this.onContextMenu.bind(this))
        document.addEventListener('keydown', this.onKeyDown.bind(this))
    }

    public dispose() {
        this.canvas.removeEventListener('pointerdown', this.onPointerDown)
        this.canvas.removeEventListener('pointermove', this.onPointerMove)
        this.canvas.removeEventListener('pointerup', this.onPointerUp)
        this.canvas.removeEventListener('wheel', this.onMouseWheel);
        this.canvas.removeEventListener('contextmenu', this.onContextMenu)
        document.removeEventListener('keydown', this.onKeyDown)
    }
}
