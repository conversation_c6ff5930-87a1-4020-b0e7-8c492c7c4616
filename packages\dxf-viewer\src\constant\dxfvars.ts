/** Use 16-bit indices for indexed geometry. */
export const INDEXED_CHUNK_SIZE = 0x10000
/** Arc angle for tessellating point circle shape. */
export const POINT_CIRCLE_TESSELLATION_ANGLE = 15 * Math.PI / 180
export const POINT_SHAPE_BLOCK_NAME = "__point_shape"
/** Flatten a block if its total vertices count in all instances is less than this value. */
export const BLOCK_FLATTENING_VERTICES_THRESHOLD = 1024
/** Number of subdivisions per spline point. */
export const SPLINE_SUBDIVISION = 4
/** Limit hatch lines number to some reasonable value to mitigate hanging and out-of-memory issues
 * on bad files.
 */
export const MAX_HATCH_LINES = 20000
/** Limit hatch segments number per line to some reasonable value to mitigate hanging and
 * out-of-memory issues on bad files.
 */
export const MAX_HATCH_SEGMENTS = 20000


/** Default values for system variables. Entry may be either value or function to call for obtaining
 * a value, the function `this` argument is DxfScene.
 */
export const DEFAULT_VARS = {
    /* https://knowledge.autodesk.com/support/autocad/learn-explore/caas/CloudHelp/cloudhelp/2016/ENU/AutoCAD-Core/files/GUID-A17A69D7-25EF-4F57-B4EB-D53A56AB909C-htm.html */
    DIMTXT: function() {
        //XXX should select value for imperial or metric units
        return 2.5 //XXX 0.18 for imperial
    },
    DIMASZ: 2.5,//XXX 0.18 for imperial
    DIMCLRD: 0,
    DIMCLRE: 0,
    DIMCLRT: 0,
    DIMDEC: 2, //XXX 4 for imperial,
    DIMDLE: 0,
    DIMDSEP: ".".charCodeAt(0), //XXX "," for imperial,
    DIMEXE: 1.25, //XXX 0.18 for imperial
    DIMEXO: 0.625, // XXX 0.0625 for imperial
    DIMFXL: 1,
    DIMFXLON: false,
    DIMGAP: 0.625,//XXX for imperial
    DIMLFAC: 1,
    DIMRND: 0,
    DIMSAH: 0,
    DIMSCALE: 1,
    DIMSD1: 0,
    DIMSD2: 0,
    DIMSE1: 0,
    DIMSE2: 0,
    DIMSOXD: false,
    DIMTSZ: 0,
    DIMZIN: 8, //XXX 0 for imperial,
}


/** Custom viewer event names are prefixed with this string. */
export const EVENT_NAME_PREFIX = "__dxf_"

export enum InstanceType{
    /** Not instanced. */
    NONE= 0,
    /** Full affine transform per instance. */
    FULL= 1,
    /** Point instances, 2D-translation vector per instance. */
    POINT= 2,
    /** Number of types. */
    MAX= 3
}