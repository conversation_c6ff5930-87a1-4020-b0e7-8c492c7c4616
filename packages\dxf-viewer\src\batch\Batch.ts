
import {ColorCode, DynamicObjColor, DynamicSceneType} from "../constant"
import { DxfViewer } from "../viewer"
import {BatchEntityType,InstanceType} from '../constant'
import { BufferAttribute, BufferGeometry, InstancedBufferAttribute, InstancedBufferGeometry, InstancedInterleavedBuffer, Int32BufferAttribute, InterleavedBufferAttribute, Line, LineSegments, Mesh, Points, Uint16BufferAttribute, Uint32BufferAttribute, Uint8BufferAttribute, Vector3 } from "three"
// import { Line2 } from 'three/examples/jsm/lines/Line2';
// import { LineGeometry } from "three/examples/jsm/lines/LineGeometry"
// import { LineSegments2 } from "three/examples/jsm/lines/LineSegments2"
// import { LineSegmentsGeometry } from "three/examples/jsm/lines/LineSegmentsGeometry"

import { RenderBatch } from "./renderBatch"
import { <PERSON><PERSON><PERSON><PERSON> } from "./BatchingKey"
import { ERenderMode } from "../viewer/DxfMaterials"
import { CadLine, CadLineSegments } from "../glsl"
export class Batch {
    /**
     * @param viewer {DxfViewer}
     */
    public viewer:DxfViewer
    public renderBatch:RenderBatch
    public key:BatchingKey
    public vertices
    public transforms
    public entityIds
    public entityColors
    public lineStyleIds
    public lineScales
    public chunks
    public transforms0
    public transforms1
    public instanceColor
    public instanceId
    public layerColor
    public get dxfmats(){
        return this.viewer.dxfmats
    }
    constructor(viewer:DxfViewer, batch:RenderBatch) {
        this.viewer = viewer
        this.renderBatch=batch
        this.key = batch.key
        if (this.key.IsIndexed()) {
            this.chunks=[]
            for (const chunk of batch.chunks) {
                let idLen=chunk.entityIds.GetSize()
                const verticesArray =new Float32Array(chunk.vertices.buffer.slice(0,idLen*2))
                const indicesArray =new Uint16Array(chunk.indices.buffer)
                const entityIdsArray =new Uint32Array(chunk.entityIds.buffer.slice(0,idLen))
                const lineStyleIdsArray=new Uint32Array(chunk.lineStyleIds.buffer.slice(0,idLen))
                const lineScalesArray=new Float32Array(chunk.lineScales.buffer.slice(0,idLen))
                const entityColorsArray=new Uint8Array(chunk.entityColors.buffer.slice(0,idLen*4))
                this.chunks.push({
                    vertices: new BufferAttribute(verticesArray, 2),
                    entityIds: new BufferAttribute(entityIdsArray, 1),
                    indices: new BufferAttribute(indicesArray, 1),
                    entityColors:new BufferAttribute(entityColorsArray,4),
                    lineStyleIds:new BufferAttribute(lineStyleIdsArray,1),
                    lineScales:new BufferAttribute(lineScalesArray,1)
                })
            }
        }  else if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE) {
            const instanceIdsArray = new Uint32Array(batch.instanceIds.buffer);
            const idBuf = new InstancedInterleavedBuffer(instanceIdsArray, 1);
            this.instanceId = new InterleavedBufferAttribute(idBuf, 1, 0);

            const instanceTransformsArray = new Float32Array(batch.instanceTransforms.buffer)
            const transBuf = new InstancedInterleavedBuffer(instanceTransformsArray, 6)
            this.transforms0 = new InterleavedBufferAttribute(transBuf, 3, 0)
            this.transforms1 = new InterleavedBufferAttribute(transBuf, 3, 3)

            const instanceColors=new InstancedInterleavedBuffer(new Uint8Array(batch.instanceColors.buffer), 4)
            this.instanceColor=new InterleavedBufferAttribute(instanceColors, 4, 0)
        } else {
            let idLen=batch.entityIds.GetSize()
            if(this.key.geometryType !== BatchEntityType.POINT_INSTANCE) {
                this.vertices=new BufferAttribute(new Float32Array(batch.vertices.buffer.slice(0,idLen*2)),2)
            }
            if(this.key.geometryType === BatchEntityType.POINT_INSTANCE){
                this.transforms = new InstancedBufferAttribute(new Float32Array(batch.vertices.buffer.slice(0,idLen*2)),2)
            }
            this.entityIds=new BufferAttribute(new Uint32Array(batch.entityIds.buffer.slice(0,idLen)),1)
            this.lineStyleIds=new BufferAttribute(new Uint32Array(batch.lineSytleIds.buffer.slice(0,idLen)),1)
            this.lineScales=new BufferAttribute(new Float32Array(batch.lineScales.buffer.slice(0,idLen)),1)
            this.entityColors=new BufferAttribute(new Uint8Array(batch.entityColors.buffer.slice(0,idLen*4)),4)
        }

        // console.log(this.key.layerName,'this.key.layerName');
        const layer = this.viewer.layers.get(this.key.layerName)
        // console.log(layer,'layer');
        
        if (layer) {
            this.layerColor = layer.rgb
        } else {
            this.layerColor = [1, 0.5, 0]
        }
    }

    GetInstanceType() {
        switch (this.key.geometryType) {
            case BatchEntityType.BLOCK_INSTANCE:
                return InstanceType.FULL
            case BatchEntityType.POINT_INSTANCE:
                return InstanceType.POINT
            default:
                return InstanceType.NONE
        }
    }
    getMeshContructor(){
        let objConstructor
        switch (this.key.geometryType) {
            case BatchEntityType.POINTS:
            case BatchEntityType.POINT_INSTANCE:
                objConstructor = Points
                break
            case BatchEntityType.LINES:
            case BatchEntityType.INDEXED_LINES:
            case BatchEntityType.DASH_SEGMENTS:
                objConstructor = CadLineSegments
                break
            case BatchEntityType.DASH_LINES:
                objConstructor = CadLine
                break;
            case BatchEntityType.TRIANGLES:
            case BatchEntityType.INDEXED_TRIANGLES:
                objConstructor = Mesh
                break
            default:
                throw new Error("Unexpected geometry type:" + this.key.geometryType)
        }
        return objConstructor
    }
    /** Create scene objects corresponding to batch data.
     * @param instanceBatch {?Batch} Batch with instance transform. Null for non-instanced object.
     */
    *CreateObjects(instanceBatch = null) {
        if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE ||
            this.key.geometryType === BatchEntityType.POINT_INSTANCE) {
            if (instanceBatch !== null) {
                throw new Error("Unexpected instance batch specified for instance batch")
            }
            yield* this._CreateBlockInstanceObjects()
            return
        }
        yield* this._CreateObjects(instanceBatch)
    }
    *_CreateObjects(instanceBatch=null) {
        let instancetype=instanceBatch?.GetInstanceType() ?? InstanceType.NONE
        const material = this.dxfmats.getMaterial(ERenderMode.Default,this.key,instancetype)
        material.uniforms.layerColor={value:new Vector3(...this.layerColor)};
        let objConstructor=this.getMeshContructor()
        let isdash=this.key.IsDashLine()
        // let isPoint=this.key.IsPoint()
        function CreateObject(vertices, indices, entityIds, entityColors, lineStyleIds,lineScales) {
            // console.log("vertices: ", vertices.array)
            // console.log("entity ids: ", entityIds.array)
            const geometry = instanceBatch ? new InstancedBufferGeometry() : new BufferGeometry()
            geometry.setAttribute("position", vertices)
            geometry.setAttribute("entityId", entityIds)
            geometry.setAttribute("entityColor", entityColors)
            geometry.setAttribute("linestyleId",lineStyleIds)
            geometry.setAttribute("linescale",lineScales)
            instanceBatch?._SetInstanceTransformAttribute(geometry)
            instanceBatch?._SetInstanceIdAttribute(geometry);
            setGeomInterleavedIds(geometry,entityIds.array);
            // (!isPoint)&&setGeomInterleavedIds(geometry,entityIds.array);
            indices&&indices.array.length&&indices.array[1]&&geometry.setIndex(indices);
            const obj = new objConstructor(geometry, material);
            isdash && obj.computeLineDistances();
            obj.frustumCulled = false
            obj.matrixAutoUpdate = false
            // console.log(obj)
            return obj
        }

        if (this.chunks) {
            for (const chunk of this.chunks) {
                yield CreateObject(chunk.vertices, chunk.indices, chunk.entityIds, chunk.entityColors,chunk.lineStyleIds,chunk.lineScales)
            }
        } else {
            yield CreateObject(this.vertices, null, this.entityIds,this.entityColors,this.lineStyleIds,this.lineScales)
        }
    }
    *_CreateBlockInstanceObjects() {
        let block= this.viewer.blocks.get(this.key.blockName)
        if (!block) {
            return
        }
        for (const batch of block.batches) {
            yield* batch.CreateObjects(this)
        }
        if (this.hasOwnProperty("vertices")) {
            yield* this._CreateObjects()
        }
    }
    *CreateIdObjects(instanceBatch = null) {
        if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE ||
            this.key.geometryType === BatchEntityType.POINT_INSTANCE) {
            // TODO id not handled yet
            if (instanceBatch !== null) {
                throw new Error("Unexpected instance batch specified for instance batch")
            }
            yield* this._CreateBlockInstanceIdObjects()
            return
        }
        yield* this._CreateIdObjects(instanceBatch)
    }

    *_CreateIdObjects(instanceBatch) {
        let instancetype=instanceBatch?.GetInstanceType() ?? InstanceType.NONE
        const material = this.dxfmats.getMaterial(ERenderMode.Frameid,this.key,instancetype)
        let objConstructor=this.getMeshContructor()
        // let isPoint=this.key.IsPoint()
        function CreateObject(vertices, indices, entityIds) {
            const geometry = instanceBatch ? new InstancedBufferGeometry() : new BufferGeometry()
            geometry.setAttribute("position", vertices)
            geometry.setAttribute("entityId", entityIds)
            instanceBatch?._SetInstanceTransformAttribute(geometry)
            instanceBatch?._SetInstanceIdAttribute(geometry);
            // (!isPoint)&&setGeomInterleavedIds(geometry,entityIds.array);
            setGeomInterleavedIds(geometry,entityIds.array);
            indices&&indices.array.length&&indices.array[1]&&geometry.setIndex(indices);
            const obj = new objConstructor(geometry, material)
            // obj.computeLineDistances();
            obj.frustumCulled = false
            obj.matrixAutoUpdate = false
            return obj
        }

        if (this.chunks) {
            for (const chunk of this.chunks) {
                yield CreateObject(chunk.vertices, chunk.indices, chunk.entityIds)
            }
        } else {
            yield CreateObject(this.vertices, null, this.entityIds)
        }
    }
    *_CreateBlockInstanceIdObjects() {
        const block = this.viewer.blocks.get(this.key.blockName)
        if (!block) {
            return
        }
        for (const batch of block.batches) {
            yield* batch.CreateIdObjects(this)
        }
        if (this.hasOwnProperty("vertices")) {
            yield* this._CreateIdObjects(null)
        }
    }
    *CreateDynamicObjects(scolorName,dynamicType:DynamicSceneType,instanceBatch = null,eids:number[]) {
        if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE ||
            this.key.geometryType === BatchEntityType.POINT_INSTANCE) {
            yield* this._CreateDynamicBlockInstanceObjects(scolorName,dynamicType,eids)
            return
        }
        let color =scolorName?scolorName:DynamicObjColor[dynamicType];
        let instancetype=instanceBatch?.GetInstanceType() ?? InstanceType.NONE
        const material = this.dxfmats.getMaterial(ERenderMode.Dyanmic,this.key,instancetype,color)
        material.uniforms.isHL={value:1}
        // if(eids.length){
        //     material.uniforms.eids.value=eids;
        //     material.uniforms.neid.value=eids.length;
        // }
        // console.log(material.uniforms,'material');
        
        // let isPoint=this.key.IsPoint()
        let objConstructor=this.getMeshContructor()

        function CreateObject(vertices, indices, entityIds) {
            const geometry = instanceBatch ? new InstancedBufferGeometry() : new BufferGeometry();
            geometry.setAttribute("position", vertices)
            geometry.setAttribute("entityId", entityIds)
            instanceBatch?._SetInstanceTransformAttribute(geometry)
            instanceBatch?._SetInstanceIdAttribute(geometry);
            // (!isPoint)&&setGeomInterleavedIds(geometry,entityIds.array);
            setGeomInterleavedIds(geometry,entityIds.array);
            indices&&indices.array.length&&indices.array[1]&&geometry.setIndex(indices);
            const obj = new objConstructor(geometry, material)
            obj.frustumCulled = false
            obj.matrixAutoUpdate = false
            return obj
        }

        if (this.chunks) {
            for (const chunk of this.chunks) {
                yield CreateObject(chunk.vertices, chunk.indices, chunk.entityIds)
            }
        } else {
            yield CreateObject(this.vertices,null, this.entityIds)
        }
    }
    *_CreateDynamicBlockInstanceObjects(scolorName,dynamicType:DynamicSceneType,eids) {
        const block = this.viewer.blocks.get(this.key.blockName)
        if (!block) {
            return
        }
        for (const batch of block.batches) {
            yield* batch.CreateDynamicObjects(scolorName,dynamicType,this,eids)
        }
    }

    /**
     * @param geometry {InstancedBufferGeometry}
     */
    _SetInstanceTransformAttribute(geometry) {
        if (!geometry.isInstancedBufferGeometry) {
            throw new Error("InstancedBufferGeometry expected")
        }
        if (this.key.geometryType === BatchEntityType.POINT_INSTANCE) {
            geometry.setAttribute("instanceTransform", this.transforms)
        } else {
            geometry.setAttribute("instanceTransform0", this.transforms0)
            geometry.setAttribute("instanceTransform1", this.transforms1)
            geometry.setAttribute("instanceColor",this.instanceColor)
        }
    }

    _SetInstanceIdAttribute(geometry) {
        if (!geometry.isInstancedBufferGeometry) {
            throw new Error("InstancedBufferGeometry expected")
        }
        geometry.setAttribute("instanceId", this.instanceId)
    }

    /**
     * @param defColor {number} Color value for block definition batch.
     * @return {number} RGB color value for a block instance.
     */
    _GetInstanceColor(defColor) {
        if (defColor === ColorCode.BY_BLOCK) {
            return this.key.color
        } else {
            return defColor
        }
    }
}

const setGeomInterleavedIds=(geom,ids)=>{
    let _ids0=new Uint32Array(ids.length)
    _ids0.set(ids.slice(0,ids.length-1),1)
    _ids0[0]=ids[0]
    geom.setAttribute('id0', new BufferAttribute(_ids0,1))
}
