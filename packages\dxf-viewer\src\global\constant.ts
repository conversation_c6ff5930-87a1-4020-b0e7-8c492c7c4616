export enum WorkOn{
    CAD='CAD',
    Quantity="Quantity"
}

export enum DrawState{
    Pick="Pick",
    Drawing="Drawing",
    Plot="Plot",
    IdentifiyBox="IdentifiyBox",
    IdentifiyPolygon="IdentifiyPolygon",
    Pickline="Pickline",
    
}
export enum SlScopeType{
    Empty="Empty",//无状态
    All="ALL",
    RectangularRange="RectangularRange",
    PolygonRange="PolygonRange",
    Del='Del',
    Draw='Draw',//长度类绘制
    Pickup='Pickup',//长度类拾取
    AreaDraw='AreaDraw',//面积绘制
    AreaPickup='AreaPickup',//面积拾取
    ThreeDDraw='ThreeDDraw',//三维绘制
    ThreeDPickup='ThreeDPickup',//三维拾取
    Graphicelementimg="Graphicelementimg",
    Scale='Scale',
    PeiDistinguish='PeiDistinguish',//图元识别
    AI='AI',
    Producepicture='Producepicture',//出图
    DrawingInheritance='DrawingInheritance',//识图继承

}

//选择类型
export enum IdentifyScopeType{
    All="ALL",
    Box="Box",
    Polygon="Polygon",
}

export enum IdentifyState{
    Create='Create',
    Edit='Edit',
    Del='Del',
}

export enum ColorMod{
    ByLayer='ByLayer',
    ByBlock='ByBlock',
    Fixed="Fixed"
}