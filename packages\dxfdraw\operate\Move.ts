import { DrawHelp } from "../drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState,pickupInfo,dxfZsObj,splitBlockId } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"

export class Move extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    public gpList:any //已选择对象集合
    public cs:any={x:0,y:0}
    public count=0
    // public color:string='white'


    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        console.log(this.color,'this.color');
        
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init()
        console.log(this.viewer.renderer,'Line');
        
    }
    public async init(){
        if(pickupInfo.pickupIdlist.length) {
            console.log('move');
            
            let gpwasm=this.viewer.transaction.gpObj
            let arr:any=[]
            pickupInfo.pickupIdlist.forEach(id=>{
                let {entityId,blockId}=splitBlockId(id)
                let obj:any=null
                if(blockId) {
                    obj=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
                }else {
                    obj=gpwasm.getPBaseObjById(id).copyNewOne()
                }
                arr.push(obj)
            })
            this.gpList=arr

            let p0=await this.pointPick({
                tip:"指定的基点",
                pos:'',
            })
            this.setV(p0)
            let p1=await this.lengthPick({
                tip:'指定移动点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
            this.complete()
        }else {
            globalDxf.drawstate=DrawState.Pick
            // poptip.tip(opt)
        }

    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        // console.log('ppppppppppppppppp');
        
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        // console.log('执行了');
        // this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
        let x=v.x-this.pnts[0].x
        let y=v.y-this.pnts[0].y
        
        let trans=new sg.Matrix3()
        trans.move(x-this.cs.x,y-this.cs.y)
        this.cs={x,y}
        this.gpList.forEach(gp=>{
            gp.transform(trans)
        })
        this.viewer.transientSceneWasm.clear()
        this.viewer.transientSceneWasm.draw(this.gpList)
        
    }
    
    complete(){
        // let {x,y}=globalDxf.wcsOff 
        this.viewer.transientSceneWasm.clear()
        let gpwasm=this.viewer.transaction.gpObj
        let x_1=this.pnts[1].x-this.pnts[0].x
        let y_1=this.pnts[1].y-this.pnts[0].y
        let trans=new sg.Matrix3()
        trans.move(x_1,y_1)
        pickupInfo.pickupIdlist.forEach(id=>{
            let {entityId,blockId}=splitBlockId(id)
            let obj:any=null
            if(blockId) {
                obj=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
            }else {
                obj=gpwasm.getPBaseObjById(id)
            }
            obj.transform(trans)
        })
        this.viewer.Render()
        poptip.dispose()
        globalDxf.operation=GraphicType.None
        globalDxf.isdraw = false
        this.isSnap=false
        setTimeout(()=>{
            globalDxf.drawstate=DrawState.Pick
        })
        // this.onCancel()
    }
}

