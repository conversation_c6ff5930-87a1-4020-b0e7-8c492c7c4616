import { AdditiveBlending, Color, DoubleSide, GLSL3, RawShaderMaterial, RepeatWrapping, TextureLoader, Vector3 } from "three"
import { InstanceType } from "../constant"
import { vfs_enity } from "./enity_vfs"
import { CadLineMaterial } from "./CadLineMaterial"
import { vfs_point } from "./point_vfs"
import { vfs_line } from "./line_vfs"
import { vfs_linedash } from "./linedash_vfs"
import { vfs_linedashId } from "./linedashId_vfs"
import { globalDxf } from "../global"
import { vfs_pointId } from "./pointId_vfs"

const texture = new TextureLoader().load( '/img/cadpoint/circle0.png' );
texture.wrapS = RepeatWrapping;
texture.wrapT = RepeatWrapping;

export const crtPoinstMaterial=(instanceType)=>{
    const shaders = vfs_point(instanceType, true)
    return new RawShaderMaterial({
        uniforms: {
            pointTexture:{value:texture},
            pointSize: { value: 10 },
            color: { value: new Color(0xff00ff)},
            eids:{ value:[0] },
            neid:{ value:0 },
            isHL:{value:0},
            layerColor:{value:new Vector3(1,1,1)}
        },
        
        vertexShader: shaders.vertex,
        fragmentShader: shaders.fragment,
        depthTest: false,
        depthWrite: false,
        glslVersion: GLSL3,
        transparent:true
    })
}
export const crtLineMaterial=(instanceType)=>{
    const shaders = vfs_line(instanceType, true)
    return new RawShaderMaterial({
        uniforms: {
            color: { value: new Color(0xff00ff)},
            eids:{ value:[0] },
            neid:{ value:0 },
            isHL:{value:0},
            layerColor:{value:new Vector3(1,1,1)}
        },
        vertexShader: shaders.vertex,
        fragmentShader: shaders.fragment,
        depthTest: false,
        depthWrite: false,
        glslVersion: GLSL3,
    })
}

export const crtLineDashMaterial=(instanceType)=>{
    const shaders = vfs_linedash(instanceType, true)
    return new RawShaderMaterial({
        uniforms: {
            color: { value: new Color(0xff00ff)},
            eids:{ value:[0] },
            neid:{ value:0 },
            isHL:{value:0},
            scale:{value:10},
            layerColor:{value:new Vector3(1,1,1)},
            lineStyle:{value:globalDxf.lineStyleUniforms.lineStyle},
            dash:{value:globalDxf.lineStyleUniforms.dash},
            dashOffset:{value:0}
        },
        // blending: AdditiveBlending,
        vertexShader: shaders.vertex,
        fragmentShader: shaders.fragment,
        depthTest: false,
        depthWrite: false,
        glslVersion: GLSL3
    })
}
export const createSimpleLineMaterial=(instanceType = InstanceType.NONE)=> {
    return new CadLineMaterial({
        color: 0xffffff,
        linewidth: 1,
        dashed: true,
        dash:[3, -0.1, 0.1, -0.1, 0.1, -0.1],
        dashScale:0.1
    })
}

export const createSimplePointMaterial=(instanceType = InstanceType.NONE)=> {
    const shaders = vfs_enity(instanceType, true)
    return new RawShaderMaterial({
        uniforms: {
            color: { value: new Color(0xff00ff)},
            pointSize: { value: 10},
            eids:{ value:[0] },
            neid:{ value:0 },
            isHL:{value:0},
            layerColor:{value:new Vector3(1,1,1)}
        },
        vertexShader: shaders.vertex,
        fragmentShader: shaders.fragment,
        depthTest: false,
        depthWrite: false,
        glslVersion: GLSL3
    })
}

export const createSimpleColorMaterial=(instanceType = InstanceType.NONE)=> {
    const shaders = vfs_enity(instanceType, false)
    return new RawShaderMaterial({
        uniforms: {
            color: { value: new Color(0xff00ff)},
            eids:{ value:[0] },
            neid:{ value:0 },
            isHL:{ value:0 },
            layerColor:{value:new Vector3(1,1,1)}
        },
        vertexShader: shaders.vertex,
        fragmentShader: shaders.fragment,
        depthTest: false,
        depthWrite: false,
        glslVersion: GLSL3,
        side: DoubleSide
    })
}

export const createSimpleColorIdMaterial=(instanceType = InstanceType.NONE)=> {
    const shaders = vfs_linedashId(instanceType, false)
    return new RawShaderMaterial({
        uniforms: {
            color: {
                value: new Color(0xff00ff)
            }
        },
        vertexShader: shaders.vertex,
        fragmentShader: shaders.fragment,
        depthTest: false,
        depthWrite: false,
        glslVersion: GLSL3,
        side: DoubleSide
    })
}

export const createSimplePointIdMaterial=(instanceType = InstanceType.NONE)=> {
    const shaders = vfs_pointId(instanceType, true)
    return new RawShaderMaterial({
        uniforms: {
            color: {
                value: new Color(0x00ff00)
            }, 
            pointSize: {
                value: 10
            }
        },
        vertexShader: shaders.vertex,
        fragmentShader: shaders.fragment,
        depthTest: false,
        depthWrite: false,
        glslVersion: GLSL3,
        side: DoubleSide
    })
}
