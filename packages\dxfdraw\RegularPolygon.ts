import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState,viewCmdInput } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"
import { PubSub } from 'emitter';

export class RegularPolygon extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    public isNextStep:boolean=false
    public polygonNum:number=5

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init() 
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        while(!this.isNextStep){
            let p1=await this.lengthPick({
                tip:'指定第二个点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
        }
        this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    public override onKeyUp( event:KeyboardEvent ) : boolean{
        PubSub.default.pub('cmdInput')
        if (event.key === ' ' || event.key === 'Spacebar' ||  event.key === 'Enter'){
            console.log(viewCmdInput.CmdInputValue,'viewCmdInput.CmdInputValue');
            
        }
        return true;
    }

    setV(v:Vector3){
        if(this.pnts.length) {
            if(this.isNextStep) {
                this.pnts.push(v)
            }
        }else {
            this.pnts.push(v)
        }
        
    }
    public drawTempGraphic(v:Vector3){
        let plist=this.getvertices([...this.pnts,v])
        this.mesh.geometry=new BufferGeometry().setFromPoints(plist);
    }
    
    complete(){
        let {x,y}=globalDxf.wcsOff
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        if(this.pnts.length<2) return
        let plist=this.getvertices(this.pnts)
        const wasmVPlist=this.setWasmVPlist(plist)
        let wasmBulge = new sg.vector_double()
        let  pl=new sg.PolyLine2d(wasmVPlist, wasmBulge, false)
        let  plobj=new sg.SGObjComBinCurve()
        plobj.setpCurve(pl)
        let [r,g,b]=this.color
        plobj.setColor(new sg.SGColor(r,g,b,255))
        plobj.setLineType(this.lineType)

        this.viewer.transaction.add(plobj)
        globalDxf.operation=GraphicType.None
        globalDxf.isdraw = false
        this.isSnap=false
        setTimeout(()=>{
            globalDxf.drawstate=DrawState.Pick
        })
    }

    private getvertices(vlist) {
        const pointA = vlist[0]
        const pointC = vlist[1]
        const pointB = new Vector3(pointC.x, pointA.y, pointA.z);
        const pointD = new Vector3(pointA.x, pointC.y, pointC.z);
        // 定义矩形的所有顶点
        const vertices = [pointA,pointB,pointC,pointD,pointA];
        return vertices
    }
    private setWasmVPlist(vList){
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x, item.y))
        })
        return wasmVPlist
    }
}


// import * as THREE from "three"
// import {ElementStyle} from './ElementStyle'

// export class RegularPolygon extends ElementStyle {
//     public vPlist:THREE.Vector3[]=[]
//     public count:number=0
//     public iscomplete:boolean=false
//     public RegularPolygon:any
//     public gp:any 
//     public wasmBulge:any 
//     constructor() {
//         // 调用父类的构造函数以初始化继承的属性
//         super();
//         // 创建直线的几何体
//         const geometry = new THREE.BufferGeometry();
//         // 生成圆形顶点数据
//         const vertices:any = [];
//         geometry.setFromPoints(vertices);
//         const material = new THREE.LineBasicMaterial({ color:this.color});
//         this.RegularPolygon = new THREE.LineLoop(geometry, material);
//         this.wasmBulge = new sg.vector_double()
//     }
//     setV(v:THREE.Vector3){
//         this.count++
//         this.vPlist.push(v)
//         switch ( this.count) {
// 			case 1:
// 				break;
// 			case 2:
//                 this.iscomplete=true

//                 let vertices=this.getvertices(this.vPlist[1])
//                 const wasmVPlist=this.setWasmVPlist(vertices)
//                 let  pl=new sg.PolyLine2d(wasmVPlist, this.wasmBulge, true)
//                 this.gp=new sg.SGObjComBinCurve()
//                 this.gp.setpCurve(pl)
//                 break;
// 			default:
//                 break;
// 		}
//     }
//     getmove(v:THREE.Vector3){
//         if(!this.vPlist.length) return
//         let vertices=this.getvertices(v)
//         this.RegularPolygon.geometry.dispose();
//         this.RegularPolygon.geometry=new THREE.BufferGeometry().setFromPoints(vertices);

//     }
//     complete(){
//         if(this.vPlist.length<2) return
//         let vertices=this.getvertices(this.vPlist[1])
//         this.RegularPolygon.geometry.dispose();
//         this.RegularPolygon.geometry=new THREE.BufferGeometry().setFromPoints(vertices);
//     }
    
//     getisitCompleted(){
//         return this.iscomplete
//     }

//     private getvertices(end:THREE.Vector3) {
//         const polyN =5;
//         const start = this.vPlist[0];
//         const vec = new THREE.Vector3().subVectors(end, start);
//         const quaternion = new THREE.Quaternion();
//         const vertices = [end];
//         for(var i = 1; i < 5; i++){
//             const rotateVec = new THREE.Vector3()
//             rotateVec.copy(vec)
//             const angle = 2* Math.PI / polyN * i;
//             quaternion.setFromAxisAngle( new THREE.Vector3(0, 0, 1),  angle);
//             rotateVec.applyQuaternion(quaternion)
//             vertices[i] = new THREE.Vector3().addVectors(start, rotateVec);
//         }
		
//         return vertices
//     }
//     private setWasmVPlist(vList){
//         const wasmVPlist= new sg.vector_Point2d()
//         vList.forEach(item=>{
//             wasmVPlist.push_back(new sg.Point2d(item.x, item.y))
//         })
//         return wasmVPlist
//     }
// }