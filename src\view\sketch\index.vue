<template>
  <div class="dxfview" ref="refdxfview" v-on:click.right="handleRightClick" v-on:click.left="handleLeftClick" >

    <!-- <button  @click="ceshi333">tuceng</button> -->
    <!-- <SideMenu></SideMenu> -->

    <MenuBar :measurementCategoryObj="measurementCategoryObj"
     :sLcontrolObj="sLcontrolObj"
     :menuBarMethodObj="menuBarMethodObj"
     :refdxfpop='refdxfpop'
     ref="refMenuBar"
     v-if="isMeuBarShow" @attach="attach" @inherit="inherit"
      @drawingComparison="drawingComparison"></MenuBar>
    <div class="btn" v-if="plotBtnisshow">
      <button :style="{ backgroundColor: dxflSobj.dxfisDraw ? '#409eff' : '#ccc' }"
        @click="btnProduceapicture">绘制图纸</button>
    </div>
    
    <rightClickMenu v-if="rightClickMenuObj.rightClickMenUpoP" :buttonList="rightClickMenuObj.buttonList"
      :position="rightClickMenuObj.position" @rightClickMenuconfirm="rightClickMenuconfirm"></rightClickMenu>

    <DxfViewer ref="viewers" :dxfUrl="dxfUrl" :fonts="fonts" @dxf-loaded="_OnLoaded" @dxf-cleared="_OnCleared"
      @dxf-message="_OnMessage"></DxfViewer>

    <GraphicRecognitionPop :identifyImgObj="identifyImgObj" :sLcontrolObj="sLcontrolObj"
      @mapRecognitionResultsSee="mapRecognitionResultsSee">
    </GraphicRecognitionPop>

    <ScalePop :measurementCategoryObj="measurementCategoryObj" :sLcontrolObj="sLcontrolObj"
      @sizePopseve="btnsizePopseve" @measureChangescale="measureChangescale"></ScalePop>
    <!-- 出图 -->
    <el-dialog :model-value="dxflSobj.isdxfPop" title="图纸" width="500" :before-close="btnCancellation">
      <div>图纸名称：<el-input v-model="drawingName" style="width: 240px" placeholder="" /></div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="btnSave">确认</el-button>
          <el-button @click="btnCancellation">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>


    <dxfpop ref="refdxfpop" :fonts="fonts"></dxfpop>
    <AitextPop ref="AitextPopref"  :measurementCategoryObj="measurementCategoryObj"></AitextPop>
    
</template>


<script setup>

import DxfViewer from './components/DxfViewer.vue'
import { DxfViewer as _DxfViewer, dxfViewMessageLevel, addQuantityCalculationLayer } from "dxf-viewer"

import rightClickMenu from './components/rightClickMenu.vue'
import MenuBar from './components/menuBar.vue'
import ScalePop from './components/scalePop.vue'
import dxfpop from './components/dxfpop.vue'
import AitextPop from './components/aitextPop.vue'

import SideMenu from './components/SideMenu.vue'

import GraphicRecognitionPop from './components/graphicRecognitionPop.vue'

import { ElLoading, ElMessage } from "element-plus";
import {
  DrawState, IdentifyState, pickupInfo, modifyOtherSelectedIdObj, modifylayerObj,splitBlockId,
  dxflSobj, dxfZsObj, uploadFileAsText, SlScopeType,KeyboardKey,dynamicLoadByTimeStamp,initDxfLayerData,
} from 'dxf-viewer'
import {idmovecamera} from "dxf-viewer/src/libs"

import { IdentifyImg } from './slMethod/identifyImg'
import { PubSub } from 'emitter';
import {
  measureObj, MeasurementCategory, distinguishObj,
  rightClickMenuObj,
} from './slMethod/measurementCategory'
import { MenuBarMethod ,comparisonResultsGp} from './slMethod/menuBarMethod'
import { SLcontrol } from 'SLabutment/slcontrol'
import { Snap } from "dxf-viewer/src/scene/Snap"



import { ref, onMounted, reactive, watchEffect, nextTick } from 'vue'
import { useRouter } from "vue-router";
import { cloneDeep } from 'lodash';
import { LoadDxf } from 'sgdraw/util/fileUpload'

import JSZip, { JSZipGeneratorOptions } from "jszip";


const router = useRouter();
const LDxf = new LoadDxf()

const emits = defineEmits(['cadsuisave', 'cadgraphicelementImg','cadgraphicelementAttribute',
 'delGraphicElementApi', 'cadRightClickMenuconfirm','cadLeftClickMenuconfirm', 'attach','inherit','drawingComparison'])

let viewers = ref(null)
let refdxfview = ref(null)
const refMenuBar=ref(null)

const drawingName = ref('')
const dxfUrl = ref(null)
const props = defineProps({
  fonts: {
    type: Array,
    default: []
  },
  isMeuBarShow: {
    type: Boolean,
    default: true
  }
})
// var getSgBufInf=()=>{
//     let buf= sg.getShareBuffer();
//     sg.freeShareBuffer();
//     let [id,r,g,b,a,lWidth,lscale]=buf.slice(4,11)
//     let color=`rgba(${r},${g},${b},${a})`
//     let vertices=buf.slice(18)
//     return {
//         id,
//         color,
//         lWidth,
//         lscale,
//         vertices,
//         rgb:[r,g,b]
//     }
// }


const dxfObj = reactive({
  layers: null,
  inputFile: null,
  isLocalFile: false,
  aboutDialog: false,
  urlDialog: false,
  inputUrl: null
})

const plotBtnisshow = ref(false) //出图按钮

const pickupResultObj = reactive({
  cloneLIst: [],
  pagerCount: 1,
  pageSize: 10,
  total: 0,
})

const identifyImgObj=ref(null)
const measurementCategoryObj=ref(null)
const menuBarMethodObj=ref(null)
const sLcontrolObj=ref(null)

const refdxfpop=ref(null)


onMounted(() => {
  try {
    window.postMessage({ type: 'Wasm', payload: 'Loading completed' }, '*');
  } catch (error) {
    window.postMessage({ type: 'Wasm', payload: 'Loading completed' }, '*');
  }
  
  nextTick(()=>{
    sLcontrolObj.value=new SLcontrol(viewers.value)
    identifyImgObj.value= new IdentifyImg(viewers.value)
    measurementCategoryObj.value=new MeasurementCategory({dxfview:viewers.value,sLcontrolObj:sLcontrolObj.value})
    menuBarMethodObj.value=new MenuBarMethod(viewers.value)
    

    PubSub.default.sub("modelToImage", graphicelementImg);//选择图元的图片
    PubSub.default.sub("modelToAttribute", graphicelementAttribute);//选择图元的图片
    PubSub.default.sub("sl_measure_scale", measurementCategoryObj.value.opensizePop); //比例尺
    PubSub.default.sub("KeyboardKey", isdelObj); //删除
  })
})


const btnDisplaydrawings = async (obj) => {//打开新图纸
  console.log(obj,'cadobjobjobj');

  if (obj.drawingUrl) {
    pickupInfo.gcdPointList=[]
    pickupInfo.isSLflag=true
    dxfZsObj.dxfBoxselection = obj.dxfBoxselection || []
    pickupInfo.otherSelectedIdObj = cloneDeep(obj.otherSelectedIdObj)
    modifyOtherSelectedIdObj(pickupInfo.otherSelectedIdObj)

    plotBtnisshow.value = obj.plotBtnisshow
    pickupInfo.exportDxfText = obj.layerObj && obj.layerObj.layerInfo ? obj.layerObj.layerInfo : ''
    measureObj.measureScaleText = obj.scale || '1'
    
    // sLcontrolObj.value.setState(SlScopeType.Empty)
    sg.DxfImportManager.deleteAll()
    if (dxfZsObj.drawingId !== obj.drawingId || !dxfObj.inputFile) {
      dxfZsObj.drawingId = obj.drawingId || ''
      const loading = ElLoading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      
      try {
        //把url转成File再解压
        const dxfFile = await urlToFileDecomp(obj.drawingUrl, obj.isZip || false)
        if (dxfUrl.value && dxfObj.isLocalFile) {
          URL.revokeObjectURL(dxfUrl.value)
        }
        dxfObj.isLocalFile = true
        dxfObj.inputFile = dxfFile
        dxfUrl.value = URL.createObjectURL(dxfObj.inputFile)

        await viewers.value?.Load(dxfFile)

        viewers.value.GetViewer().selectionScene.isPinchpoint=false //关闭夹点编辑功能
        viewers.value.GetViewer().drawingSelected2()
        menuBarMethodObj.value.hidehandle()
        sLcontrolObj.value.setState(SlScopeType.Empty)
      } catch (error) {
        console.error('There has been a problem with your fetch operation:', error);
      } finally {
        loading?.close();
      }
    } else {
      viewers.value?.Load(dxfObj.inputFile)//dxfObj.inputFile
      viewers.value.GetViewer().selectionScene.isPinchpoint=false //关闭夹点编辑功能
      // viewers.value.GetViewer().snap.m_snapOn=false //关闭捕捉吸附 iscatch
      Snap.turnOffSnap()//关闭捕捉吸附
      viewers.value.GetViewer().drawingSelected2()
      menuBarMethodObj.value.hidehandle()
      sLcontrolObj.value.setState(SlScopeType.Empty)
    }

  }
}

const againOpenOperated=async(obj)=>{//重新打开已选择的图元和自定义的图元
  console.log(obj,'再次打开obj');
  
  let viewer= viewers.value.GetViewer()
  identifyImgObj.value.pickupResultClear()
  pickupInfo.structureTypeName=obj.structureTypeName || '围栏'

  let customGraphicElementsids=viewer.transaction.gpObj.getGreatMaxIds()
  let num=customGraphicElementsids.size()
  let delid=[]
  for (let i =0; i < num; i++) {
    let id=customGraphicElementsids.get(i)
    delid.push(id)
  }
  viewer.transaction.del(delid)
  // console.log(pickupInfo.bluePrintCount,'pickupInfo.bluePrintCount');
  
  viewer.transaction.gpObj.deleteMaxIdObjs(pickupInfo.bluePrintCount) //删除自定义图元
  
  viewer.slIdentifiedscene.clear()
  dxfZsObj.dxfBoxselection = obj.dxfBoxselection || []
  pickupInfo.otherSelectedIdObj = cloneDeep(obj.otherSelectedIdObj)
  modifyOtherSelectedIdObj(pickupInfo.otherSelectedIdObj)
  pickupInfo.exportDxfText = obj.layerObj && obj.layerObj.layerInfo ? obj.layerObj.layerInfo : ''

  if(pickupInfo.exportDxfText) {
    // let usedcount=viewer.transaction.gpObj.count()
    // console.log(usedcount,'usedcount');
   let newgplist=viewer.transaction.gpObj.appendCutOffString(pickupInfo.exportDxfText)
  //  console.log(viewer.transaction.gpObj.count(),'countcountcountcount');
   
    modifylayerObj(pickupInfo.layerObj)
    const newgpCount = newgplist.size()
    console.log(newgpCount,'newgpCountnewgpCountnewgpCount');
    let objs=[]
    for (let i = 0; i < newgpCount; i++) {
      let a = newgplist.get(i);
      a && objs.push(a)
    }
   await viewer.dxfScene.addEntities(objs)
  }

  viewer.drawingSelected2()
  menuBarMethodObj.value.hidehandle()
  viewer.Render()
  sLcontrolObj.value.setState(SlScopeType.Empty)

}

const btnSave = () => {
  if (!drawingName.value) return ElMessage({ message: '请输入名称', type: 'warning', })
  emits('cadsuisave', { dxfBoxselection: dxflSobj.ids, drawingName: drawingName.value })
  drawingName.value = ''
  dxflSobj.isdxfPop = false
  dxflSobj.dxfisDraw = false
  dxflSobj.ids = []
  PubSub.default.pub("slidentifyclear");
}
const btnCancellation = () => {
  dxflSobj.isdxfPop = false
  dxflSobj.dxfisDraw = false
  dxflSobj.ids = []
  PubSub.default.pub("slidentifyclear");
}
const btnProduceapicture = () => {
  dxflSobj.dxfisDraw = !dxflSobj.dxfisDraw//是否开始出图
  if (dxflSobj.dxfisDraw) {
    sLcontrolObj.value.setState(SlScopeType.Producepicture)
  } else {
    sLcontrolObj.value.setState(SlScopeType.Empty)
  }
}

const _OnLoaded = () => {
  const obj = viewers.value.GetViewer().GetLayers()
  obj.forEach(lyr => lyr.isVisible = true)
  dxfObj.layers = obj
}


const getCadData = (type) => {
  let obj={}
  switch (type) {
    case 'identifyTheImages':
          obj= {
            identifiedIds: pickupInfo.identifiedIds,
            hashCodestr:pickupInfo.hashCodestr,
          }
      break
    case 'layerInfo':
          obj={

          }
      break
  }
  return obj
}
const delIDGraphicElement=async(idlist)=>{//根据id删除图元
  if(!idlist.length)return
  const bluePrintCount = Number(pickupInfo.bluePrintCount)
  let viewer= viewers.value.GetViewer()
  let arr=idlist.map(id=>{
    let newid
    if(id.toString().includes('A')) {
      newid=Number(id.slice(1)) + bluePrintCount
    }else {
      newid=Number(id)
    }
    return newid
  })
  // pickupInfo.selectedID=pickupInfo.selectedID.filter(id=>!arr.includes(id))
  console.log(arr,'arrr');
  
  await viewer.transaction.del(arr)
  viewer.selectionScene.clear()
  viewer.Render()
}
const creanCadWasm = () => {
  // sg.DxfImportManager.deleteDxf(dxfZsObj.drawingId)
  sg.DxfImportManager.deleteAll()
}

const _OnCleared = () => {
  dxfObj.layers = null
}
const _OnMessage = (e) => {
  let type = "info"
  switch (e.detail.level) {
    case dxfViewMessageLevel.WARN:
      type = "warning"
      break
    case dxfViewMessageLevel.ERROR:
      type = "negative"
      break
  }
  console.log(e.detail.message);
  //   this.$q.notify({ type, message: e.detail.message })
}
const handleRightClick=(e)=>{//右键菜单  SlScopeType.AI SlScopeType.PeiDistinguish
  let arr=[SlScopeType.Pickup, SlScopeType.Draw, SlScopeType.AreaDraw,SlScopeType.ThreeDDraw,
  SlScopeType.AreaPickup,SlScopeType.ThreeDPickup, SlScopeType.Del,]
  if(!arr.includes(pickupInfo.slcurrentstate)) return
  console.log(pickupInfo.pickupIdlist,'pickupInfo.pickupIdlist');
  
  if(!(pickupInfo.pickupIdlist.length || pickupInfo.fenceDrawlist.length)) {
    emits('cadRightClickMenuconfirm', {values:[],type:'null'})
    sLcontrolObj.value.setState(SlScopeType.Empty)
    return
  }
  rightClickMenuconfirm()
}
const handleLeftClick=async()=>{//左键菜单
  let arr=[SlScopeType.AI]
  if(!arr.includes(pickupInfo.slcurrentstate)) return
  if(!pickupInfo.pickupIdlist.length) return
  await measurementCategoryObj.value.leftClickConfirm()
  let obj={
    type:''
  }
  if (pickupInfo.slcurrentstate === SlScopeType.AI) {
    obj.type = 'text'
    obj.AItext = measurementCategoryObj.value.AItext
  }
  emits('cadLeftClickMenuconfirm', obj)
  sLcontrolObj.value.setState(SlScopeType.Empty)
}

/*********************************测量***************************************************/
const measureChangescale = () => {
  emits('changescale', { scale: measureObj.measureScaleText })  //比例尺
}
const btnsizePopseve = () => {
  measureObj.measureScaleText = (measureObj.sizetext / measureObj.measureLength).toFixed(2)
  measurementCategoryObj.value.closesizePop()
  measurementCategoryObj.value.openPop()
  measureChangescale()
}
const isdelObj = (event) => {//del按钮删除
  switch(event.key){
    case KeyboardKey.Delete:
          if(pickupInfo.exportDxfText) {
            // delIDGraphicElement(pickupInfo.pickupIdlist)
          }
      break;
    case KeyboardKey.Escape:
          sLcontrolObj.value.setState(SlScopeType.Empty)
      break;
  }
}

const openControlPanelPop = () => {//比例尺
  measurementCategoryObj.value.openPop()

}

const rightClickMenuconfirm = async () => {
  await measurementCategoryObj.value.rightClickConfirm()
  let obj = {
    type: '',
    AItext: [],
    layerInfo: '',
    values: '',
    deleteObj: '',
  }
  if (pickupInfo.slcurrentstate === SlScopeType.AI) {
    obj.type = 'AI'
    obj.AItext = measurementCategoryObj.value.AItext
  } else if (pickupInfo.slcurrentstate === SlScopeType.Del) {
    obj.type = 'del'
    obj.deleteObj = measurementCategoryObj.value.deleteInfo
  } else {
    obj.values = measurementCategoryObj.value.fenceGroup || []
    obj.layerInfo = measurementCategoryObj.value.layerInfo || ''
  }
  emits('cadRightClickMenuconfirm', obj)
  sLcontrolObj.value.setState(SlScopeType.Empty)
}
const AIdistinguish = () => {//cad 触发AI识字
  sLcontrolObj.value.setState(SlScopeType.AI)
}

const switchMenu = (obj) => {//CAD触发切换菜单
  menuBarMethodObj.value.menuControl(obj)
  pickupInfo.structureTypeName=obj.structureTypeName || '围栏'
  // measurementCategoryObj.value.operationType('')
  sLcontrolObj.value.setState(SlScopeType.Empty)
}

const generateSubComponent = async(mainComp) => {//支架
  const sgmainComp = new sg.vector_ElementObj()
  const lsarr=[]
  
  let gp=viewers.value.GetViewer().transaction.gpObj
  if (mainComp.elements.length || mainComp.groups.length) {
    mainComp.elements.forEach(item => {
      item.ids.forEach(id => {
        if(!id.toString().includes('A')) {
          const vecint = new sg.vector_int
          vecint.push_back(Number(id))
          lsarr.push({ids:[id]})
          const element = new sg.ElementObj(vecint)
          sgmainComp.push_back(element)
        }
      })
      // item.idList.forEach(ite=>{
      //   ite.ids.forEach(id=>{
      //     if(!id.toString().includes('A')) {
      //       const vecint = new sg.vector_int
      //       vecint.push_back(Number(id))
      //       const element = new sg.ElementObj(vecint)
      //       sgmainComp.push_back(element)
      //     }
      //   })
      // })
    })
    mainComp.groups.forEach(item => {
      const vecint = new sg.vector_int
      lsarr.push({ids:item.ids})
      item.ids.forEach(id => {
        vecint.push_back(Number(id))
      })
      const element = new sg.ElementObj(vecint)
      sgmainComp.push_back(element)

      // item.graphGroups.forEach(ite=>{
      //   ite.idList.forEach(it=>{
      //     const vecint = new sg.vector_int
      //     it.ids.forEach(id=>{
      //       vecint.push_back(Number(id))
      //     })
      //     const element = new sg.ElementObj(vecint)
      //     sgmainComp.push_back(element)
      //   })
      // })
    })
  }
  console.log(lsarr,'lsarr');
  
  const sgsubComponentIDs = sg.addSubsidiaryComponent(dxfZsObj.drawingId, sgmainComp, '支架');
  const subComponentID = []
  let zjid=[]
  let objs=[]
  const size = sgsubComponentIDs.size()
  for (let i = 0; i < size; i++) {
    const id = sgsubComponentIDs.get(i)
    pickupInfo.selectedID.push(id)
    zjid.push(id)
    let newid = id - Number(pickupInfo.bluePrintCount)
    subComponentID.push({
      ids:['A' + newid],
      attachTo:lsarr[i].ids.join()
    })
  }
  let viewer= viewers.value.GetViewer()
  zjid.forEach(id=>{
    let a=gp.getPBaseObjById(id)
    a && objs.push(a)
  })
  await viewer.dxfScene.addEntities(objs)
  measurementCategoryObj.value.exportDxf()
  const layerInfo = pickupInfo.exportDxfText
  viewer.Render()
  console.log(subComponentID,'subComponentID');
  
  return { subComponentID, layerInfo }
}

const cableMidHead=async(obj)=>{//电缆接头和端头
  if(!obj.arr.length) return
  function screenobj(arr,name){
    let obj=null
    arr.forEach(item=>{
      if(item.name===name) {
        obj=item
      }
    })
    return obj
  }
  let gp=viewers.value.GetViewer().transaction.gpObj
  let CablePointInfo=new sg.vector_Cable()
  const bluePrintCount = Number(pickupInfo.bluePrintCount)
  obj.arr.forEach(item=>{
    let attribute=screenobj(item.attributes,'型号规格')
    item.items.forEach(id=>{
      let newid
      if(id.toString().includes('A')) {
        newid=Number(id.slice(1)) + bluePrintCount
      }else {
        newid=Number(id)
      }
      let Cable=new sg.Cable(newid,attribute.value)
      CablePointInfo.push_back(Cable)
    })
    
  })
  let sgsubComponentIDs=null
  if(obj.type=='mid') {//接头
    sgsubComponentIDs= sg.addCablesJointComponent(dxfZsObj.drawingId,CablePointInfo,obj.length,'电缆接头')
  }else if(obj.type=='head') {//端头
    sgsubComponentIDs= sg.addCablesTipComponent(dxfZsObj.drawingId,CablePointInfo,'电缆端头')
  }



  const cableID = []
  let zjid=[]
  let objs=[]
  const size = sgsubComponentIDs.size()
  for (let i = 0; i < size; i++) {
    const obj= sgsubComponentIDs.get(i)
    let name=obj.getCableType()
    let ids=[]//
    let attachTo=obj.getCableId()- Number(pickupInfo.bluePrintCount)
    let wasmids=obj.getIds()
    let count=wasmids.size()
    for (let j = 0; j < count; j++) {
      let id=wasmids.get(j)

      // let  newobj=gp.getPBaseObjById(id)
      // let  PitCurve = sg.SGObjTool.convertToPoint(newobj)
      // let middlePoint={
      //   x:PitCurve.getPoint().x(),
      //   y:PitCurve.getPoint().y(),
      // }

      zjid.push(id)//图元id
      pickupInfo.selectedID.push(id)
      let newid = id - Number(pickupInfo.bluePrintCount)
      let lsid='A' + newid
      ids.push(lsid)
    }
    cableID.push({
      ids,
      name,
      attachTo:'A' + attachTo,
    })
  }
  
  
  let viewer= viewers.value.GetViewer()
  zjid.forEach(id=>{
    let a=gp.getPBaseObjById(id)
    a && objs.push(a)
  })
  await viewer.dxfScene.addEntities(objs)
  measurementCategoryObj.value.exportDxf()
  const layerInfo = pickupInfo.exportDxfText
  viewer.Render()
  return { cableID, layerInfo,type:obj.type }
}

const attach = () => {//专业绘图
  emits('attach')
}
const inherit = () => { //识图继承按钮
  if (!viewers.value.GetViewer().transaction.gpObj.count()) return
  emits('inherit')
}
const drawingInheritance = async (obj) => {//识图继承方法
  if (!obj.url) return
  sLcontrolObj.value.setState(SlScopeType.DrawingInheritance)
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  console.log(obj.url, 'obj.urlobj.urlobj.url');
  let fileContent = ''
  try {
    const dxfFile = await urlToFileDecomp(obj.url, obj.isZip || false)
    fileContent = await uploadFileAsText(dxfFile)
  } catch (error) {
    console.error('There has been a problem with your fetch operation:', error);
  } finally {
    loading?.close();
  }
  let newObj = menuBarMethodObj.value.drawingInheritance(obj, fileContent)
  return newObj
}


const drawingComparison=()=>{//图纸对比按钮
  if(!viewers.value.GetViewer().transaction.gpObj.count()) return
  emits('drawingComparison') 
}
const drawingComparisonFn=async (obj)=>{//图纸对比方法
  if(!obj.url) return
  let newurl=obj.url
  // let newurl=dynamicLoadByTimeStamp(obj.url,'&')
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  let fileContent=''
  try {
    const transaction = viewers.value.GetViewer().transaction
    const dxfFile = await urlToFileDecomp(newurl, obj.isZip || false)
    fileContent = await uploadFileAsText(dxfFile)
  } catch (error) {
    console.error('There has been a problem with your fetch operation:', error);
  } finally {
    loading?.close();
  }
  menuBarMethodObj.value.drawingComparisonFn(fileContent)
}
const drawingComparisonSwitch=()=>{//图纸对比结果开关
  refdxfpop.value.switchPop()
  refdxfpop.value.render(comparisonResultsGp)
}
const urlToFileDecomp = async (url, isZip) => {
  const fileResources = await LDxf.urlToDxfFile(url); // 将网络资源转换为文件资源
  let obj= isZip ? await LDxf.decompression(fileResources) : fileResources;
   return obj
}


const btnSelectGraphicElements=()=>{//图元识别选择图元
  sLcontrolObj.value.setState(SlScopeType.Graphicelementimg)
}
const btnIdentificationScope=(value)=>{//图元识别选择范围
  PubSub.default.pub("slidentifyclear");
  if(value ===SlScopeType.All) {
  }else {
    sLcontrolObj.value.setState(value)
  }
}
const distinguish=async()=>{//图元识别 识别
  console.log('distinguishxxxxxxxxxxxxxxxxxx');
  pickupInfo.pickupFalg=false
  await identifyImgObj.value.identifyTheImages()
  return pickupInfo.identifiedInfo
} 
const distinguishclear=()=>{//图元识别清除
  identifyImgObj.value.pickupResultClear()
  sLcontrolObj.value.setState(SlScopeType.All)
}

const graphicelementImg = () => { //提供图片给算量
  emits('cadgraphicelementImg',
    {
      pickupOBjImgurl: pickupInfo.pickupOBjImgurl,
    }
  )
}
const graphicelementAttribute=()=>{//获取对象属性
  let obj=identifyImgObj.value.graphicelementAttribute()
  emits('cadgraphicelementAttribute',obj)
}
const identifyAttribute=(obj)=>{//批量识别
  let arr=identifyImgObj.value.identifyAttribute(obj,measureObj.measureScaleText)
  sLcontrolObj.value.setState(SlScopeType.Empty)
  return arr
  
}
const cameraSwitching = (item) => {//搜索结果镜头切换
  viewers.value.GetViewer().grapicControl.showBoundingBox(item)
}
const deldistinguishXYlist = (id) => {//图元识别列表删除
  let arr = pickupInfo.identifiedInfo.filter(ite=> ite.id !== id)
  pickupInfo.identifiedInfo = [...arr]
  let arr2 = pickupInfo.identifiedIds.filter(ite=> ite.id !== id)
  pickupInfo.identifiedIds = [...arr2]
  PubSub.default.pub("slidentifyclear");
  identifyImgObj.value.idEchodisplay()
}

const exportAllDxf=()=>{
  measurementCategoryObj.value.exportDxf()
  return pickupInfo.exportDxfText
}
const drawcommand=(type)=>{//绘制 拾取（面积和长度）命令 文字提取
  pickupInfo.pickupIdlist=[]
  sLcontrolObj.value.setState(type)
}
const cadidmovecamera=(arr)=>{//绘制图元根据id移动镜头
  let viewer=viewers.value.GetViewer()
  idmovecamera(viewer,arr)
  viewer.Render()
}
const cadGetgcdPointList=()=>{ //获取高程点
  return pickupInfo.gcdPointList
}
const getIdsOridentifyScope=()=>{
  return {
    idlist:pickupInfo.pickupIdlist,//已选择图元
    identifyScope:pickupInfo.identifyScope,//范围
  }
}

onUnmounted(()=>{
  pickupInfo.exportDxfText=''
  pickupInfo.pickupIdlist=[]
  pickupInfo.selectedID=[]
  pickupInfo.customID=[]
  pickupInfo.gcdPointList=[]
  pickupInfo.isSLflag=false
  viewers.value=false
})




const ceshi333=()=>{
  // viewers.value.GetViewer().drawingSelected2()
  // identifyImgObj.value.idEchodisplay()

  let dxfbase=viewers.value.GetViewer().transaction.gpObj.getDxfBaseData()
  const layer = new sg.SGLayer()
  let laycount=dxfbase.layerCount()
  for(let i=0;i<laycount;i++){
    let lay=dxfbase.getLayer(i)
    lay.getName()
    console.log(lay.getName(),'oooooooooooooooooooo');
    
  }
}
const btnisshow=()=>{
  refMenuBar.value.btnceshishow()
}


defineExpose({
  btnisshow,//测试用
  btnDisplaydrawings,//打开图纸
  getCadData,//获取数据
  creanCadWasm,//清除wasm

  //测量
  openControlPanelPop,//比例尺
  AIdistinguish,//AI识别

  switchMenu,//切换菜单
  generateSubComponent,//支架

  drawingInheritance,//图纸继承
  drawingComparisonFn,//图纸对比

  againOpenOperated,//重新打开已选择的图元和自定义的图元
  drawingComparisonSwitch,//图纸对比结果开关
  delIDGraphicElement,//根据id删除图元

  distinguish,//图元识别 识别
  distinguishclear,//图元识别清除
  btnSelectGraphicElements,//图元识别选择图元
  btnIdentificationScope,//图元识别选择范围
  cameraSwitching,//搜索结果镜头切换
  deldistinguishXYlist,//图元识别列表删除

  exportAllDxf,//导出自定义图元字符串
  drawcommand,//绘制 拾取（面积和长度）命令
  cableMidHead,//电缆接头和端头
  cadidmovecamera,//绘制图元根据id移动镜头
  cadGetgcdPointList,//获取高程点

  identifyAttribute,//根据图元属性批量识别
  getIdsOridentifyScope,//已选择id和范围
})

</script>

<script>
export default {
  name: 'CadUI'
};
</script>

<style lang="scss" scoped>
.dxfview {
  position: relative;
  flex: auto;
  background-color: #fff;
  width: 100%;
  height: 100%;

  .btn {
    position: absolute;
    top: 100px;
    left: 5px;
    z-index: 999;

    button {
      height: 30px;
      line-height: 30px;
      padding: 0 5px;
    }
  }

  .Measure {
    position: absolute;
    top: 50px;
    left: 10px;
    z-index: 999;
  }
}

.herde {
  display: flex;
  position: relative;

  .sy {
    height: 30px;
    font-size: 12px;
    background-color: #f0f9eb;
    color: #67c23a;
    line-height: 30px;
    padding: 0 10px;
    margin-right: 20px;
  }

  .btn {
    position: absolute;
    top: 0;
    left: 50%;
    height: 30px !important;
    transform: translate(-50%);
  }
}

.pickupPop {
  width: 100vw;
  height: 100vh;
  background-color: rgba(128, 128, 128, 0.3);
  position: fixed;
  top: 0;
  left: 0;

  .list {
    width: 300px;
    height: 420px;
    background-color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    // overflow: hidden;
  }

}

table {
  width: 100%;
  border-collapse: collapse;
}

table thead,
table tbody tr {
  display: table;
  width: 100%;
}

table tbody {
  display: block;
  max-height: 340px;
  /* 设定一个最大高度，根据实际需求调整 */
  overflow-y: auto;
}

/* 可选：为了确保表头和表格内容对齐，你可以调整thead的宽度 */
table thead {
  width: calc(100% - 4px);
  /* 减去滚动条宽度或其他偏移 */
}

/* 可选：为了美观，添加一些其他样式 */
table,
th,
td {
  border: 1px solid black;
}

th,
td {
  padding: 8px;
  text-align: left;
}

.close {
  position: absolute;
  right: 10px;
  // top: -5px;
  top: 0;
  font-size: 20px;
  font-weight: 500;
  padding: 0px 8px 5px 8px;
  cursor: pointer;

  &:hover {
    background-color: #57aaff;
  }
}

.paging {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #eceff7;
  display: flex;
  justify-content: center;
  align-items: center;

  .left,
  .right {
    font-size: 12px;
    padding: 0 5px;
    cursor: pointer;

    &:hover {
      background-color: #57aaff;
    }
  }
}
</style>