import { emitter } from "../emitter";
import { CADEvents, GraphicType, screen2wcs } from "dxf-viewer/src/controls";
import { DrawState, DxfViewer, globalDxf,currentColor,dxfLinetypes } from "dxf-viewer";
import { Vector3 } from "three";


export class CadMessageReply {
    public tempGeometry:any[];//临时的绘制对象数组
    public viewer:DxfViewer  //视口
    public color:any=currentColor.value
    public lineType:any=dxfLinetypes.curLinetype.name
    public get cadCtx(){//cad上下文
        return this.viewer.transaction.gpObj
    }  
    
    public isPick : any // 设置是否可以pick
    public isSnap:any //设置是否可以捕捉
    public id:number=0
    constructor(viewer) {
        this.setupEvents()
        this.viewer = viewer
        this.isPick = true;
        this.isSnap = true;
        this.tempGeometry = []
    }
    //消息返回false表示可以往下路由，返回true表示在这个节点截止不再往下路由
    // 左键按下
    public onLButtonDown( event:PointerEvent ) : boolean{
        console.log("左键按下",this.id)
        return false;
    }
    //左键弹起
    public onLButtonUp( event:PointerEvent ) : boolean{
        console.log("左键弹起",this.id)
        return false;
    }
    //右键按下
    public onRButtonDown( event:PointerEvent ) : boolean{
        console.log("右键按下",this.id)
        return false;
    }
    //右键弹起
    public onRButtonUp( event:PointerEvent ) : boolean{
        console.log("右键弹起",this.id)
        return false;
    }
    //鼠标移动
    public onMouseMove( event:PointerEvent ) : boolean{
        // if(this.isSnap){
        //     this.viewer.snap.showMarker(event)
        // }
        return true;
    }
    //键盘按下
    public onKeyDown(event:KeyboardEvent) : boolean{
        console.log("键盘按下",this.id)
        return false;
    }
    //键盘弹起
    public onKeyUp(event:KeyboardEvent) : boolean{
        console.log("键盘弹起",this.id)
        return false;
    }
    //中键
    public onWheel(event:PointerEvent): boolean{
        console.log('中键',this.id)
        return false;
    }
    //获取鼠标当前坐标
    public getEventPoint(event:PointerEvent):Vector3{
        const worldPoint = screen2wcs(event,this.viewer.camera);  
        let p1 = worldPoint;
        if(this.isSnap){
            const snappoint = this.viewer.snap.showMarker(event)
            if(snappoint){
                p1 = snappoint
            }else{
                p1 = worldPoint
            }
        }

        return p1
    }
    public threePointToSgPoint(pt:THREE.Vector3){
        let res = new sg.Point2d(pt.x,pt.y)
        return res
    }
    //显示临时图形
    public showTempGeometry():void {
        this.tempGeometry.forEach((v)=>{
            this.viewer.scene.add(v);
        })
    }
    //清空临时对象
    public clearTempGeometry():void{
        this.tempGeometry.forEach((v)=>{
            this.viewer.scene.remove(v);
        })
        this.tempGeometry = []
    }
        //退出命令
    public onCancel():void{
        this.clearTempGeometry()
        this.dispose()
        this.viewer.snap.hideAllMarker()
        globalDxf.drawstate=DrawState.Pick
        globalDxf.operation=GraphicType.None
    }

    public setupEvents(){
        this.dispose()
        emitter.on(CADEvents.LButtonDown,this.onLButtonDown.bind(this))
        emitter.on(CADEvents.LButtonUp,this.onLButtonUp.bind(this))
        emitter.on(CADEvents.RButtonDown,this.onRButtonDown.bind(this))
        emitter.on(CADEvents.RButtonUp,this.onRButtonUp.bind(this))
        emitter.on(CADEvents.Wheel,this.onWheel.bind(this))
        emitter.on(CADEvents.KeyDown,this.onKeyDown.bind(this))
        emitter.on(CADEvents.KeyUp,this.onKeyUp.bind(this))
        emitter.on(CADEvents.MouseMove,this.onMouseMove.bind(this))
    }
    public dispose(){
        emitter.off(CADEvents.LButtonDown)
        emitter.off(CADEvents.LButtonUp)
        emitter.off(CADEvents.RButtonDown)
        emitter.off(CADEvents.RButtonUp)
        emitter.off(CADEvents.Wheel)
        emitter.off(CADEvents.KeyDown)
        emitter.off(CADEvents.KeyUp)
        emitter.off(CADEvents.MouseMove)
    }
}