import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial,EllipseCurve, Vector3,Line as ThLine } from "three"

export class Ellipse extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        console.log(this.color,'this.color');
        
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)

        this.init()
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        let p1=await this.lengthPick({
            tip:'指定第二个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p1)
        let p2=await this.lengthPick({
            tip:'指定第三个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p2)
        this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }else if(this.pnts.length==1){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }else if(this.pnts.length==2){
            let secpt = this.getEventPoint(event)
            this.drawMoveArc(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
    }
    public drawMoveArc(v:Vector3){
        var points = this.getvertices([...this.pnts,v]);
        this.mesh.geometry=new BufferGeometry().setFromPoints(points);
    }
    
    complete(){
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        if(this.pnts.length<3) return
        let {x,y}=globalDxf.wcsOff
        
        const r1=  Math.sqrt((this.pnts[1].x-this.pnts[0].x) ** 2 + (this.pnts[1].y-this.pnts[0].y) ** 2 )
        const r2=  Math.sqrt((this.pnts[2].x-this.pnts[0].x) ** 2 + (this.pnts[2].y-this.pnts[0].y) ** 2 )

        console.log(this.pnts,'this.pnts');
        let wp= new sg.Point2d(this.pnts[0].x+x, this.pnts[0].y+y)
        const v=new sg.Vector2d(
            this.pnts[2].x-this.pnts[0].x,
            this.pnts[2].y-this.pnts[0].y
        )
        console.log(v.x(),v.y(),'xxxxxxxxxxxxxxxxxxxxx');
        let l1=r1>r2?r1:r2
        let l2=l1===r1?r2:r1
        let gparc=new sg.Ellipse2d(wp,v,l1,l2)
        let els:any=new sg.SGObjCurve()
        let [r,g,b]=this.color
        els.setColor(new sg.SGColor(r,g,b,255))
        els.setLineType(this.lineType)
        els.setpCurve(gparc)
        this.viewer.transaction.add(els)
        console.log(this.viewer.transaction.gpObj.count(),'ppppppppp');
        
        globalDxf.operation=GraphicType.None
        globalDxf.isdraw = false
        this.isSnap=false
        setTimeout(()=>{
            globalDxf.drawstate=DrawState.Pick
        })
    }
    private setWasmVPlist(vList){
        let {x,y}=globalDxf.wcsOff
        const wasmVPlist:any=[]
        vList.forEach(item=>{
            wasmVPlist.push(new sg.Point2d(item.x+x, item.y+y))
        })
        return wasmVPlist

    }
    private getvertices(vList:any) {//2 * Math.PI
        // const  aRotation =2 * Math.PI-Math.atan2(vList[1].y-vList[0].y,vList[1].x-vList[0].x)
        const aRotation = Math.atan2(vList[2].y - vList[0].y, vList[2].x - vList[0].x)+ Math.PI;
        const r1=  Math.sqrt((vList[1].x-vList[0].x) ** 2 + (vList[1].y-vList[0].y) ** 2 )
        const r2=  Math.sqrt((vList[2].x-vList[0].x) ** 2 + (vList[2].y-vList[0].y) ** 2 )
        let l1=r1>r2?r1:r2
        let l2=l1===r1?r2:r1
    
        var curve = new EllipseCurve(vList[0].x, vList[0].y, 
                                            l1,l2,0, 2 * Math.PI,
                                            false,aRotation);
        
        let num=100*(1+r2)                                    
        var points = curve.getPoints(num);
        if (points[0] !== points[points.length - 1]) {
            points.push(points[0]); // 确保闭合
        }
        return points
    }
}