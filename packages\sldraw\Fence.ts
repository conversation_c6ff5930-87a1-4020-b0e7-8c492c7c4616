import { DrawHelp } from "../dxfdraw/drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState,pickupInfo,SlScopeType,newAddLayer } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial,Group, Vector3,Line as ThLine } from "three"
import { PubSub } from 'emitter';
import { Snap } from "dxf-viewer/src/scene/Snap"
import { getInterpNum,getSgBufInf } from "dxf-viewer/src/scene"

export class Fence extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    public Fence:THREE.Group = new Group()//临时对象组
    public color:string='#EC808D'
    public els:any=[]
    public fencelist:any=[]
    public draw:boolean=true

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        const material = new LineBasicMaterial({ color:this.color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        // this.Fence.add(this.mesh)
        // this.viewer.transientSceneWasm.scene.add(this.Fence)
        
        this.viewer.transientSceneWasm.scene.add(this.mesh)

        PubSub.default.sub("slfencedraw", this.contro.bind(this));
        PubSub.default.sub("slidentifyclear", this.clear.bind(this));
        // pickupInfo.pickupIdlist=[]
        this.init()
        Snap.turnOnSnap()
    }
    public async init(){
        
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        while (this.draw) {
            let p1=await this.lengthPick({
                tip:'指定下一个点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
        }
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    public override onRButtonDown( event:PointerEvent ) : boolean{
        if(pickupInfo.slcurrentstate==SlScopeType.Draw || pickupInfo.slcurrentstate==SlScopeType.ThreeDDraw) {
            if(this.pnts.length<2){
                this.clear()
                return true
            } 
        }else if(pickupInfo.slcurrentstate==SlScopeType.AreaDraw) {
            if(this.pnts.length<3){
                this.clear()
                return true
            }
            this.pnts=[...this.pnts,this.pnts[0]]
        }else {
            this.clear()
            return true
        }
        this.generatePolyline()

        
        return true
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }
    public override onKeyUp(event: KeyboardEvent): boolean {
        if (event.key === 'Escape') {
            this.clear()
            this.viewer.Render()                      
        }
        return true
    }

    private generatePolyline(){
        this.mesh.geometry.setFromPoints(this.pnts);
        const wasmVPlist=this.setWasmVPlist(this.pnts)
        let wasmBulge = new sg.vector_double()
        const pl=new sg.PolyLine2d(wasmVPlist, wasmBulge, false)
        
        const gp=new sg.SGObjComBinCurve()
        newAddLayer(this.viewer.transaction.gpObj,pickupInfo.structureTypeName)
        gp.setpCurve(pl)
        gp.setLayer(pickupInfo.structureTypeName)
        gp.setColor(new sg.SGColor(236 ,128 ,141,255))
        
        const id = gp.getDrawObjectId()
        if(pickupInfo.slcurrentstate==SlScopeType.Draw) {
            const length=pl.length()
            pickupInfo.fenceDrawlist.push({
                id,
                length,
            })
        }else if(pickupInfo.slcurrentstate==SlScopeType.AreaDraw) {
            let iszj=pl.isSelfIntersect()//是否自交
            if(iszj) return this.clear()
            const area=pl.area()
            pickupInfo.fenceDrawlist.push({
                id,
                area:area,
            })
        }else if(pickupInfo.slcurrentstate==SlScopeType.ThreeDDraw) {
            gp.getRenderPoints(100)
            let {vertices} = getSgBufInf()
            let extremepoint:any = [];
            for (let i = 0; i < vertices.length; i += 2) {
                let x = vertices[i];
                let y = vertices[i + 1];
                extremepoint.push({x: x, y: y});
            }
            pickupInfo.fenceDrawlist.push({
                id,
                extremepoint,
            })
        }
        // pickupInfo.pickupIdlist=pickupInfo.fenceDrawlist.map(item=>item.id)

        this.els.push(gp)
        this.contro()
        
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
    }
    
    public contro(){
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        this.dispose()
        if(!this.els.length) return
        this.viewer.transaction.add(this.els)
        this.pnts=[]
        this.els=[]
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
        Snap.turnOffSnap()
    }
    public clear(){
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        this.pnts=[]
        poptip.dispose()
        this.dispose()
        pickupInfo.fenceDrawlist=[]
        // pickupInfo.pickupIdlist=[]
        this.els=[]
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
        Snap.turnOffSnap()
    }
    private setWasmVPlist(vList){
        let {x,y}=globalDxf.wcsOff
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x+x, item.y+y))
        })
        return wasmVPlist
    }
    private getUniformPointsOnSegments(points, N) {
        let allPoints:any = [];
        function getUniformPointsOnLine(P0, P1, N) {
            let segmentPoints:any = [];
            let deltaX = (P1.x - P0.x) / N;
            let deltaY = (P1.y - P0.y) / N;
            for (let i = 0; i <= N; i++) {
                let x = P0.x + i * deltaX;
                let y = P0.y + i * deltaY;
                segmentPoints.push({x: x, y: y});
            }
            return segmentPoints;
        }
        for (let i = 0; i < points.length - 1; i++) {
            let segmentPoints = getUniformPointsOnLine(points[i], points[i + 1], N);
            if (i < points.length - 2) {
                allPoints.push(...segmentPoints.slice(0, -1)); 
            } else {
                allPoints.push(...segmentPoints);
            }
        }
        return allPoints;
    }
}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       



