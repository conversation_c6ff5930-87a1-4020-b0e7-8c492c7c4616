import { Matrix3, Vector2 } from "three"
import { HAlign, MTextAlign, MTextEntityType, TextBoxParagraphAlign, VAlign } from "./constant"
import { TextBoxParagraph } from "./textBoxParagraph"


/** Encapsulates layout calculations for a multiline-line text block. */
export class TextBox {
    /**
     * @param fontSize
     * @param {Function<CharShape, String>} charShapeProvider
     */
    public fontSize
    public charShapeProvider
    public curParagraph
    public paragraphs
    public spaceShape
    constructor(fontSize, charShapeProvider) {
        this.fontSize = fontSize
        this.charShapeProvider = charShapeProvider
        this.curParagraph = new TextBoxParagraph(this)
        this.paragraphs = [this.curParagraph]
        this.spaceShape = charShapeProvider(" ")
    }

    /** Add some formatted text to the box.
     * @param {MTextFormatEntity[]} formattedText Parsed formatted text.
     */
    FeedText(formattedText) {
        /* For now advanced formatting is not implemented so scopes are just flattened. */
        function *FlattenItems(items) {
            for (const item of items) {
                if (item.type === MTextEntityType.SCOPE) {
                    yield *FlattenItems(item.content)
                } else {
                    yield item
                }
            }
        }

        /* Null is default alignment which depends on attachment point. */
        let curAlignment = null

        for (const item of FlattenItems(formattedText)) {
            switch(item.type) {

            case MTextEntityType.TEXT:
                for (const c of item.content) {
                    if (c === " ") {
                        this.curParagraph.FeedSpace()
                    } else {
                        this.curParagraph.FeedChar(c)
                    }
                }
                break

            case MTextEntityType.PARAGRAPH:
                this.curParagraph = new TextBoxParagraph(this)
                this.curParagraph.SetAlignment(curAlignment)
                this.paragraphs.push(this.curParagraph)
                break

            case MTextEntityType.NON_BREAKING_SPACE:
                this.curParagraph.FeedChar(" ")
                break

            case MTextEntityType.PARAGRAPH_ALIGNMENT:
                let a:any = null
                switch (item.alignment) {
                case "l":
                    a = TextBoxParagraphAlign.LEFT
                    break
                case "c":
                    a = TextBoxParagraphAlign.CENTER
                    break
                case "r":
                    a = TextBoxParagraphAlign.RIGHT
                    break
                case "d":
                    a = TextBoxParagraphAlign.JUSTIFY
                    break
                case "j":
                    a = null
                    break
                }
                this.curParagraph.SetAlignment(a)
                curAlignment = a
                break
            }
        }
    }

    *Render(position, width, rotation, direction, attachment, lineSpacing, color, layer) {
        for (const p of this.paragraphs) {
            p.BuildLines(width)
        }
        if (width === null || width === 0) {
            /* Find maximal paragraph width which will define overall box width. */
            width = 0
            for (const p of this.paragraphs) {
                const pWidth = p.GetMaxLineWidth()
                if (pWidth > width) {
                    width = pWidth
                }
            }
        }

        let defaultAlignment = TextBoxParagraphAlign.LEFT
        switch (attachment) {
        case MTextAlign.TOP_CENTER:
        case MTextAlign.MIDDLE_CENTER:
        case MTextAlign.BOTTOM_CENTER:
            defaultAlignment = TextBoxParagraphAlign.CENTER
            break
        case MTextAlign.TOP_RIGHT:
        case MTextAlign.MIDDLE_RIGHT:
        case MTextAlign.BOTTOM_RIGHT:
            defaultAlignment = TextBoxParagraphAlign.RIGHT
            break
        }

        for (const p of this.paragraphs) {
            p.ApplyAlignment(width, defaultAlignment)
        }

        /* Box local coordinates have top-left corner origin, so Y values are negative. The
         * specified attachment should be used to obtain attachment point offset relatively to box
         * CS origin.
         */

        if (direction !== null) {
            /* Direction takes precedence over rotation if specified. */
            rotation = Math.atan2(direction.y, direction.x) * 180 / Math.PI
        }

        const lineHeight = lineSpacing * 5 * this.fontSize / 3

        let height = 0
        for (const p of this.paragraphs) {
            if (p.lines === null) {
                /* Paragraph always occupies at least one line. */
                height++
            } else {
                height += p.lines.length
            }
        }
        height *= lineHeight

        let origin = new Vector2()
        switch (attachment) {
        case MTextAlign.TOP_LEFT:
            break
        case MTextAlign.TOP_CENTER:
            origin.x = width / 2
            break
        case MTextAlign.TOP_RIGHT:
            origin.x = width
            break
        case MTextAlign.MIDDLE_LEFT:
            origin.y = -height / 2
            break
        case MTextAlign.MIDDLE_CENTER:
            origin.x = width / 2
            origin.y = -height / 2
            break
        case MTextAlign.MIDDLE_RIGHT:
            origin.x = width
            origin.y = -height / 2
            break
        case MTextAlign.BOTTOM_LEFT:
            origin.y = -height
            break
        case MTextAlign.BOTTOM_CENTER:
            origin.x = width / 2
            origin.y = -height
            break
        case MTextAlign.BOTTOM_RIGHT:
            origin.x = width
            origin.y = -height
            break
        default:
            throw new Error("Unhandled alignment")
        }

        /* Transform for each chunk insertion point. */
        const transform = new Matrix3().translate(-origin.x, -origin.y)
            .rotate(-rotation * Math.PI / 180).translate(position.x, position.y)

        let y = -this.fontSize
        for (const p of this.paragraphs) {
            if (p.lines === null) {
                y -= lineHeight
                continue
            }
            for (const line of p.lines) {
                for (let chunkIdx = line.startChunkIdx;
                     chunkIdx < line.startChunkIdx + line.numChunks;
                     chunkIdx++) {

                    const chunk = p.chunks[chunkIdx]
                    let x = chunk.position
                    /* First chunk of continuation line never prepended by whitespace. */
                    if (chunkIdx === 0 || chunkIdx !== line.startChunkIdx) {
                        x += chunk.GetSpacingWidth()
                    }
                    const v = new Vector2(x, y)
                    v.applyMatrix3(transform)
                    if (chunk.block) {
                        yield* chunk.block.Render(v, null, rotation, null,
                                                  HAlign.LEFT, VAlign.BASELINE,
                                                  color, layer)
                    }
                }
                y -= lineHeight
            }
        }
    }
}