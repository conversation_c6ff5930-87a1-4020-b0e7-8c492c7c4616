import axios from "axios";
import { ElMessage } from "element-plus";

// 获取API基础URL
let VITE_BASE_API_URL = import.meta.env.VITE_BASE_API_URL;
let VITE_BASE_MAP_BOX_URL = import.meta.env.VITE_BASE_MAP_BOX_URL;
let VITE_STANDARD_DATA_BASE_URL = import.meta.env.VITE_STANDARD_DATA_BASE_URL;
let VITE_UPLOAD_URL = import.meta.env.VITE_UPLOAD_URL;
let VITE_DIGITAL_COMPONENT_BASE_URL = import.meta.env.VITE_DIGITAL_COMPONENT_BASE_URL;
let VITE_MAP_BOX_UPLOAD_URL = import.meta.env.VITE_MAP_BOX_UPLOAD_URL;
let VITE_ISOLAR_BUILD_URL = import.meta.env.VITE_ISOLAR_BUILD_URL;
// 导出API URL供其他模块使用
export const API_URLS = {
  BASE_URL: VITE_BASE_API_URL,
  UPLOAD_URL: VITE_MAP_BOX_UPLOAD_URL,
  VITE_BASE_MAP_BOX_URL,
  VITE_STANDARD_DATA_BASE_URL,
  VITE_UPLOAD_URL,
  VITE_DIGITAL_COMPONENT_BASE_URL,
  VITE_MAP_BOX_UPLOAD_URL,
  VITE_ISOLAR_BUILD_URL
};

// 创建axios实例
const instance = axios.create({
  baseURL: VITE_MAP_BOX_UPLOAD_URL,
  timeout: 600000, // 设置较长的超时时间，适合文件上传
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 在请求发送前处理请求配置
    const token = localStorage.getItem("token");
    if (token) {
      // 如果存在token，将其添加到请求头
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // 请求错误处理
    console.error("请求错误：", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 对响应数据进行处理
    const res = response.data;

    // 如果是文件下载等二进制流，直接返回响应
    if (
      response.headers["content-type"]?.includes("application/octet-stream")
    ) {
      return response;
    }

    // 假设后端返回的数据格式为 { code: number, data: any, message: string }
    if (res.code === undefined || res.code === 0 || res.code === 200) {
      // 成功响应
      return res; // 返回完整响应，包括code、data和message
    }

    // 处理特定错误码
    if (res.code === 401) {
      // 未登录或Token过期
      ElMessage.error("登录已过期，请重新登录");
      // 可以在这里进行登录处理
    } else {
      // 其他错误
      ElMessage.error(res.message || "请求失败");
    }

    return Promise.reject(new Error(res.message || "请求失败"));
  },
  (error) => {
    // 处理HTTP错误
    if (error.response) {
      switch (error.response.status) {
        case 400:
          ElMessage.error("请求错误");
          break;
        case 401:
          ElMessage.error("未授权，请重新登录");
          break;
        case 403:
          ElMessage.error("拒绝访问");
          break;
        case 404:
          ElMessage.error("请求资源不存在");
          break;
        case 500:
          ElMessage.error("服务器内部错误");
          break;
        default:
          ElMessage.error(`连接错误 ${error.response.status}`);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      if (error.message.includes("timeout")) {
        ElMessage.error("请求超时！请检查网络连接");
      } else {
        ElMessage.error("网络异常，未收到响应");
      }
    } else {
      // 请求设置有误
      ElMessage.error("请求设置有误，请联系管理员");
    }

    return Promise.reject(error);
  }
);

export default instance;
