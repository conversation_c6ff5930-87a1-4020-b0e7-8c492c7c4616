import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Line, LineDashedMaterial, Vector3 } from "three";
import { <PERSON>x<PERSON><PERSON><PERSON><PERSON> } from "dxf-viewer/src/viewer";
import { PubSub } from 'emitter';
import { globalDxf, } from "dxf-viewer/src"
export class ItemBounding<PERSON>ox extends Line{
    public viewer: DxfViewer 
    constructor(viewer){
        super()
        this.viewer=viewer
		PubSub.default.sub("slidentifyclear", this.del.bind(this));
    }
    public zoomto(obj){
		let {x,y}=globalDxf.wcsOff
        const { leftTopPoint, width, heigth } = obj

		this.viewer.FitView(leftTopPoint.x,leftTopPoint.x + width,leftTopPoint.y- heigth,leftTopPoint.y)
		console.log(this.viewer.camera.position.x,'this.viewer.camera.position.x');
		this.viewer.controls.target = new Vector3(this.viewer.camera.position.x, this.viewer.camera.position.y, 0)
		this.viewer.controls.update()
		this.viewer.camera.zoom = 0.1
		this.viewer.camera.updateProjectionMatrix()
		
		let x1=leftTopPoint.x-x
		let y1=leftTopPoint.y-y
		let pointA = new Vector3(x1-width*0.1, y1+heigth*0.1, 0)
		let pointB = new Vector3(x1+width*0.1 + width, y1+heigth*0.1, 0)
		let pointC = new Vector3(x1+width*0.1 + width, y1-heigth*0.1 - heigth, 0)
		let pointD = new Vector3(x1-width*0.1, y1-heigth*0.1 - heigth, 0)
		const vertices = [pointA, pointB, pointC, pointD, pointA];
		this.geometry = new BufferGeometry().setFromPoints(vertices);
		this.material = new LineDashedMaterial({ color: 0xffffff, dashSize: width/20, gapSize: width/20 })
		this.computeLineDistances();
		this.show()
    }
	public show(){
		this.viewer.scene.add(this)
		this.viewer.Render()
	}
	public del(){
		this.viewer.scene.remove(this)
		this.viewer.Render()
	}

}