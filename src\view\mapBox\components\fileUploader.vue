<template>
  <div class="file-uploader">
    <div v-if="!fileSelected">
      <el-upload
        class="upload-demo"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :limit="1"
        action=""
      >
        <template #trigger>
          <el-button type="primary" class="upload-button">{{
            buttonText
          }}</el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip">
            {{
              fileType === "cad" ? "CAD图纸(*.dwg, *.dxf)" : "TIF图像(*.tif)"
            }}
          </div>
        </template>
      </el-upload>
    </div>

    <div v-else class="file-info">
      <div class="file-details">
        <span>文件名称: {{ fileInfo.fileName }}</span>
        <span>文件大小: {{ formatFileSize(fileInfo.fileSize) }}</span>
        <span v-if="fileType === 'tif'"
          >推荐文件分片数量: {{ fileInfo.chunkCount }}</span
        >
        <span v-if="fileType === 'tif'"
          >推荐文件分片大小: {{ formatFileSize(chunkSize) }}</span
        >
      </div>

      <!-- TIF大文件上传进度 -->
      <div
        class="upload-progress"
        v-if="fileType === 'tif' && uploadStatus !== 'success'"
      >
        <el-progress
          v-if="isUploading"
          :percentage="uploadProgress"
          :format="progressFormat"
        />
        <div class="upload-actions">
          <el-button
            type="primary"
            @click="startUploadTIF"
            :disabled="isUploading"
          >
            {{ uploadStatus === "process" ? "继续上传" : "开始上传" }}
          </el-button>
          <el-button @click="cancelUpload" :disabled="isUploading"
            >取消</el-button
          >
        </div>
      </div>

      <!-- CAD文件上传进度 -->
      <div
        class="upload-progress"
        v-if="fileType === 'cad' && uploadStatus !== 'success'"
      >
        <el-progress
          v-if="isUploading"
          :percentage="uploadProgress"
          :format="progressFormat"
        />
        <div class="upload-actions">
          <el-button
            type="primary"
            @click="uploadCadFile"
            :disabled="isUploading"
          >
            {{ uploadStatus === "process" ? "继续上传" : "开始上传" }}
          </el-button>
          <el-button @click="cancelUpload" :disabled="isUploading"
            >取消</el-button
          >
        </div>
      </div>

      <div
        v-if="!isUploading && uploadStatus === 'success'"
        class="upload-success"
      >
        <el-icon class="upload-success-icon"><Check /></el-icon>
        <span>上传成功</span>
        <el-button link @click="resetUpload">重新选择</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import { Check } from "@element-plus/icons-vue";
import SparkMD5 from "spark-md5";
import {
  initUpload,
  uploadChunk,
  getUploadStatus,
  cancelUpload as cancelUploadApi,
  StatusResponse,
  InitResponse,
} from "../api/uploadService";
import { LoadDxf } from "../utils/mapBoxFileTools";
import { mitts, btnFnObj, mittEvents } from "sgdraw/mittBus";
/**
 * 上传结果接口
 */
interface UploadResult {
  status: string;
  fileCode: string;
  filePath?: string;
  path?: string;
  chunkIndex?: number;
  projectCode: string;
  fileHash: string;
}

// 定义接口和类型
interface FileInfo {
  file: File | null;
  fileName: string;
  fileSize: number;
  fileHash: string;
  fileType: string;
  chunkCount: number;
}

interface ChunkData {
  index: number;
  data: Blob;
  size: number;
}

const props = defineProps({
  buttonText: {
    type: String,
    default: "选择文件",
  },
  fileType: {
    type: String, // 'cad' 或 'tif'
    required: true,
  },
  projectCode: {
    type: String,
    required: true,
  },
});

// 文件相关状态
const fileSelected = ref<boolean>(false);
const fileInfo = ref<FileInfo>({
  file: null,
  fileName: "",
  fileSize: 0,
  fileHash: "",
  fileType: "",
  chunkCount: 0,
});

// 上传相关状态
const uploadStatus = ref<string>("init"); // 'init', 'process', 'success'
const isUploading = ref<boolean>(false);
const uploadProgress = ref<number>(0);
const chunkSize = ref<number>(2 * 1024 * 1024); // 默认2MB分片大小
const currentChunkIndex = ref<number>(0);
const chunks = ref<ChunkData[]>([]);
const statusCheckTimer = ref<number | null>(null);

// 根据文件大小自动设置分片大小
const calculateOptimalChunkSize = (fileSize: number): number => {
  // 小于5MB的文件，使用1MB分片
  if (fileSize < 5 * 1024 * 1024) {
    return 1 * 1024 * 1024; // 1MB
  }
  // 5MB-20MB的文件，使用2MB分片
  else if (fileSize < 20 * 1024 * 1024) {
    return 2 * 1024 * 1024; // 2MB
  }
  // 20MB-50MB的文件，使用5MB分片
  else if (fileSize < 50 * 1024 * 1024) {
    return 5 * 1024 * 1024; // 5MB
  }
  // 50MB-200MB的文件，使用10MB分片
  else if (fileSize < 200 * 1024 * 1024) {
    return 10 * 1024 * 1024; // 10MB
  }
  // 200MB-500MB的文件，使用20MB分片
  else if (fileSize < 500 * 1024 * 1024) {
    return 20 * 1024 * 1024; // 20MB
  }
  // 大于500MB的文件，使用30MB分片
  else {
    return 30 * 1024 * 1024; // 30MB
  }
};

// 计算进度文本
const progressFormat = (percentage: number): string => {
  return percentage === 100 ? "上传完成" : `${percentage}%`;
};

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + " B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + " KB";
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + " MB";
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + " GB";
  }
};

// 文件变更处理
const handleFileChange = async (file: any) => {
  if (!file) return;

  let allowedExtensions: string[] = [];
  if (props.fileType === "cad") {
    allowedExtensions = ["dwg", "dxf"];
  } else if (props.fileType === "tif") {
    allowedExtensions = ["tif"];
  }

  // 改进的扩展名检测方法
  const fileName = file.name || "";
  // 获取最后一个点后面的内容作为扩展名
  const extension =
    fileName.lastIndexOf(".") !== -1
      ? fileName.slice(fileName.lastIndexOf(".") + 1).toLowerCase()
      : "";

  console.log("文件名:", fileName, "扩展名:", extension);

  if (!extension || !allowedExtensions.includes(extension)) {
    ElMessage.error(`只允许上传 ${allowedExtensions.join(", ")} 格式的文件`);
    return;
  }

  fileSelected.value = true;
  fileInfo.value.file = file.raw;
  fileInfo.value.fileName = file.name;
  fileInfo.value.fileSize = file.size;
  fileInfo.value.fileType = props.fileType;

  // 如果是TIF文件，计算文件哈希和分片
  if (props.fileType === "tif") {
    // 根据文件大小动态设置分片大小
    chunkSize.value = calculateOptimalChunkSize(file.size);

    // 计算文件哈希 - 确保计算完成后再进行下一步
    await calculateFileHash(file.raw);

    if (!fileInfo.value.fileHash) {
      ElMessage.error("文件哈希计算失败，请重试");
      fileSelected.value = false;
      return;
    }

    // 计算分片数量
    const count = Math.ceil(file.size / chunkSize.value);
    fileInfo.value.chunkCount = count;

    // 生成文件分片
    generateChunks();
  }
};

// 计算文件哈希 (仅TIF使用)
const calculateFileHash = async (file: File): Promise<string> => {
  try {
    return new Promise<string>((resolve) => {
      // 如果文件太大，只取前面一部分和后面一部分计算哈希
      // 这样可以加快哈希计算速度，同时保证唯一性
      const chunks: Blob[] = [];
      const hashChunkSize = 2 * 1024 * 1024; // 哈希计算用2MB分片
      const totalChunks = Math.ceil(file.size / hashChunkSize);

      // 如果文件大于100MB，只取前10个分片和后5个分片
      if (file.size > 100 * 1024 * 1024) {
        // 取前10个分片
        for (let i = 0; i < 10; i++) {
          if (i >= totalChunks) break;
          const start = i * hashChunkSize;
          const end = Math.min(start + hashChunkSize, file.size);
          chunks.push(file.slice(start, end));
        }

        // 取后5个分片
        for (let i = Math.max(10, totalChunks - 5); i < totalChunks; i++) {
          const start = i * hashChunkSize;
          const end = Math.min(start + hashChunkSize, file.size);
          chunks.push(file.slice(start, end));
        }
      } else {
        // 文件较小，读取全部内容
        for (let i = 0; i < totalChunks; i++) {
          const start = i * hashChunkSize;
          const end = Math.min(start + hashChunkSize, file.size);
          chunks.push(file.slice(start, end));
        }
      }

      // 加入文件名、大小等信息增强唯一性
      const fileInfoStr = `${file.name}-${file.size}-${file.lastModified}`;
      const infoChunk = new Blob([fileInfoStr]);
      chunks.unshift(infoChunk);

      const spark = new SparkMD5.ArrayBuffer();
      let currentChunk = 0;

      // 递归处理每个分片
      const processNextChunk = () => {
        if (currentChunk >= chunks.length) {
          // 所有分片处理完毕
          const hash = spark.end();
          fileInfo.value.fileHash = hash;
          resolve(hash);
          return;
        }

        const reader = new FileReader();
        reader.onload = (e: Event) => {
          const target = e.target as FileReader;
          if (target && target.result) {
            spark.append(target.result as ArrayBuffer);
            currentChunk++;
            // 继续处理下一个分片
            processNextChunk();
          } else {
            // 如果读取失败，使用默认哈希
            const fallbackHash = generateFallbackHash(file);
            fileInfo.value.fileHash = fallbackHash;
            console.warn("部分分片读取失败，使用备用哈希:", fallbackHash);
            resolve(fallbackHash);
          }
        };

        reader.onerror = () => {
          // 读取错误，使用备用哈希
          const fallbackHash = generateFallbackHash(file);
          fileInfo.value.fileHash = fallbackHash;
          console.warn("读取分片出错，使用备用哈希:", fallbackHash);
          resolve(fallbackHash);
        };

        reader.readAsArrayBuffer(chunks[currentChunk]);
      };

      // 开始处理分片
      processNextChunk();
    });
  } catch (error) {
    console.error("计算哈希错误:", error);
    // 出现错误时使用备用哈希
    const fallbackHash = generateFallbackHash(file);
    fileInfo.value.fileHash = fallbackHash;
    console.warn("哈希计算过程出错，使用备用哈希:", fallbackHash);
    return fallbackHash;
  }
};

// 生成备用哈希，确保哈希永远不为空
const generateFallbackHash = (file: File): string => {
  // 使用文件名、大小、修改时间和随机数生成备用哈希
  const randomPart = Math.random().toString(36).substring(2, 15);
  const timePart = new Date().getTime().toString(36);
  const filePart = `${file.name}-${file.size}-${file.lastModified}`.replace(
    /\W/g,
    ""
  );
  return `${filePart}-${timePart}-${randomPart}`;
};

// 生成文件分片 (仅TIF使用)
const generateChunks = () => {
  chunks.value = [];
  const file = fileInfo.value.file;
  if (!file) return;

  const count = fileInfo.value.chunkCount;

  for (let i = 0; i < count; i++) {
    const start = i * chunkSize.value;
    const end = Math.min(file.size, start + chunkSize.value);
    const chunk = file.slice(start, end);
    chunks.value.push({
      index: i,
      data: chunk,
      size: chunk.size,
    });
  }
};

// 开始上传 (TIF大文件上传)
const startUploadTIF = async () => {
  if (!fileInfo.value.file) return;

  isUploading.value = true;

  try {
    // 1. 调用文件初始化接口
    const initResult = await initFileUpload();
    if (!initResult) {
      throw new Error("初始化失败");
    }
    uploadStatus.value = initResult.status;

    // 2. 根据初始化结果处理
    if (initResult.status === "success") {
      // 文件已上传过
      uploadProgress.value = 100;
      if (initResult.projectCode && initResult.fileCode) {
        // 开始检查上传状态
        await startStatusCheck(initResult);

        await handleUploadSuccessTif(initResult.fileCode, initResult.filePath!);
      } else {
        //TODO: 文件已上传过，但初始化失败
      }
    } else if (
      initResult.status === "init" ||
      initResult.status === "process"
    ) {
      // 新文件或续传
      if (initResult.status === "process") {
        // 断点续传，获取已上传的分片索引
        currentChunkIndex.value = initResult.chunkIndex || 0;
        uploadProgress.value = Math.floor(
          (currentChunkIndex.value / fileInfo.value.chunkCount) * 100
        );
      }

      // 开始上传分片
      await uploadChunks(initResult);

      if (initResult.projectCode && initResult.fileCode) {
        // 开始检查上传状态
        await startStatusCheck(initResult);
      }
    }
  } catch (error) {
    ElMessage.error("上传初始化失败，请重试");
    isUploading.value = false;
  }
};

// 修改CAD文件上传方法，处理dwg文件的转换
const uploadCadFile = async () => {
  if (!fileInfo.value.file) return;

  isUploading.value = true;
  uploadProgress.value = 0;
  uploadStatus.value = "process";

  try {
    // 创建LoadDxf实例并转换文件
    const dxfLoader = new LoadDxf();
    const conversionResult = await dxfLoader.uploadDxfOrConvertDwgToDxf(
      fileInfo.value.file,
      (progress) => {
        // 更新上传进度
        uploadProgress.value = progress;
      }
    );

    if (!conversionResult.success || !conversionResult.file) {
      throw new Error(conversionResult.message || "文件处理失败");
    }

    fileInfo.value.fileName = conversionResult.file.name;

    // 处理上传成功，获取storageFileId
    if (conversionResult.storageFileId) {
      uploadStatus.value = "success";
      uploadProgress.value = 100;
      handleUploadSuccessCAD(conversionResult.storageFileId);
    } else {
      throw new Error("未获取到文件ID");
    }
  } catch (error) {
    ElMessage.error(
      error instanceof Error ? error.message : "CAD文件上传失败，请重试"
    );
    uploadStatus.value = "init";
  } finally {
    isUploading.value = false;
  }
};

// 初始化文件上传 (TIF大文件上传)
const initFileUpload = async (): Promise<UploadResult | null> => {
  // 确保哈希值不为空
  if (!fileInfo.value.fileHash) {
    // 尝试重新计算哈希
    try {
      if (fileInfo.value.file) {
        await calculateFileHash(fileInfo.value.file);
      }
    } catch (error) {
      console.error("重新计算哈希失败:", error);
    }

    // 如果哈希还是为空，使用备用哈希
    if (!fileInfo.value.fileHash && fileInfo.value.file) {
      fileInfo.value.fileHash = generateFallbackHash(fileInfo.value.file);
    }
  }

  try {
    if (!fileInfo.value.file) {
      throw new Error("文件不存在");
    }

    // 调用TypeScript版的接口，注意传递的chunkSize单位是MB
    const initResult: InitResponse | null = await initUpload(
      fileInfo.value.file,
      fileInfo.value.fileHash,
      fileInfo.value.fileType,
      props.projectCode,
      chunkSize.value / (1024 * 1024) // 转换为MB
    );
    if (initResult) {
      return {
        status: initResult.status,
        fileCode: initResult.fileCode,
        filePath: initResult.path,
        fileHash: initResult.hash,
        projectCode: initResult.projectCode,
      };
    } else {
      throw new Error("初始化返回数据无效");
    }
  } catch (error) {
    console.error("文件初始化请求失败:", error);
    throw error;
  }
};

// 上传文件分片 (TIF大文件上传)
const uploadChunks = async (initResult: UploadResult) => {
  if (currentChunkIndex.value >= fileInfo.value.chunkCount) {
    return;
  }

  try {
    for (let i = currentChunkIndex.value; i < fileInfo.value.chunkCount; i++) {
      if (!isUploading.value) break;

      const chunk = chunks.value[i];
      if (!chunk) continue;

      // 当前分片的进度
      let chunkProgress = 0;

      // 直接使用TypeScript版的接口
      const result = await uploadChunk(
        chunk.data,
        i,
        initResult.fileHash,
        initResult.projectCode,
        initResult.fileCode,
        (progress) => {
          // 更细粒度的进度显示：每个分片的进度 + 已完成分片的进度
          chunkProgress = progress;
          const completedChunksProgress = (i / fileInfo.value.chunkCount) * 100;
          const currentChunkContribution = progress / fileInfo.value.chunkCount;
          uploadProgress.value = Math.floor(
            completedChunksProgress + currentChunkContribution
          );
        }
      );

      if (!result.success) {
        throw new Error(`分片${i}上传失败`);
      }

      // 保存返回的数据
      if (result.data) {
        // 可以使用返回的path和status等信息
        if (
          result.data.status === "success" &&
          i === fileInfo.value.chunkCount - 1
        ) {
          // 最后一个分片上传成功，可以立即处理
          uploadStatus.value = "success";
          uploadProgress.value = 100;
          handleUploadSuccessTif(result.data.fileCode, result.data.path || ""); // 确保path不为undefined

          // 清除定时器，因为已经确认成功
          if (statusCheckTimer.value) {
            clearInterval(statusCheckTimer.value);
            statusCheckTimer.value = null;
          }

          return; // 提前结束
        }
      }

      currentChunkIndex.value = i + 1;
      uploadProgress.value = Math.floor(
        (currentChunkIndex.value / fileInfo.value.chunkCount) * 100
      );
    }
  } catch (error) {
    ElMessage.error("分片上传失败，稍后可继续上传");
    isUploading.value = false;
  }
};

// 开始状态检查 (TIF大文件上传)
const startStatusCheck = async (initResult: UploadResult) => {
  // 清除已有定时器
  if (statusCheckTimer.value) {
    clearInterval(statusCheckTimer.value);
    statusCheckTimer.value = null;
  }

  // 先立即执行一次状态检查
  try {
    const result = await checkUploadStatus(initResult);
    console.log("初始状态检查:", result);

    if (
      result &&
      result.status === "success" &&
      (result.filePath || result.path)
    ) {
      // 如果状态已经是成功，并且有path，直接处理
      uploadStatus.value = "success";
      uploadProgress.value = 100;
      await handleUploadSuccessTif(
        result.fileCode,
        result.filePath || result.path || ""
      );
      return; // 不需要设置定时器
    }
  } catch (error) {
    console.error("初始状态检查失败:", error);
  }

  // 每3秒查询一次上传状态
  statusCheckTimer.value = window.setInterval(async () => {
    try {
      const result = await checkUploadStatus(initResult);
      console.log("查询一次上传状态", result);
      if (!result) return;

      if (result.status === "success" && (result.filePath || result.path)) {
        // 状态为成功且有path时，清除定时器
        if (statusCheckTimer.value) {
          clearInterval(statusCheckTimer.value);
          statusCheckTimer.value = null;
        }
        uploadStatus.value = "success";
        uploadProgress.value = 100;
        await handleUploadSuccessTif(
          result.fileCode,
          result.filePath || result.path || ""
        );
      }
    } catch (error) {
      console.error("状态查询失败:", error);
    }
  }, 3000) as unknown as number;
};

// 检查上传状态 (TIF大文件上传)
const checkUploadStatus = async (
  initResult: UploadResult
): Promise<UploadResult | null> => {
  try {
    const result: StatusResponse | null = await getUploadStatus(
      initResult.projectCode,
      initResult.fileCode
    );

    if (result) {
      console.log("获取上传状态成功:", result);

      // 确保返回所有需要的字段
      const uploadResult: UploadResult = {
        status: result.status,
        fileCode: result.fileCode || initResult.fileCode,
        filePath: result.path || "", // 使用接口返回的path作为filePath
        path: result.path || "", // 同时保留path属性
        projectCode: result.projectCode || initResult.projectCode,
        fileHash: result.hash || initResult.fileHash,
      };

      // 如果状态为success，更新uploadStatus
      if (uploadResult.status === "success") {
        uploadStatus.value = "success";
        uploadProgress.value = 100;
      }

      return uploadResult;
    } else {
      throw new Error("获取状态失败");
    }
  } catch (error) {
    console.error("上传状态查询失败:", error);
    throw error;
  }
};

// 修改上传成功TIF格式文件处理方法，添加文件类型参数
const handleUploadSuccessTif = async (
  fileCode: string,
  filePath: string,
  fileType?: string
) => {
  isUploading.value = false;

  mitts.emit(mittEvents["uploadSuccessTif"], {
    fileCode,
    filePath,
    fileName: fileInfo.value.fileName,
    fileHash: fileInfo.value.fileHash,
    fileType: fileType || props.fileType, // 使用传入的文件类型或默认文件类型
    projectCode: props.projectCode,
    tiffStatus: "SUCCESS",
  });
};

// 修改上传成功CAD格式文件处理方法，添加文件类型参数
const handleUploadSuccessCAD = async (storageFileId: string) => {
  isUploading.value = false;
  mitts.emit(mittEvents["uploadSuccessCAD"], {
    storageFileId,
    fileName: fileInfo.value.fileName,
    fileHash: fileInfo.value.fileHash,
    fileType: props.fileType, // 使用传入的文件类型或默认文件类型
    projectCode: props.projectCode,
    cadStatus: "SUCCESS",
  });
};

// 取消上传
const cancelUpload = () => {
  isUploading.value = false;
  // 调用API取消上传
  cancelUploadApi();

  if (statusCheckTimer.value) {
    clearInterval(statusCheckTimer.value);
    statusCheckTimer.value = null;
  }
  resetUpload();
};

// 重置上传状态
const resetUpload = () => {
  fileSelected.value = false;
  fileInfo.value = {
    file: null,
    fileName: "",
    fileSize: 0,
    fileHash: "",
    fileType: "",
    chunkCount: 0,
  };
  uploadStatus.value = "init";
  isUploading.value = false;
  uploadProgress.value = 0;
  currentChunkIndex.value = 0;
  chunks.value = [];
};

// 移除文件处理
const handleFileRemove = () => {
  resetUpload();
};

// 组件卸载前清理定时器
onBeforeUnmount(() => {
  if (statusCheckTimer.value) {
    clearInterval(statusCheckTimer.value);
    statusCheckTimer.value = null;
  }
});

// 暴露方法给父组件
defineExpose({
  resetUpload
});
</script>

<style scoped lang="scss">
.file-uploader {
  width: 100%;

  .upload-button {
    width: 100%;
    background-color: #ff923f;
    border-color: #ff923f;
    color: #fff;
    &:hover {
      background-color: #e67e2e;
      border-color: #e67e2e;
    }
  }

  .el-upload__tip {
    margin-top: 5px;
    font-size: 12px;
    color: #909399;
  }

  .file-info {
    width: 100%;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .file-details {
      display: flex;
      flex-direction: column;
      gap: 5px;
      margin-bottom: 10px;
      font-size: 14px;
    }

    .upload-progress {
      .upload-actions {
        display: flex;
        gap: 10px;
        margin-top: 10px;
      }
    }

    .upload-success {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #67c23a;

      .upload-success-icon {
        font-size: 16px;
      }
    }
  }
}
</style>
