<template>
    <div class="galleryFrame">                       
        <div class="title">
            <div class="name">云图库</div>
            <div class="close" @click="btnClose">X</div>
        </div>
        <div class="blockbody">
            <div v-for="item in blockBox.blockList" class="block" @click="createBlock(item.blockName)">
                {{ item.blockName }}
            </div>
        </div>
    </div>
</template>
    
<script setup>
import {blockBox,DxfviewerCad,viewlayout} from 'dxf-viewer'
import { PubSub } from 'emitter';

const btnClose=()=>{
  viewlayout.visBlockGallery=false
}
const createBlock=(blockName)=>{
  let transaction=DxfviewerCad.value.GetViewer().transaction
  // let ibk=new sg.InsertSGBlock()
  // ibk.setInsertBlockName(blockName)

  let bk=transaction.gpObj.getBlockByName(blockName)
  let bw=bk.getBoundingBox()
  let  bwcenter=bw.getMiddlePoint()//中心点坐标
  let width=bw.width()
  let heigth=bw.heigth()
  PubSub.default.pub('executeCommand','Block')
  Object.assign(DxfviewerCad.value.GetViewer().grapicControl.current,{
    surroundingBoxwidth:width,
    surroundingBoxheight:heigth,
    blockName:blockName,
    bkWasm:bk,
    blockcenter:bwcenter
  })
}

</script>

<style lang="scss" scoped>
.galleryFrame {
  width: 300px;
  flex: 0 0 auto;
  background-color: #fff;
  .title {
    display: flex;
    justify-content: space-between;
    .name {
        flex: auto;
        text-align: center;
    }
    .close {
        width: 30px;
        text-align: center;
        font-weight: 500;
        padding: 0px 8px 5px 8px;
        cursor: pointer;
        &:hover{
          background-color: #57aaff;
        }
    }
  }
  .blockbody {
    .block {
        width: 60px;
        height: 60px;
        margin: 10px;
        background-color: #4e5664;
        color: #fff;
        text-align: center;
        line-height: 60px;
    }
  }
}
</style>