import { Matrix3, Vector2 } from "three"
import { BlockContextType } from "../constant"

export class BlockContext {
    public block
    public type:BlockContextType
    public origin
    public transform
    constructor(block, type:BlockContextType) {
        this.block = block
        this.type = type
        this.origin = this.block.data.position
        /* Transform to apply for block definition entities not including block offset. */
        this.transform = new Matrix3()
    }

    /** @return {string} Block name */
    get name() {
        return this.block.data.name
    }

    /**
     * @param v {{x,y}}
     * @return {{x,y}}
     */
    TransformVertex(v) {
        const result = new Vector2(v.x, v.y).applyMatrix3(this.transform)
        if (this.type !== BlockContextType.DEFINITION &&
            this.type !== BlockContextType.NESTED_DEFINITION) {
            throw new Error("Unexpected transform type")
        }
        this.block.verticesCount++
        if (this.block.offset === null) {
            /* This is the first vertex. Take it as a block origin. So the result is always zero
             * vector for the first vertex.
             */
            this.block.offset = result
            const v = new Vector2()
            this.block.UpdateBounds(v)
            return v
        }
        result.sub(this.block.offset)
        this.block.UpdateBounds(result)
        return result
    }

    /**
     * Get transform for block instance.
     * @param entity Raw DXF INSERT entity.
     * @return {Matrix3} Transform matrix for block instance to apply to the block definition.
     */
    GetInsertionTransform(entity) {
        const mInsert = new Matrix3().translate(-this.origin.x, -this.origin.y)
        const yScale = entity.yScale || 1
        const xScale = entity.xScale || 1
        const rotation = -(entity.rotation || 0) * Math.PI / 180
        let x = entity.position.x
        const y = entity.position.y
        mInsert.scale(xScale, yScale)
        mInsert.rotate(rotation)
        mInsert.translate(x, y)
        if (entity.extrusionDirection && entity.extrusionDirection.z < 0) {
            mInsert.scale(-1, 1)
        }
        if (this.type !== BlockContextType.INSTANTIATION) {
            return mInsert
        }
        const mOffset = new Matrix3().translate(this.block.offset.x, this.block.offset.y)
        return mInsert.multiply(mOffset)
    }

    GetWasmInsertionTransform(insert) {     
        const xscale = insert.getXScale();
        const yscale = insert.getYScale();
        const rotation = -insert.getRotateAngle() * Math.PI / 180;
        const insertPoint = insert.getInsertPoint();
        // console.log(xscale,yscale,rotation,insertPoint.x(), insertPoint.y(),this.block.offset,this.origin)
        let insertMatrix = new Matrix3().translate(-this.origin.x, -this.origin.y)
        insertMatrix.scale(xscale, yscale);
        insertMatrix.rotate(rotation);
        insertMatrix.translate(insertPoint.x(), insertPoint.y());
        if (this.type !== BlockContextType.INSTANTIATION) {
            return insertMatrix
        }
        let mOffset
        if(!this.block.offset){
            mOffset=new Matrix3()
        }else{
            mOffset = new Matrix3().translate(this.block.offset.x, this.block.offset.y)
        }
        return insertMatrix.multiply(mOffset)
    }

    /**
     * Create context for nested block.
     * @param block {Block} Nested block.
     * @param entity Raw DXF INSERT entity.
     * @return {BlockContext} Context to use for nested block entities.
     */
    NestedBlockContext(block, entity) {
        block.RegisterNestedUse(this.block)
        const nestedCtx = new BlockContext(block, BlockContextType.NESTED_DEFINITION)
        const nestedTransform = nestedCtx.GetInsertionTransform(entity)
        const ctx = new BlockContext(this.block, BlockContextType.NESTED_DEFINITION)
        ctx.transform = new Matrix3().multiplyMatrices(this.transform, nestedTransform)
        return ctx
    }

    WasmNestedBlockContext(block, insert) {
        block.RegisterNestedUse(this.block)
        const nestedCtx = new BlockContext(block, BlockContextType.NESTED_DEFINITION)
        const nestedTransform = nestedCtx.GetWasmInsertionTransform(insert)
        const ctx = new BlockContext(this.block, BlockContextType.NESTED_DEFINITION)
        ctx.transform = new Matrix3().multiplyMatrices(this.transform, nestedTransform)
        return ctx
    }
}