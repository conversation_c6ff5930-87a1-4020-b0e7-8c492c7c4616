import md5 from "js-md5";
import axios from "axios";
import { apiQueryUploadFileUrl, materialUrl } from "@/http/buildingBlockApi.js";

export const download = () => {};

export const savedxf = (arr: Array<any>) => {
  // arr
};

export const randomNumber = (count, max, min = 1) => {
  let randomNumbers: any = [];
  for (let i = 0; i < count; i++) {
    randomNumbers.push(Math.floor(Math.random() * (max - min + 1)) + min);
  }
  return randomNumbers;
};

// 上传文件
export const uploadfileFn = (files) => {
  console.log(files, "uploadfileFn");
  return new Promise((resove, reacjet) => {
    let params = {
      name: files[0].name,
      size: files[0].size,
      md5: "",
    };
    const reader = new FileReader();
    reader.onloadend = function (e: any) {
      // 从事件对象中获取已读取的文件内容（转换为字节数组）
      const content = e.target.result;
      // 使用js-md5计算文件内容的MD5散列值
      //@ts-ignore
      params.md5 = md5(content as string);
      apiQueryUploadFileUrl(params)
        .then((res: any) => {
          if (res.code === 200) {
            if (res.data.url) {
              var instance = axios.create();
              instance.interceptors.request.use((config) => {
                // 删除Content-Type，仅针对PUT请求
                if (config.method === "put") {
                  config.headers["Content-Type"] = "";
                }
                return config;
              });
              instance
                .put(res.data.url, files[0])
                .then((item) => {
                  let reader = new FileReader();
                  reader.readAsDataURL(files[0]);
                  reader.onload = (e) => {
                    resove({ storageFileId: res.data.storageFileId });
                  };
                })
                .catch((error) => {
                  reacjet(error);
                });
            } else {
              resove({ storageFileId: res.data.storageFileId });
            }
          }
        })
        .catch((error) => {
          reacjet(error);
        });
    };
    reader.readAsArrayBuffer(files[0]);
  });
};
