<template> 
  <div class="choiceLinestyles">
    <el-select @change="onChangeLinetype" v-model="curLinetype" style="min-width: 100px;margin:0 5px;">
      <el-option v-for="itm, i in linetypes" :key="i" :label="itm.name" :value="itm">
        <div style="display: flex;align-items: center;">
          <!-- <div class="colorbtn"></div> -->
          <div>{{ itm.name }}</div>
        </div>
      </el-option>
      <template #prefix>
        <div class="fb fc">
          <!-- <div class="colorbtn"> </div> -->
          <div>{{ curLinetype.name }}</div>
        </div>
      </template>
    </el-select>
  </div>
</template>

<script setup>

import {
  dxfLinetypes,
} from 'dxf-viewer'
import { PubSub } from 'emitter'
const curLinetype=dxfLinetypes.curLinetype
const linetypes = dxfLinetypes.linetypes

const onChangeLinetype = (val) => {
  console.log(val,'val');
  dxfLinetypes.curLinetype=val
}

onMounted(() => {
  
})


</script>

<style lang="scss" scoped>
.choiceLinestyles {
  position: relative;
  background-color: #212832;

  .show {
    width: 300px;
    height: 25px;
    line-height: 25px;
    align-items: center;
  }

  .itembox {
    width: 300px;
    position: absolute;
    z-index: 99;
    top: 30px;
    background-color: #212832;
    color: '#fff';
    min-height: 100px;
    max-height: 300px;
    overflow-y: scroll;

    .item {
      margin: 5px 0;
      display: flex;
      align-items: center;

      &:hover {
        background-color: #787878;
      }
    }

  }

  .ico {
    margin: 0 3px;

    &:hover {
      background-color: red;
    }
  }

  .text {
    margin-left: 30PX;
    color: #fff;
    font-size: 14px;
    font-weight: 100;
  }
}

.no-select {
  user-select: none;
  -moz-user-select: none;
  /* Firefox */
  -webkit-user-select: none;
  /* Safari */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
}

.colorbtn {
  width: 15px;
  height: 15px;
  border: 1px solid gray;
}
</style>