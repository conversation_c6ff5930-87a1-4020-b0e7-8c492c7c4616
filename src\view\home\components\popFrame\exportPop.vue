<!-- eslint-disable vue/no-mutating-props -->
<template>
    <div class="exportPop">
        <el-dialog
            :model-value="exportPopVisible"
            title="dxf/dwg 导入"
            width="500"
        >
            <div style="margin-top: 20px;font-size: 16px;color:#fff">
              <el-select v-model="ty" placeholder="类型" style="width: 350px">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input v-model="mn" style="width: 350px;margin-top: 20px" placeholder="文件名称" />
            </div>
            <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="btnSave">确认</el-button>
              <el-button @click="btnCancel">关闭</el-button>
            </div>
            </template>
        </el-dialog>
    </div>
  </template>
  
  <script setup>
    import { ref } from 'vue'
    import {mitts,btnFnObj} from 'sgdraw/mittBus'
  
    const emits = defineEmits(['btnSavecskdPop','btnCancelcskd'])
    const props = defineProps({
      exportPopVisible:{
          type: Boolean,
          default:false
       }
    })
  
    const ty = ref('')
    const mn = ref('')
  
    const options = [
      {
        value: 'straightLine',
        label: 'dxf',
      },
      {
        value: 'drawingObject2dxf',
        label: 'dwg',
      },
    ]
    const options2 = [
      {
        value: 1000,
        label: '1000',
      },
      {
        value: 10000,
        label: '10000',
      },
      {
        value: 100000,
        label: '100000',
      },
    ]
  
    const btnSave=()=>{
      if(!ty.value) return
      if(!mn.value) return
      let obj={
        ty:ty.value,
        mn:mn.value,
      }
      emits('btnSavecskdPop',obj)
      
    }
  
    const btnCancel=()=>{
     emits('btnCancelcskd')
    }
  
  
  
    const cleanFn=()=>{
      ty.value=''
      nu.value=''
    }
  
    defineExpose({
        cleanFn
    })
  
  </script>
  
  <style lang="scss" scoped>
  .exportPop {
    :deep(.el-dialog) {
      background-color: #3a4352;
    }
    :deep(.el-dialog__body) {
        padding:0 !important;
    }
    :deep(.el-dialog__headerbtn) {
      width: 16px !important;
      height: 26px !important;
      top:1px;
      
    }
    :deep(.el-dialog__header) {
      padding:0 !important;
      padding-bottom:0;
      
      .el-dialog__title {
        font-size:16px;
        color: #fff !important;
      }
    }
    .sq {
      display: flex;
      align-items: center;
      .btn {
        width: 8px;
        height: 8px;
        background-color: blue;
      }
    }
  }
  </style>
    
    