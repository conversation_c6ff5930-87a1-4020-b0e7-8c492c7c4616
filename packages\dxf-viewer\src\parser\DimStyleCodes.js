/** Dimension style variables are used either in DIMST<PERSON><PERSON> table or in DIMENSION entity style
 * override in XDATA.
 */
const codes = new Map([
    [140, "DIMTXT"],
    [142, "DIMTSZ"],
    [144, "DIM<PERSON><PERSON>"],
    [147, "<PERSON><PERSON><PERSON><PERSON>"],
    [173, "<PERSON><PERSON><PERSON><PERSON>"],
    [175, "DIMSOX<PERSON>"],
    [176, "<PERSON><PERSON><PERSON><PERSON>"],
    [177, "DIMCL<PERSON>"],
    [178, "<PERSON><PERSON><PERSON><PERSON>"],
    [271, "DIMDE<PERSON>"],
    [278 ,"DIMDS<PERSON>"],
    [281, "DIMSD1"],
    [282, "DIMSD2"],
    [3, "DIMPOST"],
    [40, "DIMSCAL<PERSON>"],
    [41, "<PERSON><PERSON><PERSON><PERSON>"],
    [42, "<PERSON>IM<PERSON><PERSON>"],
    [44, "DIMEX<PERSON>"],
    [45, "<PERSON><PERSON><PERSON><PERSON>"],
    [46, "DIMD<PERSON>"],
    [5, "DIMBLK"],
    [6, "DIMBLK1"],
    [7, "DIMBLK2"],
    [75, "DIMSE1"],
    [76, "DIMSE2"],
    [78, "<PERSON><PERSON><PERSON><PERSON>"],
])

export default codes
