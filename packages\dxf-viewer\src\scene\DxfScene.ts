import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>der<PERSON><PERSON> } from "../batch"
import { BatchEntityType, defaultDxfSceneOption, DynamicSceneType, EntityType, PdMode } from "../constant"
import { <PERSON><PERSON><PERSON>, MTextFormatParser, textRender } from "../entities"
import { globalDxf } from "../global"
import { mathBig<PERSON>lampVec, RBTree } from "../libs"

export class DxfScene{
    public viewer
    public sceneDa
    public options:any
    public batches: typeof RBTree
    public bounds:any=null
    public blocks:Map<any,any>
    public layers: Map<string, any> 
    public dimStyles:Map<string,any>

    public pdMode
    public pdSize
    public origin
    public wcsOff
    public dynamicType:DynamicSceneType=DynamicSceneType.None
    public idMapBatch:Map<number,Set<RenderBatch>>=new Map()
    constructor(viewer,options?) {
        this.viewer=viewer
        this.options = Object.create(defaultDxfSceneOption)
        if (options) {
            Object.assign(this.options, options.sceneOptions)
        }
        this.origin = null
        this.wcsOff = null
        /* RBTree<BatchingK<PERSON>, RenderBatch> */
        this.batches = new RBTree((b1, b2) => b1.key.Compare(b2.key))
        /* Indexed by layer name, value is layer object from parsed DXF. */
        this.layers = new Map()
        /* Indexed by block name, value is Block. */
        this.blocks = new Map()
        /** Indexed by dimension style name, value is DIMSTYLE object from parsed DXF. */
        this.dimStyles = new Map()

    }
    /** @return False to suppress the specified entity, true to permit rendering. */
    _FilterEntity(entity) {
        return !this.options.suppressPaperSpace || !entity.inPaperSpace
    }

    public clear() {
        this.sceneDa=null
        this.origin = null;
        this.wcsOff = null
        this.bounds = null;
        this.batches.clear();
        this.layers.clear();
        this.blocks.clear();
        this.dimStyles.clear();
        this.idMapBatch.clear()
        console.log('DxfScene clear')
    }

    /**
     * @param entity {Entity}
     * @param blockCtx {?BlockContext}
     */
    _ProcessEntity(entity, blockCtx?) {
        if(!blockCtx){
            if(this.dynamicType!=DynamicSceneType.None){
                blockCtx={
                    name : this.dynamicType,
                    TransformVertex : function(v) {
                        return { x: v.x - globalDxf.wcsOff.x, y: v.y - globalDxf.wcsOff.y }
                    }
                }
            }
        }
        switch (entity.type) {
            case EntityType.POINTS:
                this._ProcessPoints(entity, blockCtx)
                break
            case EntityType.LINE_SEGMENTS:
                this._ProcessLineSegments(entity, blockCtx)
                break
            case EntityType.DASHLINE:
                this._ProcessDashline(entity, blockCtx)
                break
            case EntityType.POLYLINE:
                this._ProcessPolyline(entity, blockCtx)
                break
            case EntityType.TRIANGLES:
                this._ProcessTriangles(entity, blockCtx)
                break
            default:
                throw new Error("Unhandled entity type: " + entity.type)
        }
    }


    /**
     * @param entity {Entity}
     * @param blockCtx {?BlockContext}
     */
    _ProcessPoints(entity, blockCtx = null) {
        const key = new BatchingKey(entity.layer, blockCtx?.name,
                                    BatchEntityType.POINTS, entity.color, 0)
        const batch = this._GetBatch(key)
        this.pushRenderbatchToIdmap(entity.entityId,batch)
        for(let i=0;i<entity.vertices.length/2;i++) {
            let v={
                x:entity.vertices[i*2],
                y:entity.vertices[i*2+1]
            }
            batch.PushVertex(this._TransformVertex(v, blockCtx))
            batch.PushEntityId(entity.entityId)
            batch.PushEntityColor(entity.mrgb)
            batch.PushLineSyleId(entity.lineStyleId)
            batch.PushLineScale(entity.lineScale)
        }
    }

    /**
     * @param entity {Entity}  
     * @param blockCtx {?BlockContext}
     */
    _ProcessLineSegments(entity, blockCtx = null) {
        if (entity.vertices.length % 2 !== 0) {
            throw Error("Even number of vertices expected")
        }
        const key = new BatchingKey(entity.layer, blockCtx?.name,
                                    BatchEntityType.LINES, entity.color, entity.lineType)
        const batch = this._GetBatch(key)
        this.pushRenderbatchToIdmap(entity.entityId,batch)
        for(let i=0;i<entity.vertices.length/2;i++) {
            let v={
                x:entity.vertices[i*2],
                y:entity.vertices[i*2+1]
            }
            batch.PushVertex(this._TransformVertex(v, blockCtx))
            batch.PushEntityId(entity.entityId)
            batch.PushLineSyleId(entity.lineStyleId)
            batch.PushLineScale(entity.lineScale)
            batch.PushEntityColor(entity.mrgb)
        }
    }

    _ProcessDashline(entity:Entity, blockCtx = null) {
        const verticesCount = entity.vertices.length/2
        if (verticesCount < 2) {
            return
        }
        if (verticesCount <= 3) {
            const key = new BatchingKey(entity.layer, blockCtx?.name,
                                        BatchEntityType.DASH_SEGMENTS, entity.color,
                                        entity.lineType)
            const batch = this._GetBatch(key)
            this.pushRenderbatchToIdmap(entity.entityId,batch)
            for(let i=1; i<verticesCount; i++){
                let prev={
                    x:entity.vertices[2*(i - 1)],
                    y:entity.vertices[2*(i - 1)+1]
                }
                batch.PushVertex(this._TransformVertex(prev, blockCtx))
                batch.PushEntityId(entity.entityId)
                batch.PushLineSyleId(entity.lineStyleId)
                batch.PushLineScale(entity.lineScale)
                batch.PushEntityColor(entity.mrgb)
                let v={
                    x:entity.vertices[2*i],
                    y:entity.vertices[2*i+1]
                }
                batch.PushVertex(this._TransformVertex(v, blockCtx))
                batch.PushEntityId(entity.entityId)
                batch.PushLineSyleId(entity.lineStyleId)
                batch.PushLineScale(entity.lineScale)
                batch.PushEntityColor(entity.mrgb)
            }
        
            if (entity.shape && verticesCount > 2) {
                let v1={
                    x:entity.vertices[2*(verticesCount - 1)],
                    y:entity.vertices[2*(verticesCount - 1)+1]
                }
                batch.PushVertex(this._TransformVertex(v1, blockCtx))
                batch.PushEntityId(entity.entityId)
                batch.PushLineSyleId(entity.lineStyleId)
                batch.PushLineScale(entity.lineScale)
                batch.PushEntityColor(entity.mrgb)
                let v2={
                    x:entity.vertices[0],
                    y:entity.vertices[1]
                }
                batch.PushVertex(this._TransformVertex(v2, blockCtx))
                batch.PushEntityId(entity.entityId)
                batch.PushLineSyleId(entity.lineStyleId)
                batch.PushLineScale(entity.lineScale)
                batch.PushEntityColor(entity.mrgb)
            }
            return
        }

        const key = new BatchingKey(entity.layer, blockCtx?.name, BatchEntityType.DASH_LINES,
                                    entity.color, entity.lineType)
        const batch = this._GetBatch(key)
        this.pushRenderbatchToIdmap(entity.entityId, batch)
        /* Line may be split if exceeds chunk limit. */
        for (const lineChunk of entity._IterateLineChunks()) {
            const chunk = batch.PushChunk(lineChunk.verticesCount)
            for (const v of lineChunk.vertices) {
                chunk.PushVertex(this._TransformVertex({x:v[0],y:v[1]}, blockCtx))
            }
            for (const id of lineChunk.entityIds) {
                chunk.PushEntityId(id)
            }
            for (const id of lineChunk.lineStyleIds) {
                chunk.PushLineSyleId(id)
            }
            for (const s of lineChunk.lineScales) {
                chunk.PushLineScale(s)
            }
            for(let color of lineChunk.entityColors){
                chunk.PushEntityColor(color)
            }
            chunk.Finish()
        }
    }
    /**
     * @param entity {Entity}
     * @param blockCtx {?BlockContext}
     */
    _ProcessPolyline(entity:Entity, blockCtx = null) {
        const verticesCount = entity.vertices.length/2
        if (verticesCount < 2) {
            return
        }
        /* It is more optimal to render short polylines un-indexed. Also DXF often contains
         * polylines with just two points.
         */
        if (verticesCount <= 3) {
            const key = new BatchingKey(entity.layer, blockCtx?.name,
                                        BatchEntityType.LINES, entity.color,
                                        entity.lineType)
            
            const batch = this._GetBatch(key)
            this.pushRenderbatchToIdmap(entity.entityId,batch)
            for(let i=1; i<verticesCount; i++){
                let prev={
                    x:entity.vertices[2*(i - 1)],
                    y:entity.vertices[2*(i - 1)+1]
                }
                batch.PushVertex(this._TransformVertex(prev, blockCtx))
                batch.PushEntityId(entity.entityId)
                batch.PushLineSyleId(entity.lineStyleId)
                batch.PushLineScale(entity.lineScale)
                batch.PushEntityColor(entity.mrgb)
                let v={
                    x:entity.vertices[2*i],
                    y:entity.vertices[2*i+1]
                }
                batch.PushVertex(this._TransformVertex(v, blockCtx))
                batch.PushEntityId(entity.entityId)
                batch.PushLineSyleId(entity.lineStyleId)
                batch.PushLineScale(entity.lineScale)
                batch.PushEntityColor(entity.mrgb)
            }

            if (entity.shape && verticesCount > 2) {
                let v1={
                    x:entity.vertices[2*(verticesCount - 1)],
                    y:entity.vertices[2*(verticesCount - 1)+1]
                }
                batch.PushVertex(this._TransformVertex(v1, blockCtx))
                batch.PushEntityId(entity.entityId)
                batch.PushLineSyleId(entity.lineStyleId)
                batch.PushLineScale(entity.lineScale)
                batch.PushEntityColor(entity.mrgb)
                let v2={
                    x:entity.vertices[0],
                    y:entity.vertices[1]
                }
                batch.PushVertex(this._TransformVertex(v2, blockCtx))
                batch.PushEntityId(entity.entityId)
                batch.PushLineSyleId(entity.lineStyleId)
                batch.PushLineScale(entity.lineScale)
                batch.PushEntityColor(entity.mrgb)
            }
            return
        }

        const key = new BatchingKey(entity.layer, blockCtx?.name, BatchEntityType.INDEXED_LINES,
                                    entity.color, entity.lineType)
        
        const batch = this._GetBatch(key)
        this.pushRenderbatchToIdmap(entity.entityId, batch)
        /* Line may be split if exceeds chunk limit. */
        for (const lineChunk of entity._IterateLineChunks()) {
            const chunk = batch.PushChunk(lineChunk.verticesCount)
            for (const v of lineChunk.vertices) {
                chunk.PushVertex(this._TransformVertex({x:v[0],y:v[1]}, blockCtx))
            }
            for (const idx of lineChunk.indices) {
                chunk.PushIndex(idx)
            }
            for (const id of lineChunk.entityIds) {
                chunk.PushEntityId(id)
            }
            for (const id of lineChunk.lineStyleIds) {
                chunk.PushLineSyleId(id)
            }
            for (const s of lineChunk.lineScales) {
                chunk.PushLineScale(s)
            }
            for(let color of lineChunk.entityColors){
                chunk.PushEntityColor(color)
            }
            chunk.Finish()
        }
    }

    /**
     * @param entity {Entity}
     * @param blockCtx {?BlockContext}
     */
    _ProcessTriangles(entity:Entity, blockCtx = null) {
        const verticesCount = entity.vertices.length/2
        if (verticesCount < 3) {
            return
        }
        if (entity.indices.length % 3 !== 0) {
            console.error("Unexpected size of indices array: " + entity.indices.length)
            return
        }
        const key = new BatchingKey(entity.layer, blockCtx?.name,
                                    BatchEntityType.INDEXED_TRIANGLES,
                                    entity.color, 0)
        const batch = this._GetBatch(key)

        this.pushRenderbatchToIdmap(entity.entityId,batch)
        //XXX splitting into chunks is not yet implemented. Currently used only for text glyphs so
        // should fit into one chunk
        const chunk = batch.PushChunk(verticesCount)
        for(let i=0;i<verticesCount;i++) {
            let v={
                x:entity.vertices[i*2],
                y:entity.vertices[i*2+1]
            }
            chunk.PushVertex(this._TransformVertex(v, blockCtx))
            chunk.PushEntityId(entity.entityId)
            chunk.PushLineSyleId(entity.lineStyleId)
            chunk.PushLineScale(entity.lineScale)
            chunk.PushEntityColor(entity.mrgb)
        }
        for (const idx of entity.indices) {
            chunk.PushIndex(idx)
        }
        chunk.Finish()
    }
    pushRenderbatchToIdmap(id,batch){
        if(!this.idMapBatch.has(id)){
            this.idMapBatch.set(id,new Set())
        }
        let batchset=this.idMapBatch.get(id)
        batchset?.add(batch)
    }
    /** @return {RenderBatch} */
    _GetBatch(key):RenderBatch {
        let batch = this.batches.find({key})
        if (batch !== null) {
            return batch
        }
        
        batch = new RenderBatch(key)
        this.batches.insert(batch)
        
        if (key.blockName !== null && !key.IsInstanced()) {
            /* Block definition batch. */
            const block = this.blocks.get(key.blockName)
            if (block) {
                block.batches.push(batch)
            }
        }
        return batch
    }

    /**
     * Apply all necessary final transforms to a vertex before just before storing it in a rendering
     * batch.
     * @param v {{x: number, y: number}}
     * @param blockCtx {BlockContext}
     * @return {{x: number, y: number}}
     */
    _TransformVertex(v, blockCtx = null) {
        if (blockCtx) {
            // Block definition in block coordinates. So it should not touch bounds and origin.
            return blockCtx.TransformVertex(v)
        }
        this._UpdateBounds(v)
        return { x: v.x - globalDxf.wcsOff.x, y: v.y - globalDxf.wcsOff.y }
    }

    /** @param v {{x,y}} Vertex to extend bounding box with and set origin. */
    _UpdateBounds(v) {
        if (this.bounds === null) {
            this.bounds = { minX: v.x, maxX: v.x, minY: v.y, maxY: v.y }
        } else {
            if (v.x < this.bounds.minX) {
                this.bounds.minX = v.x
            } else if (v.x > this.bounds.maxX) {
                this.bounds.maxX = v.x
            }
            if (v.y < this.bounds.minY) {
                this.bounds.minY = v.y
            } else if (v.y > this.bounds.maxY) {
                this.bounds.maxY = v.y
            }
        }
        if (this.origin === null) {
            this.origin = { x: v.x, y: v.y, z: 0 }
            if(this==this.viewer.dxfScene){
                globalDxf.origin=this.origin
                globalDxf.wcsOff= mathBigClampVec(this.origin)
                console.log(globalDxf.wcsOff,'globalDxf.wcsOff');
            }
        }
    }

    _BuildSceneDa() {
        let verticesSize = 0
        let indicesSize = 0
        let transformsSize = 0
        let entityIdSize  = 0
        let instanceIdsSize = 0;
        let batchSize = 0
        this.batches.each(b => {
            batchSize += 1
            verticesSize += b.GetVerticesBufferSize()
            indicesSize += b.GetIndicesBufferSize()
            transformsSize += b.GetTransformsSize()
            entityIdSize += b.GetEntityIdBufferSize()
            instanceIdsSize += b.GetInstanceIdsBufferSize()
        })
        const sceneDa:any  = {
            verticesSize,
            indicesSize,
            transformsSize,
            entityIdSize,
            instanceIdsSize,
            batchSize,
        }
        
        sceneDa.pointShapeHasDot = (this.pdMode & PdMode.MARK_MASK) === PdMode.DOT
        this.sceneDa=sceneDa
        return sceneDa
    }
}