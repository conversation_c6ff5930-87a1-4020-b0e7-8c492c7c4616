import { ref,reactive} from 'vue'
import { ElLoading ,ElMessage } from "element-plus";
import {dxfZsObj,dxflayers,insertBlockXData,pickupInfo,backfilterGroupsByIds,} from 'dxf-viewer'
import { cloneDeep } from 'lodash';

export const drawingMenuControl=ref('LENGTH')
export let comparisonResultsGp:any=null //对比图纸结果

let dxfview:any=null
export class MenuBarMethod {
    // public dxfview:any
    constructor(obj){
        dxfview=obj
    }

    public menuControl=(obj)=>{
        switch (obj.identifyItem) {
            case 'LENGTH':
                drawingMenuControl.value='LENGTH' //长度
               break;
            case 'ATTACH':
                drawingMenuControl.value='ATTACH' //附属构建
                break;
            case 'IDENTIFY':
               drawingMenuControl.value='IDENTIFY' //图元识别
               break;
            default:
                drawingMenuControl.value='' //长度
        }
    }
    public drawingInheritance1111=(obj,fileContent)=>{//图纸继承
        let newObj=cloneDeep(obj)
        let sgimp= new sg.SGDxfImport(fileContent as string)
        sgimp.importDxf()
        let gp1= sgimp.getDrawGroup()
        const viewer = dxfview.GetViewer()
        const transaction = viewer.transaction
        let pda=new sg.PaperDifferenceAlgor(gp1,transaction.gpObj)//gp1 上  gp2现版本
        pda.calDifferenceGp_Qt()
        let idobjs=pda.getIdPair()
        let count=idobjs.size()
        let idmap=new Map()
        for (let i=0;i<count;i++){
          let wobj=idobjs.get(i)
          let j=wobj.getPreId()  //旧图纸
          let x=wobj.getPostId() //新图纸
          idmap.set(j,x)
        }
        let objlist:any={}
        newObj.elements && newObj.elements.forEach(item=>{
          item.ids.forEach(id => {
            if(!id.toString().includes('A')) {
               let newID=idmap.get(Number(id))
               if(!newID) return
              //  newids.push({used:[id],new:[newID]})
              //  objlist.set(id,newID)
              objlist[id]=newID
            }
          })
        })
        
        newObj.groups && newObj.groups.forEach(item=>{
          let arrlist:any=[]
          item.ids.forEach(id => {
            let newID=idmap.get(Number(id))
            if(!newID) return
            arrlist.push(newID)
          })
          console.log(arrlist,'arrlist');
          
          // arrlist.length===item.ids.length &&  newids.push({used:item.ids,new:arrlist})
          if(arrlist.length===item.ids.length) {
            item.ids.forEach((id,index)=>{
              objlist[id]=arrlist[index]
            })
          }
        })
        return objlist
    }

    public drawingInheritance=(obj,fileContent)=>{//图纸继承
      let newObj=cloneDeep(obj)
      const sgmainComp = new sg.vector_ElementObj()
      let jcarr:any=[]
      if(newObj.elements.length) {
        newObj.elements.forEach(item=>{
          item.ids.forEach(id=>{
            if(!id.toString().includes('A')) {
              jcarr.push(Number(id)) 
            }
          })
        })
      }
      if(newObj.groups.length) {
        newObj.groups.forEach(item=>{
          jcarr.push(item.ids) 
        })
      }
      console.log(jcarr,'jcarr');
      jcarr.forEach(item=>{
        const vecint = new sg.vector_int
        if(item instanceof Array) {
          item.forEach(id=>{
            vecint.push_back(Number(id))
          })
        }else {
          vecint.push_back(item)
        }
        const element = new sg.ElementObj(vecint)
        sgmainComp.push_back(element)
      })

      let sgimp= new sg.SGDxfImport(fileContent as string)
      sgimp.importDxf()
      let gp1= sgimp.getDrawGroup()
      const viewer = dxfview.GetViewer()
      const transaction = viewer.transaction
      let pda=new sg.PaperDifferenceAlgor(gp1,transaction.gpObj)//gp1 上  gp2现版本
      pda.calDifferenceGp_Qt()
      let idobjs= pda.getPreElementObjInPost(sgmainComp)
    
      let obj2:any=[]
      let count=idobjs.size()
      let coordinateIdsInfo:any=[]
      for (let i=0;i<count;i++){
        let ids=idobjs.get(i).getObjIds()
        let num=ids.size()
        let arr:any=[]
        if(!num) {
          obj2.push({ids:[]})
          continue
        }
        let bw=sg.getElementObjRect(dxfZsObj.drawingId,idobjs.get(i))
        let czb=bw.getMiddlePoint()//中心点坐标
        for (let j=0;j<num;j++) {
          let id=ids.get(j)
          arr.push(id)
        }
        obj2.push({ids:arr})
      }
      // console.log(obj2,'obj2obj2obj2obj2obj2obj2');
      let objlist:any={}
      jcarr.forEach((item,index)=>{
        if(item instanceof Array) {
          if(obj2[index].ids.length && item.length ===obj2[index].ids.length) {
            item.forEach((id,idx)=>{
              objlist[id]=obj2[index].ids[idx]
            })
          }
        }else {
          if(obj2[index].ids.length) {
            objlist[item]=obj2[index].ids[0]
          }
        }
      })
      // console.log(objlist,'objlist');
      return objlist
  }






    public drawingComparisonFn=(fileContent)=>{//图纸对比
      let sgimp= new sg.SGDxfImport(fileContent as string)
      sgimp.importDxf()
      let gp1= sgimp.getDrawGroup()
      const viewer = dxfview.GetViewer()
      const transaction = viewer.transaction
      console.log(transaction.gpObj.count(),'transaction.gpObj');
      let pda=new sg.PaperDifferenceAlgor(gp1,transaction.gpObj)//gp1 上  gp2现版本
      pda.setChangeColor(true)
      // pda.setColor(new sg.SGColor(192, 192, 192,255),new sg.SGColor(255, 165, 0,255),new sg.SGColor(0, 0, 255,255))
      console.log('执行了3');
      pda.calDifferenceGp_Qt()
      let newgp=pda.getCommonGp()
      // console.log(newgp.count(),'xxxxxxxxxxx');
      // console.log(transaction.gpObj.count(),'ooooooooooooo');
      
      // newgp.initTransactionManager()
      comparisonResultsGp=newgp
    }


    //自定义图层
    public cstuc(name,isVisible){
      dxfview.GetViewer().slIdentifiedscene.layersVisible(name,isVisible)
      this.hidehandle()
    }
    public allslsceneVisible(isVisible){
      dxfview.GetViewer().slIdentifiedscene.layersALLVisible(isVisible)
      this.hidehandle()
    }
    public hidehandle=()=>{

      let obj=cloneDeep(pickupInfo.otherSelectedIdObj)
      console.log(dxflayers.layers);
      
      let viewer=dxfview.GetViewer()
      let namelist:any=[]
      dxflayers.layers.forEach(item=>{
          if(item.isSLidentifiedflag && !item.isVisible){
              namelist.push(item.name)
          }
      })
      let arr=insertBlockXData(viewer,namelist)
      
      console.log(obj.elements,'obj.elements');

      if(obj.elements && obj.elements.length) {
        obj.elements.forEach(item=>{
          item.ids=item.ids.filter(id=>!arr.includes(id))
        })
      }
      if(obj.groups && obj.groups.length) {
        obj.groups=backfilterGroupsByIds(obj.groups,arr)
      }
      pickupInfo.hideSelectedIdObj=obj
      
    }
}