/**
 * useExcel.js 使用示例
 * 展示如何使用优化后的Excel生成器
 */

import ExcelGenerator, { generateExcel, createExcelGenerator } from './useExcel.js'

// 示例数据
const sampleData = [
  ['一', '基础工程', '', '', '', '', ''],
  [1, '基础开挖', 'C30混凝土', '按设计图纸计算', 100, 'm³', '已完成'],
  [2, '基础浇筑', 'C30混凝土', '按设计图纸计算', 50, 'm³', '进行中'],
  ['二', '安装工程', '', '', '', '', ''],
  [1, '支架安装', '热镀锌钢材', '按设计图纸计算', 200, '套', '未开始'],
  [2, '组件安装', '单晶硅组件', '按设计图纸计算', 1000, '块', '未开始']
]

/**
 * 方式1: 使用类实例（适合需要自定义配置的场景）
 */
export async function useExcelWithClass() {
  const generator = new ExcelGenerator({
    title: '自定义标题 - 光伏工程量清单',
    worksheetName: '自定义工作表',
    columnWidths: [12, 35, 35, 45, 18, 18, 35]
  })

  try {
    await generator.generate(sampleData, '自定义文件名.xlsx')
    console.log('Excel文件生成成功')
  } catch (error) {
    console.error('生成失败:', error.message)
  } finally {
    generator.dispose() // 清理资源
  }
}

/**
 * 方式2: 使用工厂函数（推荐方式）
 */
export async function useExcelWithFactory() {
  const generator = createExcelGenerator({
    title: '工厂函数生成的Excel',
    headers: ['序号', '项目', '规格', '计算规则', '数量', '单位', '进度', '备注']
  })

  try {
    await generator.generate(sampleData)
  } finally {
    generator.dispose()
  }
}

/**
 * 方式3: 使用便捷函数（最简单的方式）
 */
export async function useExcelWithHelper() {
  try {
    await generateExcel(sampleData, {
      filename: '便捷函数生成.xlsx',
      title: '使用便捷函数生成的Excel'
    })
    console.log('Excel文件生成成功')
  } catch (error) {
    console.error('生成失败:', error.message)
  }
}

/**
 * Vue 3 Composition API 中的使用示例
 */
export function useExcelExport() {
  const { ref, computed } = require('vue')
  
  const isExporting = ref(false)
  const exportError = ref(null)

  const exportToExcel = async (data, options = {}) => {
    if (isExporting.value) {
      console.warn('正在导出中，请稍候...')
      return
    }

    isExporting.value = true
    exportError.value = null

    try {
      // 数据验证
      if (!ExcelGenerator.validateData(data)) {
        throw new Error('数据格式不正确')
      }

      await generateExcel(data, options)
    } catch (error) {
      exportError.value = error.message
      throw error
    } finally {
      isExporting.value = false
    }
  }

  const canExport = computed(() => !isExporting.value)

  return {
    isExporting: readonly(isExporting),
    exportError: readonly(exportError),
    canExport,
    exportToExcel
  }
}

/**
 * 错误处理示例
 */
export async function handleExportErrors() {
  try {
    // 错误的数据格式
    const invalidData = [
      ['一', '基础工程'], // 列数不匹配
      'invalid row', // 不是数组
      [1, 2, 3, 4, 5, 6, 7, 8] // 列数过多
    ]

    await generateExcel(invalidData)
  } catch (error) {
    console.error('预期的错误:', error.message)
  }
}
