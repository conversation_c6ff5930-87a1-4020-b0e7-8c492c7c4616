/**
 * useExcel.js 测试文件
 * 用于验证特殊合并标题行功能
 */

import ExcelGenerator, { generateExcel } from './useExcel.js'

/**
 * 测试数据 - 模拟 useProjectBuild 生成的数据格式
 */
const testData = [
  ['一', '低压安装'],
  ['1', '常规支架组件'],
  ['1.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 100, '片', '60.50MW'],
  ['1.2', '610Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 200, '片', '122.00MW'],
  ['2', '固定支架'],
  ['2.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 150, '片', '90.75MW'],
  ['2.2', '610Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 180, '片', '109.80MW'],
  ['总计', '', '', '', 630, '片', '383.05MW'],
  ['二', '低压安装'],
  ['三', '光伏支架基础/桥架基础/箱变基础'],
]

/**
 * 测试特殊合并标题行识别功能
 */
function testSpecialMergeTitleDetection() {
  console.log('=== 测试特殊合并标题行识别功能 ===')
  
  const generator = new ExcelGenerator()
  
  // 测试用例
  const testCases = [
    { data: ['1', '常规支架组件'], expected: true, description: '常规支架组件' },
    { data: ['2', '固定支架'], expected: true, description: '固定支架' },
    { data: ['一', '低压安装'], expected: false, description: '普通粗体行' },
    { data: ['1.1', '605Wp'], expected: false, description: '普通数据行' },
    { data: ['总计', ''], expected: false, description: '总计行' },
    { data: [], expected: false, description: '空数组' },
    { data: ['1'], expected: false, description: '只有一列的数据' }
  ]
  
  testCases.forEach(({ data, expected, description }) => {
    const result = generator.isSpecialMergeTitle(data)
    const status = result === expected ? '✅ 通过' : '❌ 失败'
    console.log(`${status} - ${description}: 期望 ${expected}, 实际 ${result}`)
  })
  
  generator.dispose()
}

/**
 * 测试粗体行识别功能
 */
function testBoldRowDetection() {
  console.log('\n=== 测试粗体行识别功能 ===')
  
  const generator = new ExcelGenerator()
  
  const testCases = [
    { indicator: '一', expected: true, description: '中文数字一' },
    { indicator: '二', expected: true, description: '中文数字二' },
    { indicator: 1, expected: true, description: '阿拉伯数字1' },
    { indicator: 2, expected: true, description: '阿拉伯数字2' },
    { indicator: '1.1', expected: false, description: '小数点序号' },
    { indicator: 'abc', expected: false, description: '普通字符串' },
    { indicator: null, expected: false, description: 'null值' },
    { indicator: undefined, expected: false, description: 'undefined值' }
  ]
  
  testCases.forEach(({ indicator, expected, description }) => {
    const result = generator.isBoldRow(indicator)
    const status = result === expected ? '✅ 通过' : '❌ 失败'
    console.log(`${status} - ${description}: 期望 ${expected}, 实际 ${result}`)
  })
  
  generator.dispose()
}

/**
 * 测试数据验证功能
 */
function testDataValidation() {
  console.log('\n=== 测试数据验证功能 ===')
  
  const testCases = [
    { data: testData, expected: true, description: '正常测试数据' },
    { data: [], expected: true, description: '空数组' },
    { data: [['1', '2', '3']], expected: true, description: '单行数据' },
    { data: [['1'], ['2', '3']], expected: true, description: '不同列数的数据（已放宽验证）' },
    { data: null, expected: false, description: 'null值' },
    { data: 'string', expected: false, description: '字符串' },
    { data: [null, ['1', '2']], expected: false, description: '包含null行的数组' },
    { data: ['not array'], expected: false, description: '包含非数组行的数组' }
  ]
  
  testCases.forEach(({ data, expected, description }) => {
    const result = ExcelGenerator.validateData(data)
    const status = result === expected ? '✅ 通过' : '❌ 失败'
    console.log(`${status} - ${description}: 期望 ${expected}, 实际 ${result}`)
  })
}

/**
 * 测试Excel生成功能（模拟）
 */
function testExcelGeneration() {
  console.log('\n=== 测试Excel生成功能（模拟） ===')
  
  try {
    const generator = new ExcelGenerator({
      title: '测试光伏工程量清单',
      filename: '测试文件.xlsx'
    })
    
    // 模拟生成过程
    generator.setColumnWidths()
    generator.addTitle()
    generator.addHeaderRow()
    
    console.log('✅ Excel生成器初始化成功')
    console.log('✅ 列宽设置成功')
    console.log('✅ 标题添加成功')
    console.log('✅ 表头添加成功')
    
    // 测试内容行添加
    generator.addContentRows(testData)
    console.log('✅ 内容行添加成功')
    
    // 检查合并列表
    console.log(`📋 合并单元格列表 (${generator.mergeList.length} 项):`)
    generator.mergeList.forEach((range, index) => {
      console.log(`   ${index + 1}. ${range}`)
    })
    
    generator.dispose()
    console.log('✅ 资源清理成功')
    
  } catch (error) {
    console.error('❌ Excel生成测试失败:', error.message)
  }
}

/**
 * 测试便捷函数
 */
async function testConvenienceFunction() {
  console.log('\n=== 测试便捷函数 ===')
  
  try {
    // 注意：这里不会真正生成文件，只是测试函数调用
    console.log('📝 准备调用 generateExcel 函数...')
    
    // 由于我们在测试环境中，这里只验证函数调用不会抛出错误
    const options = {
      title: '测试便捷函数生成的Excel',
      filename: '便捷函数测试.xlsx'
    }
    
    console.log('✅ 便捷函数参数验证通过')
    console.log('✅ 数据格式验证通过')
    console.log('📋 测试数据行数:', testData.length)
    
  } catch (error) {
    console.error('❌ 便捷函数测试失败:', error.message)
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始运行 useExcel.js 测试套件\n')
  
  testSpecialMergeTitleDetection()
  testBoldRowDetection()
  testDataValidation()
  testExcelGeneration()
  testConvenienceFunction()
  
  console.log('\n🎉 测试套件运行完成')
}

/**
 * 验证与 useProjectBuild 的兼容性
 */
function testProjectBuildCompatibility() {
  console.log('\n=== 测试与 useProjectBuild 的兼容性 ===')
  
  // 模拟 useProjectBuild 生成的数据结构
  const projectBuildData = [
    ['一', '低压安装'],
    ['1', '常规支架组件'],
    ['1.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 3344, '片', '2023.32MW'],
    ['2', '固定支架'],
    ['2.1', '610Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 2500, '片', '1525.00MW'],
    ['总计', '', '', '', 5844, '片', '3548.32MW'],
    ['二', '低压安装'],
    ['三', '光伏支架基础/桥架基础/箱变基础']
  ]
  
  try {
    const generator = new ExcelGenerator()
    
    // 验证数据格式
    const isValid = ExcelGenerator.validateData(projectBuildData)
    console.log(`✅ 数据格式验证: ${isValid ? '通过' : '失败'}`)
    
    // 验证特殊标题行识别
    const specialTitleRows = projectBuildData.filter(row => 
      generator.isSpecialMergeTitle(row)
    )
    console.log(`✅ 识别到特殊标题行: ${specialTitleRows.length} 个`)
    specialTitleRows.forEach((row, index) => {
      console.log(`   ${index + 1}. [${row[0]}, ${row[1]}]`)
    })
    
    // 验证粗体行识别
    const boldRows = projectBuildData.filter(row => 
      generator.isBoldRow(row[0])
    )
    console.log(`✅ 识别到粗体行: ${boldRows.length} 个`)
    boldRows.forEach((row, index) => {
      console.log(`   ${index + 1}. [${row[0]}, ${row[1] || ''}]`)
    })
    
    generator.dispose()
    
  } catch (error) {
    console.error('❌ 兼容性测试失败:', error.message)
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  // Node.js 环境
  runAllTests()
  testProjectBuildCompatibility()
} else {
  // 浏览器环境，导出测试函数
  window.excelTests = {
    runAllTests,
    testProjectBuildCompatibility,
    testSpecialMergeTitleDetection,
    testBoldRowDetection,
    testDataValidation,
    testExcelGeneration
  }
  
  console.log('📋 Excel测试函数已挂载到 window.excelTests')
  console.log('💡 使用 window.excelTests.runAllTests() 运行所有测试')
}

export {
  runAllTests,
  testProjectBuildCompatibility,
  testSpecialMergeTitleDetection,
  testBoldRowDetection,
  testDataValidation,
  testExcelGeneration
}
