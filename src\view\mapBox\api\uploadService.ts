// 获取API基础URL，从axios.js导入
import instance, { API_URLS } from "../utils/axios.js";
//@ts-ignore
// import instance from "@/http/index";
/**
 * 上传配置接口
 */
export interface UploadConfig {
  enableChunks: boolean;
  enableResume: boolean;
  enableFastUpload: boolean;
  chunkSize: number;
  projectCode?: string; // 项目编码
}

// 后端API响应接口
interface ApiResponse<T> {
  api_status: boolean;
  code: number;
  message: string;
  data: T;
}

export interface InitResponse {
  fileCode: string;
  name: string;
  chunkCount: number;
  hash: string;
  projectCode: string;
  path: string;
  type: string;
  status: string;
}

export interface StatusResponse {
  uploadedChunks: number[];
  status: string;
  fileCode?: string;
  path?: string;
  projectCode?: string;
  hash?: string;
  chunkCount?: number;
  name?: string; // 添加文件名属性
}

/**
 * 大文件分片上传响应接口
 */
export interface ChunkResponse {
  fileCode: string;
  chunkIndex: string;
  hash: string;
  projectCode: string;
  path: string;
  status: string;
}

// 全局控制器，用于取消请求
let controller: AbortController | null = null;

/**
 * 从文件名获取文件类型
 * @param filename 文件名
 * @returns string 文件类型
 */
function getFileTypeFromName(filename: string): string {
  const extension = filename.split(".").pop()?.toLowerCase() || "";
  if (["dwg", "dxf"].includes(extension)) {
    return "DWG";
  } else if (["tif"].includes(extension)) {
    return "TIF";
  }
  return extension.toUpperCase();
}

/**
 * 初始化大文件上传
 * @param file 文件对象
 * @param hash 文件哈希
 * @param fileType 文件类型
 * @param projectCode 项目编码
 * @param chunkSize 分片大小(MB)
 * @returns Promise<InitResponse | null>
 */
export async function initUpload(
  file: File,
  hash: string,
  fileType: string,
  projectCode: string,
  chunkSize: number
): Promise<InitResponse | null> {
  if (!projectCode) {
    console.error("缺少项目编码");
    return null;
  }

  // 确保hash不为空
  const effectiveHash = hash || generateDefaultHash(file);

  try {
    // 记录实际发送的参数用于调试
    const params = {
      name: file.name,
      chunkCount: Math.ceil(file.size / (chunkSize * 1024 * 1024)),
      hash: effectiveHash,
      projectCode: projectCode,
      type: fileType.toUpperCase(),
    };

    console.log("实际调用/upload/init参数:", params);

    const response = await instance({
      url: `${API_URLS.VITE_MAP_BOX_UPLOAD_URL}/upload/init`,
      method: "post",
      data: params,
    });

    console.log("项目上传初始化--init响应:", response);

    // 使用类型断言处理响应
    const res = response as unknown as ApiResponse<InitResponse>;
    if (res && res.code === 200 && res.api_status) {
      return res.data;
    }
    return null;
  } catch (error) {
    console.error("初始化上传失败:", error);
    return null;
  }
}

/**
 * 生成大文件上传默认的哈希值，避免hash为空
 */
function generateDefaultHash(file: File): string {
  // 使用文件名、大小、修改时间和随机数生成备用哈希
  const randomPart = Math.random().toString(36).substring(2, 15);
  const timePart = new Date().getTime().toString(36);
  const filePart = `${file.name}-${file.size}-${file.lastModified}`.replace(
    /\W/g,
    ""
  );
  return `${filePart}-${timePart}-${randomPart}`;
}

/**
 * 获取大文件上传状态
 * @param projectCode 项目编码
 * @param fileCode 文件编码
 * @returns Promise<StatusResponse | null>
 */
export async function getUploadStatus(
  projectCode: string,
  fileCode: string
): Promise<StatusResponse | null> {
  if (!projectCode || !fileCode) {
    console.error("缺少项目编码或文件编码");
    return null;
  }

  try {
    const response = await instance({
      url: `${API_URLS.VITE_MAP_BOX_UPLOAD_URL}/upload/status`,
      method: "GET",
      params: {
        projectCode: projectCode,
        fileCode: fileCode,
      },
    });

    console.log("获取上传状态响应:", response);

    // 使用类型断言处理响应
    const res = response as unknown as ApiResponse<StatusResponse>;
    if (res && res.code === 200 && res.api_status) {
      // 确保返回的数据格式正确，包含所需的字段
      const data = res.data || {};

      // 如果返回的数据不是预期的StatusResponse格式，尝试适配
      if (data.status === undefined && res.data) {
        // 可能是直接返回了完整文件信息
        return {
          status: res.data.status || "process",
          uploadedChunks: res.data.uploadedChunks || [],
          fileCode: res.data.fileCode || fileCode,
          path: res.data.path || "",
          projectCode: res.data.projectCode || projectCode,
          hash: res.data.hash || fileCode,
          chunkCount: res.data.chunkCount || 0,
          name: res.data.name || "",
        };
      }

      return data;
    }
    return null;
  } catch (error) {
    console.error("获取上传状态失败:", error);
    return null;
  }
}

/**
 * 检查大文件，文件是否已存在（用于秒传功能）
 * @param hash 文件哈希
 * @param name 文件名
 * @param chunkCount 总分片数量
 * @param projectCode 项目编码
 * @returns Promise<{exists: boolean, fileCode?: string}>
 */
export async function initOrCheckFileExists(
  hash: string,
  name: string,
  chunkCount: number,
  projectCode: string
): Promise<{ exists: boolean; fileCode?: string }> {
  try {
    const response = await instance({
      url: `${API_URLS.VITE_MAP_BOX_UPLOAD_URL}/upload/init`,
      method: "post",
      data: {
        name: name,
        chunkCount: chunkCount,
        hash: hash,
        projectCode: projectCode,
        type: getFileTypeFromName(name).toUpperCase(),
      },
    });

    console.log("initOrCheckFileExists响应:", response);

    // 使用类型断言处理响应
    const res = response as unknown as ApiResponse<InitResponse>;
    if (res && res.code === 200 && res.api_status && res.data) {
      // 如果状态为success，说明文件已存在
      return {
        exists: res.data.status === "success",
        fileCode: res.data.fileCode,
      };
    }
    return { exists: false, fileCode: "" };
  } catch (error) {
    console.error("检查文件是否存在失败:", error);
    return { exists: false, fileCode: "" };
  }
}

/**
 * 大文件，上传单个分片
 * @param chunk 分片数据
 * @param chunkIndex 分片索引
 * @param hash 文件哈希
 * @param projectCode 项目编码
 * @param fileCode 文件唯一标识
 * @param onProgress 上传进度回调
 * @returns Promise<{success: boolean, data?: ChunkResponse}>
 */
export async function uploadChunk(
  chunk: Blob,
  chunkIndex: number,
  hash: string,
  projectCode: string,
  fileCode: string,
  onProgress?: (progress: number) => void
): Promise<{ success: boolean; data?: ChunkResponse }> {
  if (!projectCode || !fileCode) {
    console.error("缺少项目编码或文件编码");
    return { success: false };
  }

  // 确保hash不为空
  const effectiveHash = hash || fileCode || Date.now().toString(36);

  const formData = new FormData();
  formData.append("file", chunk);
  formData.append("projectCode", projectCode);
  formData.append("fileCode", fileCode);
  formData.append("chunkIndex", chunkIndex.toString());
  formData.append("hash", effectiveHash);

  try {
    if (!controller) {
      controller = new AbortController();
    }

    const response = await instance.post(
      `${API_URLS.VITE_MAP_BOX_UPLOAD_URL}/upload/chunk`,
      formData,
      {
        signal: controller.signal,
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total && onProgress) {
            const percent = Math.floor(
              (progressEvent.loaded / progressEvent.total) * 100
            );
            onProgress(percent);
          }
        },
      }
    );

    // 使用类型断言处理响应
    const res = response as unknown as ApiResponse<ChunkResponse>;
    if (res && res.code === 200 && res.api_status) {
      console.log(`分片上传${chunkIndex}`, res);
      return {
        success: true,
        data: res.data,
      };
    }
    return { success: false };
  } catch (error: any) {
    if (error.name === "CanceledError" || error.name === "AbortError") {
      console.log("分片上传已取消");
      return { success: false };
    }
    console.error(`分片 ${chunkIndex} 上传失败:`, error);
    return { success: false };
  }
}

/**
 * 取消上传
 */
export function cancelUpload(): void {
  if (controller) {
    controller.abort();
    controller = null;
  }
}
