import { defaultTextRendererOpiton } from "../../constant"
import { CharShape } from "./CharShape"
import { Font } from "./font"
import { TextBlock } from "./textBlock"
import { TextBox } from "./textBox"
import { Shx, Text, MText, CommonProperty, LineSpacingStyle, AttachmentPoint, ColumnType } from "./shxparser.js";
import { EntityType } from "../../constant"
import { Entity } from "../Entity"
import { Vector2 } from "three"
import opentype from "opentype.js"
const ttfFallback = "simsun.ttf"
const shxFallback = "tssdeng.shx"
const shxBigFontFallback = "tssdchn.shx"
/**
 * Helper class for rendering text.
 * Currently it is just basic very simplified implementation for MVP. Further work should include:
 *  * Support DXF text styles and weight.
 *  * Bitmap fonts generation in texture atlas for more optimal rendering.
 */
export class TextRenderer {

    /**
     * @param fontFetchers {?Function[]} List of font fetchers. Fetcher should return promise with
     *  loaded font object (opentype.js). They are invoked only when necessary. Each glyph is being
     *  searched sequentially in each provided font.
     * @param options {?{}} See TextRenderer.DefaultOptions.
     */
    public fontFetchers
    public fonts
    public options
    public shapes
    public stubShapeLoaded: boolean = false
    public stubShape
    static _instance: TextRenderer | undefined = undefined
    static ttffileDataMap = new Map<string, Font>()
    static fontfilesSet = new Set<string>()
    static get instance() {
        (!this._instance) && (this._instance = new TextRenderer())
        return this._instance
    }
    constructor() {

    }

    isShxFile(filename: string) {
        return filename.endsWith(".shx") || !filename.includes(".")
    }

    async fetchAndParse(filename: string) {
        try {
            const url = "/font/" + filename
            const response = await fetch(url)
            if (response.ok) {
                const data = await response.arrayBuffer()
                if (data && data.byteLength !== 0) {
                    if (this.isShxFile(filename)) {
                        const dataview = new DataView(data)
                        await Shx.parse(filename, dataview)
                    } else {
                        const opentypefont = opentype.parse(data)
                        const font = new Font(opentypefont)
                        TextRenderer.ttffileDataMap.set(filename, font)
                    }
                }
            }
        } catch (error) {
            console.log(error)
        }
    }

    public async init(dxf, options = null) {
       
        const gp = dxf.getDrawGroup()
        const basedata = gp.getDxfBaseData()
        const n = basedata.textStyleCount()
        for (let i = 0; i < n; i++) {
            const textStyle = basedata.getTextStyle(i)
            const fontname = textStyle.getFontName()
            const stylename = textStyle.getStyleName()
            let fontFileName = textStyle.getPrimaryFontFile()
            const bigfontFileName = textStyle.getBigFontFile()
            let fontFileNameL = fontFileName.toLowerCase()
            let bigfontFileNameL = bigfontFileName.toLowerCase()
            if (fontFileNameL !== '' && !fontFileNameL.includes(".")) {
                fontFileNameL = fontFileNameL + ".shx"
            }

            if (!this.isShxFile(fontFileNameL)) {
                fontFileNameL = shxFallback
            }
            TextRenderer.fontfilesSet.add(fontFileNameL)
            if (bigfontFileName) {
                if (!bigfontFileNameL.includes(".")) {
                    bigfontFileNameL = bigfontFileNameL + ".shx"
                }
                TextRenderer.fontfilesSet.add(bigfontFileNameL)
            }

        }
        await this.initDefault()
    }

    async initDefault(){
                //fontfilesSet.add(ttfFallback)
                TextRenderer.fontfilesSet.add(shxFallback)
                TextRenderer.fontfilesSet.add(shxBigFontFallback)
                const fetchdata: Promise<void>[] = []
                for (let fontfile of TextRenderer.fontfilesSet) {
                    if(!Shx.hasFont(fontfile)){
                        const ret = this.fetchAndParse(fontfile)
                        fetchdata.push(ret)
                    }
                }
        
                await Promise.all(fetchdata)
        
                //this.fontFetchers = fontFetchers
                // this.fonts = []
        
                // this.options = Object.create(defaultTextRendererOpiton)
                // if (options) {
                //     Object.assign(this.options, options)
                // }
                // /* Indexed by character, value is CharShape. */
                this.shapes = new Map()
                // this.stubShapeLoaded = false
                // /* Shape to display if no glyph found in the specified fonts. May be null if fallback
                //  * character can not be rendered as well.
                //  */
                // this.stubShape = null
                // this.initFonts()
    }

    get canRender() {
        return this.fonts !== null && this.fonts.length > 0
    }

    /** Get width in model space units for a single line of text.
     * @param text {string}
     * @param fontSize {number}
     */
    // GetLineWidth(text, fontSize) {
    //     const block = new TextBlock(fontSize)
    //     for (const char of text) {
    //         const shape = this._GetCharShape(char)
    //         if (!shape) {
    //             continue
    //         }
    //         block.PushChar(char, shape)
    //     }
    //     return block.GetCurrentPosition()
    // }
    getFontName(fontFileName:string, bigfontFileName:string){
        let fontFileNameL = fontFileName.toLowerCase()
        let bigfontFileNameL = bigfontFileName.toLowerCase()

        if (fontFileNameL === '') {//解析的数据可能有问题，提供的是""
            fontFileNameL = shxFallback
        }
        if (bigfontFileNameL === '') {//解析的数据可能有问题，提供的是""
            bigfontFileNameL = shxBigFontFallback
        }
        if (!fontFileNameL.includes('.')) {
            fontFileNameL = fontFileNameL + ".shx"
        }
        if (!bigfontFileNameL.includes('.')) {
            bigfontFileNameL = bigfontFileNameL + ".shx"
        }
        if (!this.isShxFile(fontFileNameL)) {
            fontFileNameL = shxFallback
        }

        if (!Shx.hasFont(fontFileNameL)) {
            fontFileNameL = shxFallback
        }

        if (!Shx.hasFont(bigfontFileNameL)) {
            bigfontFileNameL = shxBigFontFallback
        }
        return {fontFileNameL, bigfontFileNameL}
    }
    /**
     * @param text {string}
     * @param startPos {{x,y}}
     * @param endPos {?{x,y}} TEXT group second alignment point.
     * @param rotation {?number} Rotation attribute, deg.
     * @param widthFactor {?number} Relative X scale factor (group 41)
     * @param hAlign {?number} Horizontal text justification type code (group 72)
     * @param vAlign {?number} Vertical text justification type code (group 73).
     * @param color {number}
     * @param layer {?string}
     * @param fontSize {number}
     * @return {Generator<Entity>} Rendering entities. Currently just indexed triangles for each
     *  glyph.
     */
    *Render({ text, fontFileName, bigfontFileName, pos, alignPoint, rotation = 0, widthFactor = 1, hAlign = 0, vAlign = 0,
        color, layer = undefined, fontSize, entityId, rgb = [1, 1, 1], colorMod = 0 }) {

        const lineType = 0
        const {fontFileNameL, bigfontFileNameL} = this.getFontName(fontFileName, bigfontFileName)

        if (this.isShxFile(fontFileNameL)) { //渲染shx
            const counts = new Array<number>()
            const points = new Array<Vector2>()
            //const alignmentPos = new Vector2(alignPoint.x, alignPoint.y)
            const position = new Vector2(pos.x, pos.y)
            CommonProperty.setTextHeight(fontSize)
            CommonProperty.setWidthFactor(widthFactor)
            CommonProperty.setHorizontalMode(hAlign as TextHorzModeValues)
            CommonProperty.setVerticalMode(vAlign as TextVertModeValues)
            CommonProperty.setRotation(rotation)
            CommonProperty.setOblique(0)


            CommonProperty.setFont(fontFileNameL, bigfontFileNameL)
            // console.log("text :"+text)
            // console.log("fontFileNameL :"+fontFileNameL)
            // console.log("bigfontFileNameL :"+bigfontFileNameL)
            // const outstr = String.fromCharCode(...text)
            // if(outstr === "注册执业印章"){
            //     console.log("pos",pos)
            //     console.log("alignPoint",alignPoint)
            //     debugger
            // }

            Text.outputPolylines(text, position, counts, points)
            const n = counts.length - 1;

            // let data = ""
            // for (let i = 0; i < n; i++) {
            //   const s = counts[i];
            //   const e = counts[i + 1];
            //   for (let j = s; j < e; j++) {
            //     const pt = points[j];
            //     data += (pt.x + ","  + pt.y + '\n');
            //     if(isNaN(pt.x) ||isNaN(pt.y)||pt.x === Infinity|| pt.y === Infinity)
            //         debugger

            //   }
            //   data += '\n'
            // }
            // console.log(data)

            for (let i = 0; i < n; i++) {
                const s = counts[i];
                const e = counts[i + 1];
                const vertices: number[] = [];

                for (let j = s; j < e; j++) {
                    const pt = points[j];
                    vertices.push(pt.x, pt.y)
                }
                yield new Entity({
                    type: EntityType.POLYLINE,
                    vertices, layer, color, lineType,
                    entityId: entityId,
                    shape: false,
                    rgb,
                    colorMod
                })

            }
        } else {//渲染opentype
            const block = new TextBlock(fontSize, entityId)
            let font = TextRenderer.ttffileDataMap.get(fontFileNameL)
            if (!font) {
                font = TextRenderer.ttffileDataMap.get(ttfFallback)
            }
            if (!font)
                return
            for (let char of text) {
                if (!font.HasChar(char)) {
                    font = TextRenderer.ttffileDataMap.get(ttfFallback)
                    if (!font.HasChar(char)) {
                        char = String.fromCodePoint(0x25A1)
                    }

                }
                let shape = this.shapes.get(char)
                if (!shape) {
                    const path = font.GetCharPath(char)
                    if (path) {
                        shape = new CharShape(font, path, this.options)
                    }
                    this.shapes.set(char, shape)
                }

                block.PushChar(char, shape)
            }

            yield* block.Render(pos, alignPoint, rotation, widthFactor, hAlign, vAlign, color, layer)

        }
    }

    /**
     * @param {MTextFormatEntity[]} formattedText Parsed formatted text.
     * @param {{x, y}} position Insertion position.
     * @param {Number} fontSize
     * @param {?Number} width Text block width, no wrapping if undefined.
     * @param {?Number} rotation Text block rotation in degrees.
     * @param {?{x, y}} direction Text block orientation defined as direction vector. Takes a
     * precedence over rotation if both provided.
     * @param {number} attachment Attachment point, one of MTextAttachment values.
     * @param {?number} lineSpacing Line spacing ratio relative to default one (5/3 of font size).
     * @param {number} color
     * @param {?string} layer
     * @return {Generator<Entity>} Rendering entities. Currently just indexed triangles for each
     *  glyph.
     */
    *RenderMText({ formattedText,layer, fontFileName, bigfontFileName, textHeight, pos,
        angle, attachmentPoint, widFactor, columnWidth, lineSpacingStyle, lineSpacingFactor, color, entityId, rgb,colorMod = 0 }) {
        const {fontFileNameL, bigfontFileNameL} = this.getFontName(fontFileName, bigfontFileName)
        CommonProperty.setTextHeight(textHeight);
        CommonProperty.setWidthFactor(widFactor)
        CommonProperty.setFont(fontFileNameL, bigfontFileNameL)
        CommonProperty.setRotation(angle)

        //MText
        //MText.setColumnType(ColumnType.kNoColumns)
        //MText.setHeight(0)
        MText.setColumnWidth(columnWidth)
        MText.setLineSpacingStyle(lineSpacingStyle)
        MText.setLineSpacingFactor(lineSpacingFactor)
        MText.setAttachment(attachmentPoint)
        const counts = new Array<number>()
        const points = new Array<Vector2>()
        MText.outputMTextPolylines(formattedText, pos, counts, points)
        const lineType = 0
        // const layer = undefined
        const n = counts.length - 1;
        for (let i = 0; i < n; i++) {
            const s = counts[i];
            const e = counts[i + 1];
            const vertices: number[] = [];

            for (let j = s; j < e; j++) {
                const pt = points[j];
                vertices.push(pt.x, pt.y)
            }

            yield new Entity({
                type: EntityType.POLYLINE,
                vertices, layer, color, lineType,
                entityId: entityId,
                shape: false,
                rgb,
                colorMod
            })
        }
    }
}


export const textRender = TextRenderer.instance