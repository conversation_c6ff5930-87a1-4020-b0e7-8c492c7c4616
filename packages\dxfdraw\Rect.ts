import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"

export class Rect extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init() 
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        let p1=await this.lengthPick({
            tip:'指定第二个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p1)
        this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        let plist=this.getvertices([...this.pnts,v])
        this.mesh.geometry=new BufferGeometry().setFromPoints(plist);
    }
    
    complete(){
        let {x,y}=globalDxf.wcsOff
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        if(this.pnts.length<2) return
        let plist=this.getvertices(this.pnts)
        const wasmVPlist=this.setWasmVPlist(plist)
        let wasmBulge = new sg.vector_double()
        let  pl=new sg.PolyLine2d(wasmVPlist, wasmBulge, false)
        let  plobj=new sg.SGObjComBinCurve()
        plobj.setpCurve(pl)
        let [r,g,b]=this.color
        plobj.setColor(new sg.SGColor(r,g,b,255))
        plobj.setLineType(this.lineType)

        this.viewer.transaction.add(plobj)
        globalDxf.operation=GraphicType.None
        globalDxf.isdraw = false
        this.isSnap=false
        setTimeout(()=>{
            globalDxf.drawstate=DrawState.Pick
        })
    }

    private getvertices(vlist) {
        const pointA = vlist[0]
		const pointC = vlist[1]
		const pointB = new Vector3(pointC.x, pointA.y, pointA.z);
		const pointD = new Vector3(pointA.x, pointC.y, pointC.z);
		// 定义矩形的所有顶点
		const vertices = [pointA,pointB,pointC,pointD,pointA];
        return vertices
    }
    private setWasmVPlist(vList){
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x, item.y))
        })
        return wasmVPlist
    }
}