import { DrawHelp } from "../dxfdraw/drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState,pickupInfo } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial,Group, Vector3,Line as ThLine } from "three"
import { PubSub } from 'emitter';

export class Scale extends DrawHelp {
    public pnts:Vector3[]=[]
    public ADgp:any
    public draw:boolean=true

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        PubSub.default.sub("slidentifyclear", this.clear.bind(this));
        this.init()
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        let p1=await this.lengthPick({
            tip:'指定下一个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p1)
        let p2=await this.lengthPick({
            tip:'指定下一个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p2)
        let p3=await this.lengthPick({
            tip:'指定下一个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p3)
        // this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length==1){
            let secpt = this.getEventPoint(event)
            this.updateLine(secpt)
        }else if(this.pnts.length==2) {
            let secpt = this.getEventPoint(event)
            this.updateAlignDim(secpt)
        }
        return true;
    }


    private updateAlignDim(v) {
        let {x,y}=globalDxf.wcsOff
        this.ADgp=new sg.SGObjAlignDim()
        const startPoint = new sg.Point2d(this.pnts[0].x+x, this.pnts[0].y+y)
        this.ADgp.setStartPoint(startPoint)
        const endPoint = new sg.Point2d(this.pnts[1].x+x, this.pnts[1].y+y)
        this.ADgp.setEndPoint(endPoint)
        const dimPoint = new sg.Point2d(v.x+x, v.y+y)
        this.ADgp.setDimensionPos(dimPoint)

        this.viewer.transientSceneWasm.clear()
        this.viewer.transientSceneWasm.draw(this.ADgp)
    }

    private updateLine(v) {
        let {x,y}=globalDxf.wcsOff
        const startPoint = new sg.Point2d(this.pnts[0].x+x, this.pnts[0].y+y)
        const endPoint= new sg.Point2d(v.x+x, v.y+y)
        const line = new sg.LineSegment2d(startPoint, endPoint)
        let gp=new sg.SGObjCurve()
        gp.setpCurve(line)
        this.viewer.transientSceneWasm.clear()
        this.viewer.transientSceneWasm.draw(gp)
    }


    setV(v:Vector3){
        this.pnts.push(v)
        if(this.pnts.length==3){
            this.complete()
        }
    }

    
    public complete(){
        poptip.dispose()
        this.dispose()
        if(this.pnts.length<3) return
        let dl=sg.SGObjTool.convertToAlignDim(this.ADgp)
        let l=dl.getDimensionText(1)
        PubSub.default.pub("sl_measure_scale",l);
        this.viewer.transientSceneWasm.clear()

        globalDxf.isdraw = false
        this.isSnap=false

        setTimeout(()=>{
            globalDxf.operation=GraphicType.None
            globalDxf.drawstate=DrawState.Pick
        })
    }
    clear(){
        this.pnts=[]
        poptip.dispose()
        this.viewer.transientSceneWasm.clear()
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
    }

}

