<template>
    <div class="rightClickMenu" :style="{ left: position.x + 'px', top: position.y + 'px' }">
        <div v-for="item in buttonList">
            <el-button type="success" class="btn" :color="item.color" @click="btnclick(item)">{{ item.text }}</el-button>
        </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref,onMounted,nextTick,reactive } from 'vue'

  const emits = defineEmits(['rightClickMenuconfirm'])
  const props = defineProps({
    buttonList:{
        type:Array,
        default:[]
    },
    position:{
      type:Object,
      default:{
        x:'',
        y:''
      }
    }
  })

  onMounted(()=>{

  })

  const  btnclick=async (item)=>{
    if(item.text==='取消') {
      item.fn()
    }else {
      emits('rightClickMenuconfirm')
    }
    
  }


  defineExpose({

  })
  </script>
  
  <style lang="scss" scoped>
  .rightClickMenu {
    position: absolute;
    top: 300px;
    left: 500px;
    background-color: #fff;
    z-index: 1;
    padding: 10px;
    .btn {
      margin: 5px;
    }
  }
  </style>
  
  