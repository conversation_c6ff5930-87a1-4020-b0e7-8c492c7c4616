export type NativeTypedArray=
    Int8Array|
    Uint8Array|
    Uint8ClampedArray|
    Int16Array|
    Uint16Array|
    Int32Array|
    Uint32Array|
    Float32Array|
    Float64Array

    
export enum NativeType {
    INT8= 0,
    UINT8=  1,
    UINT8_CLAMPED=  2,
    INT16=  3,
    UINT16=  4,
    INT32=  5,
    UINT32=  6,
    INT64=  7,
    UINT64=  8,
    FLOAT32=  9,
    FLOAT64=  10
}


/** Get TypedArray type corresponding to the specified NativeType. */
export function NativeArray(type:NativeType) {
    switch (type) {
    case NativeType.INT8:
        return Int8Array
    case NativeType.UINT8:
        return Uint8Array
    case NativeType.UINT8_CLAMPED:
        return Uint8ClampedArray
    case NativeType.INT16:
        return Int16Array
    case NativeType.UINT16:
        return Uint16Array
    case NativeType.INT32:
        return Int32Array
    case NativeType.UINT32:
        return Uint32Array
    case NativeType.FLOAT32:
        return Float32Array
    case NativeType.FLOAT64:
        return Float64Array
    default:
        throw new Error("Unrecognized native type: " + type)
    }
}