import instance from './index'

let identifying1 = ''
let identifying2 = ''
let identifying3 = ''

if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'staging' || process.env.NODE_ENV === 'production') {
    identifying1 = import.meta.env.VITE_DIGITAL_COMPONENT_BASE_URL
    identifying2 = import.meta.env.VITE_STANDARD_DATA_BASE_URL
    identifying3 = import.meta.env.VITE_UPLOAD_URL
} else {
    identifying2 = '/v2'
    identifying3 = '/v3'
}


//发起图纸导出任务
export function apiTask(data) {
    return instance({
        url: identifying1 + "/v1/cad/task",
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//获取任务列表
export function apiTaskList(params) {
    return instance({
        url: identifying1 + "/v1/cad/task",
        method: 'get',
        params,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//获取上传文件地址
export function apiQueryUploadFileUrl(data) {
    return instance({
        url: identifying3 + "/v1/storage/presignedUrl/upload",
        method: 'POST',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//获取任务详情
export function apiTaskDetail(id) {
    return instance({
        url: identifying1 + "/v1/cad/task/" + id,
        method: 'get',
        // headers:{
        //     'Content-Type':'application/json'
        // }
    })
}