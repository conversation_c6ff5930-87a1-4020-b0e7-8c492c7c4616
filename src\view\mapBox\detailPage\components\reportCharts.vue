
<template>
  <div
    class="report-charts-container"
    v-loading="loading"
    element-loading-text="加载中..."
    element-loading-background="rgba(122, 122, 122, 0.8)"
  >
    <div class="chart-item" v-for="(chart, index) in charts" :key="index">
      <div class="chart-title">{{ chart.title }}</div>
      <div
        class="chart-content"
        :id="`chart-${index}`"
        :ref="(el) => setChartRef(el, index)"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import useProjectBuild from "../hooks/useProjectBuild";
import {
  ProgressChartOption,
  PowerDistributionChartOption,
} from "./ChartOptions";

const { cadletaDetail } = useProjectBuild();
const loading = ref(true);

const charts = ref([
  {
    title: "组件安装进度",
    instance: null,
  },
  {
    title: "功率分布",
    instance: null,
  },
]);

const chartRefs = ref([]);

const setChartRef = (el, index) => {
  if (el) {
    chartRefs.value[index] = el;
  }
};

const initCharts = () => {
  charts.value.forEach((chart, index) => {
    if (chartRefs.value[index]) {
      chart.instance = echarts.init(chartRefs.value[index]);

      if (index === 0) {
        renderProgressChart(chart.instance);
      } else if (index === 1) {
        renderPowerDistributionChart(chart.instance);
      }
    }
  });
};

const renderProgressChart = (chartInstance) => {
  const option = ProgressChartOption(cadletaDetail.value);
  chartInstance.setOption(option);
};

const renderPowerDistributionChart = (chartInstance) => {
  const option = PowerDistributionChartOption(cadletaDetail.value);
  chartInstance.setOption(option);
};

const handleResize = () => {
  charts.value.forEach((chart) => {
    chart.instance && chart.instance.resize();
  });
};

onMounted(() => {
  watch(cadletaDetail, () => {
    initCharts();
    loading.value = false;
  });
  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  charts.value.forEach((chart) => {
    chart.instance && chart.instance.dispose();
  });
});
</script>

<style scoped>
.report-charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: absolute;
  right: 20px;
  top: 50px;
  /* background: red; */
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 10px;
  z-index: 1000;
}

.chart-item {
  flex: 1;
  min-width: 320px;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.chart-content {
  height: 280px;
}
</style>
