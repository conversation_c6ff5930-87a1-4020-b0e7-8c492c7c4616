<template>
    <div class="head">
        <div class="left">
          <el-image style="height: 25px;" src="img/home.png" fit="fill" />
          <div class="top">
            <svg-icon iconClass="hdaoru" w=40 h=40 class="icon zpointer" @click="btnUpload" style="padding-top:0px;"></svg-icon>
            <svg-icon iconClass="hdaochu" w=40 h=40 class="icon zpointer" @click="btnExport" style="padding-top:0px;margin-left:10px;"></svg-icon>
            <svg-icon iconClass="helpdoc" w=40 h=40 class="icon zpointer" @click="popHelpDoc" style="padding-top:0px;margin-left:10px;"></svg-icon>

          </div>
          <div class="bottom"></div>
        </div>
        <div class="row">
          <div class="title">
            <slot name="title">SungrowCAD</slot>
          </div>
          <div class="filenam" v-if="globalDxf.filenam!=''">文件名:{{ globalDxf.filenam }}</div>
        </div>
        <div class="right" v-if="exportPageIsShow">
          <svg-icon iconClass="hliebiaoye" w=30 h=30 class="icon zpointer" @click="skip" style="padding-top:0px;"></svg-icon>
        </div>
    </div>
  </template>
  
  <script setup>
  import {LoadDxf} from 'sgdraw/util/fileUpload'
  import { useRouter } from "vue-router";
  import {mitts,btnFnObj} from 'sgdraw/mittBus'
  import {globalDxf} from 'dxf-viewer'
  const router = useRouter();
  let loadDxf=new LoadDxf()
  const props = defineProps({
    exportPageIsShow:{
        type: Boolean,
        default:true
    }
  })

  const skip=()=>{
    router.push({
        path:'/exportCenter'
    })
  }

  const btnUpload=async()=>{
    loadDxf.upload()
  }

  const btnExport=()=>{
    mitts.emit(btnFnObj.export)
  }
  const popHelpDoc=()=>{}
  </script>
  
  <style lang="scss" scoped>
  .head {
    width: 100%;
    height: 80px;
    background-color: #212832;
    padding: 3px 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .left {
      height: 100%;
      display: flex;
      align-items: center;
      .top {
        display: flex;
        margin-left: 15px;
      }
    }
    .row {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);
    }
    .title {
      font-size: 20px;
      color: aliceblue;
    }
    .filenam{
      font-size: 14px;
      color: aliceblue;
      position: absolute;
      transform: translate(-50%,-50% );
      bottom:-10px;
    }
  }
  </style>
  
  