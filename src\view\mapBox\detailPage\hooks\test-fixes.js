/**
 * 测试修复的功能
 * 1. 总计计算修复
 * 2. 左对齐样式修复
 */

import useProjectBuild from './useProjectBuild.js'
import { generateExcel } from './useExcel.js'

/**
 * 测试总计计算功能
 */
export function testTotalCalculation() {
  console.log('=== 测试总计计算功能 ===')
  
  // 模拟Excel行数据
  const testExcelRows = [
    ['1.1', '605Wp', '规格1', '计算规则', 1000, '片', '605.00MW'],
    ['1.2', '610Wp', '规格2', '计算规则', 2000, '片', '1220.00MW'],
    ['2.1', '605Wp', '规格3', '计算规则', 1500, '片', '907.50MW'],
    ['2.2', '610Wp', '规格4', '计算规则', 500, '片', '305.00MW']
  ]
  
  // 手动计算期望值
  const expectedTotal = 1000 + 2000 + 1500 + 500 // 5000
  const expectedWeightedTotal = (1000 * 605 + 2000 * 610 + 1500 * 605 + 500 * 610) / (10 ** 6)
  // = (605000 + 1220000 + 907500 + 305000) / 1000000 = 3037500 / 1000000 = 3.0375
  
  console.log('📊 测试数据:')
  testExcelRows.forEach((row, index) => {
    console.log(`   ${index + 1}. ${row[1]} × ${row[4]} = ${Number(row[1].replace('Wp', '')) * row[4]}`)
  })
  
  console.log(`📈 期望结果:`)
  console.log(`   总数量: ${expectedTotal}`)
  console.log(`   加权总计: ${expectedWeightedTotal.toFixed(2)}MW`)
  
  // 这里我们无法直接测试 calculateTotalsFromExcelRows 因为它是私有方法
  // 但我们可以通过集成测试来验证
  console.log('✅ 总计计算测试数据准备完成')
}

/**
 * 测试左对齐功能
 */
export function testLeftAlignment() {
  console.log('\n=== 测试左对齐功能 ===')
  
  const testData = [
    ['一', '低压安装'],                    // 应该左对齐
    ['1', '常规支架组件'],                 // 特殊合并，居中
    ['1.1', '605Wp', '规格', '计算规则', 100, '片', '60.50MW'],
    ['二', '光伏支架(固定轴)'],            // 应该左对齐
    ['2', '固定支架'],                     // 特殊合并，居中
    ['2.1', '610Wp', '规格', '计算规则', 200, '片', '122.00MW'],
    ['三', '光伏支架基础/桥架基础/箱变基础'] // 应该左对齐
  ]
  
  console.log('📋 测试数据分析:')
  testData.forEach((row, index) => {
    const projectName = row[1]
    const leftAlignTitles = ['低压安装', '光伏支架(固定轴)', '光伏支架基础/桥架基础/箱变基础']
    const specialMergeTitles = ['常规支架组件', '固定支架']
    
    let alignment = '居中'
    if (leftAlignTitles.includes(projectName)) {
      alignment = '左对齐'
    } else if (specialMergeTitles.includes(projectName)) {
      alignment = '居中(合并B:G)'
    }
    
    console.log(`   ${index + 1}. "${projectName}" -> ${alignment}`)
  })
  
  console.log('✅ 左对齐测试数据准备完成')
}

/**
 * 集成测试 - 生成Excel文件验证修复
 */
export async function integrationTest() {
  console.log('\n=== 集成测试 ===')
  
  const testData = [
    ['一', '低压安装'],
    ['1', '常规支架组件'],
    ['1.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 1000, '片', '605.00MW'],
    ['1.2', '610Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 2000, '片', '1220.00MW'],
    ['2', '固定支架'],
    ['2.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 1500, '片', '907.50MW'],
    ['2.2', '610Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 500, '片', '305.00MW'],
    ['总计', '', '', '', 5000, '片', '3037.50MW'],
    ['二', '光伏支架(固定轴)'],
    ['三', '光伏支架基础/桥架基础/箱变基础']
  ]
  
  try {
    console.log('📝 生成测试Excel文件...')
    
    await generateExcel(testData, {
      title: '修复测试 - 光伏工程量清单',
      filename: '修复测试_光伏工程量清单.xlsx'
    })
    
    console.log('✅ Excel文件生成成功')
    console.log('📋 请检查生成的Excel文件:')
    console.log('   1. "低压安装"、"光伏支架(固定轴)"、"光伏支架基础/桥架基础/箱变基础" 应该左对齐')
    console.log('   2. "常规支架组件"、"固定支架" 应该居中且B:G列合并')
    console.log('   3. 总计行的数量应该是 5000，形象进度应该是 3037.50MW')
    
  } catch (error) {
    console.error('❌ 集成测试失败:', error.message)
  }
}

/**
 * 验证useProjectBuild的总计计算
 */
export function validateProjectBuildTotals() {
  console.log('\n=== 验证 useProjectBuild 总计计算 ===')
  
  // 模拟CBComponent和FixedComponent数据
  const mockCBComponent = [
    ['1.1', '605Wp', '规格1', '计算规则', 1000, '片', '605.00MW'],
    ['1.2', '610Wp', '规格2', '计算规则', 2000, '片', '1220.00MW']
  ]
  
  const mockFixedComponent = [
    ['2.1', '605Wp', '规格3', '计算规则', 1500, '片', '907.50MW'],
    ['2.2', '610Wp', '规格4', '计算规则', 500, '片', '305.00MW']
  ]
  
  // 合并所有行
  const allRows = [...mockCBComponent, ...mockFixedComponent]
  
  // 手动计算总计
  let totalQuantity = 0
  let totalWeighted = 0
  
  allRows.forEach(row => {
    const quantity = Number(row[4]) || 0
    const powerStr = String(row[1] || '')
    const powerMatch = powerStr.match(/(\d+)Wp/i)
    const power = powerMatch ? Number(powerMatch[1]) : 0
    
    totalQuantity += quantity
    totalWeighted += quantity * power
  })
  
  const weightedTotalMW = totalWeighted / (10 ** 6)
  
  console.log('📊 手动计算结果:')
  console.log(`   总数量: ${totalQuantity}`)
  console.log(`   加权总计: ${weightedTotalMW.toFixed(2)}MW`)
  
  console.log('📋 详细计算:')
  allRows.forEach((row, index) => {
    const quantity = Number(row[4]) || 0
    const powerStr = String(row[1] || '')
    const powerMatch = powerStr.match(/(\d+)Wp/i)
    const power = powerMatch ? Number(powerMatch[1]) : 0
    const weighted = quantity * power
    
    console.log(`   ${index + 1}. ${powerStr} × ${quantity} = ${weighted}`)
  })
  
  console.log(`📈 总加权值: ${totalWeighted}`)
  console.log(`📈 转换为MW: ${totalWeighted} ÷ 1,000,000 = ${weightedTotalMW.toFixed(2)}MW`)
  
  console.log('✅ useProjectBuild 总计计算验证完成')
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始运行修复功能测试\n')
  
  testTotalCalculation()
  testLeftAlignment()
  validateProjectBuildTotals()
  
  console.log('\n🎯 接下来运行集成测试...')
  integrationTest()
  
  console.log('\n🎉 所有测试运行完成')
  console.log('\n📝 验证清单:')
  console.log('□ 检查生成的Excel文件中总计行的数量和形象进度是否正确')
  console.log('□ 检查"低压安装"等标题是否左对齐')
  console.log('□ 检查"常规支架组件"、"固定支架"是否正确合并B:G列')
}

// 如果在浏览器环境中，将测试函数挂载到全局对象
if (typeof window !== 'undefined') {
  window.fixTests = {
    testTotalCalculation,
    testLeftAlignment,
    integrationTest,
    validateProjectBuildTotals,
    runAllTests
  }
  
  console.log('📋 修复测试函数已挂载到 window.fixTests')
  console.log('💡 使用 window.fixTests.runAllTests() 运行所有测试')
}
