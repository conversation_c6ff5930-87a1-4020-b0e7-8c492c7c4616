import { GripPoint } from "./GripCommon";
import { GripDragReply } from "./GripDragReply";
import * as THREE from "three"

//生成直线夹点
export function createLineGripPoints(obj){
    let resPoints:GripPoint[] = []
    let tpts = obj.selectPoints()
    let size = tpts.size()
    for(let i = 0 ; i < size ; i++){
        let temp = tpts.get(i)
        let strType = temp.getString()    
        let index = 1
        
        if(strType == 'startPoint'){//起点
            index =1
        }
        else if(strType == 'endPoint'){//终点
            index =3
        }
        else{//中点
            index =2
        }
        let ppt1 = temp.getPoint()
        let gpoint = new GripPoint(ppt1,obj.getDrawObjectId(),index)
        resPoints.push(gpoint)
    }
    return resPoints
}


//拖拽线段
export class DragLineReply  extends GripDragReply{
    public oriLine:any
    public objCurve : any

    constructor(viewer,gpt) {
        super(viewer,gpt)
        let group = this.viewer.transaction.gpObj
        let obj = group.getPBaseObjById(this.gPoint.objId)
        this.objCurve = sg.SGObjTool.convertToCurve(obj)
        this.oriLine = this.objCurve.getLine()
    }
    
    //消息返回false表示可以往下路由，返回true表示在这个节点截止不再往下路由
    // 左键按下
    public override onLButtonDown( event ) : boolean{
        let mousePt = this.getEventPoint(event)
        let pt = this.threePointToSgPoint(mousePt)
        let group = this.viewer.transaction.gpObj
        group.getTransactionManager().beginTransaction()
        if(this.gPoint.gripIndex == 1)
        {
            this.objCurve.setSegmentStartPoint(pt)
        }
        else if(this.gPoint.gripIndex == 2)
        {
            this.objCurve.setSegmentCenterPoint(pt)
        }
        else{
            this.objCurve.setSegmentEndPoint(pt)
        }
        group.getTransactionManager().commit()
        super.onLButtonDown(event)
        return true;
    }
    //鼠标移动
    public override onMouseMove( event ) : boolean{
        this.clearTempGeometry()
        super.onMouseMove(event)
        let mousePt = this.getEventPoint(event)
        let material1 = new THREE.LineBasicMaterial({ color:'yellow'});
        let material2 = new THREE.LineBasicMaterial({ color:'grey'});
        if(this.gPoint.gripIndex == 1 || this.gPoint.gripIndex == 3)
        {//拖动起点或者终点，绘制临时图形
            let ptFix
            let otherpt
            if(this.gPoint.gripIndex == 1){
                ptFix = this.oriLine.endPoint()
                otherpt = this.oriLine.startPoint()
            }
            else{
                ptFix = this.oriLine.startPoint()
                otherpt = this.oriLine.endPoint()
            }       
            let pt2 = new THREE.Vector3(ptFix.x(),ptFix.y(),0)
            let geometry1 =new THREE.BufferGeometry().setFromPoints([mousePt,pt2]);          
            let line1 = new THREE.Line(geometry1, material1);
            this.tempGeometry.push(line1)

            let pt3 = new THREE.Vector3(otherpt.x(),otherpt.y(),0)
            let geometry2 =new THREE.BufferGeometry().setFromPoints([mousePt,pt3]);          
            let line2 = new THREE.Line(geometry2, material2);
            this.tempGeometry.push(line2)
        }
        else 
        {//拖动线段中点，绘制临时图形
            let ptFix = this.oriLine.midPoint()
            let pt3 = new THREE.Vector3(ptFix.x(),ptFix.y(),0)
            let geometry2 =new THREE.BufferGeometry().setFromPoints([mousePt,pt3]);          
            let line2 = new THREE.Line(geometry2, material2);
            this.tempGeometry.push(line2)

            let dx = mousePt.x - pt3.x
            let dy = mousePt.y - pt3.y
            let start = this.oriLine.startPoint()
            let end = this.oriLine.endPoint()
            let threeStart = new THREE.Vector3(start.x() + dx,start.y() + dy)
            let threeend = new THREE.Vector3(end.x() + dx,end.y() + dy)
            let geometry1=new THREE.BufferGeometry().setFromPoints([threeStart,threeend]);          
            let line1 = new THREE.Line(geometry1, material1);
            this.tempGeometry.push(line1)
        }
        this.showTempGeometry()
        return true
    }  
}
   

export function createLineDragReplyByGripPoint(gpoint,viewer){

    let res = new DragLineReply(viewer,gpoint)
    return res
}