<template>
    <el-dialog
        :model-value="elevationVisible"
        width="60%"
        :before-close="close"
    >
        <div @click="btncs" style="width: 100%;text-align: center;font-size: 20px;cursor: pointer;margin: 10px;">打开</div>
        <!-- width: calc(60vw - 20px); -->
        <div ref="threeContainer" style=" height: 55vh;"></div>
    </el-dialog>
</template>

<script setup>
    import * as THREE from 'three';
    import * as d3Delaunay from 'd3-delaunay';
    import { CSG } from 'three-csg-ts';
    import { pickupInfo,globalDxf, uploadFileAsText} from 'dxf-viewer'
    import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
    import { VertexNormalsHelper } from 'three/examples/jsm/helpers/VertexNormalsHelper.js';

    const emits = defineEmits([])
    const props = defineProps({
        // cskdPopVisible:{
        //     type: Boolean,
        //     default:false
        // }
    })
    const elevationVisible = ref(false)
    const threeContainer = ref(null)

    let scene, camera ,renderer,controls;

    onMounted(()=>{

    })
    const init=()=>{
        if(scene || camera) return
        nextTick(()=>{
            
            const container = threeContainer.value;
            const width = container.clientWidth;
            const height = container.clientHeight;
            // 创建场景、相机和渲染器
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000000);
            camera.position.z = 5;
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(width, height);
            container.appendChild(renderer.domElement);
            controls = new OrbitControls(camera, renderer.domElement);

            // 自定义鼠标操作
            controls.mouseButtons = {
                LEFT: THREE.MOUSE.ROTATE,   // 左键旋转
                MIDDLE: THREE.MOUSE.ZOOM,   // 中键缩放
                RIGHT: THREE.MOUSE.PAN      // 右键平移
            };
            // 可选设置项
            controls.enableDamping = true; // 开启阻尼效果
            controls.dampingFactor = 0.05; // 设置阻尼系数
            controls.minDistance = 1; // 最小缩放距离
            controls.maxDistance = 10000; // 最大缩放距离
            controls.panSpeed = 0.5;           // 平移速度
            controls.enablePan = true;         // 是否允许平移


            
            // 渲染循环
            function animate() {
                requestAnimationFrame(animate);
                renderer.render(scene, camera);
            }

            animate();

            // 窗口大小变化时更新相机和渲染器尺寸
            window.addEventListener('resize', () => {
                const width = container.clientWidth;
                const height = container.clientHeight;
                camera.aspect = width / height;
                camera.updateProjectionMatrix();
                renderer.setSize(width, height);
            });
            // cableMesh()
            // elevationVisible.value=false
        })
    }

    // const initThreeScene = () => {
    //     if (pickupInfo.gcdPointList.length === 0) return;
    //     const referencePoint = globalDxf.wcsOff; // 格式应为 [x, y] 或 { x, y }
    //     const points = pickupInfo.gcdPointList.map(p => [
    //         p[0] - referencePoint.x,
    //         p[1] - referencePoint.y,
    //         p[2]*20
    //          // z 高程保留不变
    //     ])
    //     // 提取 x, y 坐标用于三角剖分（Delaunay 不关心 z）
    //     const xyPoints = points.map(p => [p[0], p[1]]);
    //     const delaunay = d3Delaunay.Delaunay.from(xyPoints);

    //     // 准备顶点位置数组和索引数组
    //     const vertices = [];
    //     const indices = [];

    //     // 遍历每个三角形面
    //     for (let i = 0; i < delaunay.triangles.length; i += 3) {
    //         const aIndex = delaunay.triangles[i];   // 第一个点索引
    //         const bIndex = delaunay.triangles[i + 1]; // 第二个点索引
    //         const cIndex = delaunay.triangles[i + 2]; // 第三个点索引

    //         // 将这三个点的 x, y, z 添加进顶点数组
    //         vertices.push(...points[aIndex]); // 点 A
    //         vertices.push(...points[bIndex]); // 点 B
    //         vertices.push(...points[cIndex]); // 点 C

    //         // 当前添加的是第几个三角形？用来生成 index
    //         const base = (i / 3) * 3;
    //         indices.push(base, base + 1, base + 2);
    //     }
    //     // 创建 BufferGeometry
    //     const geometry = new THREE.BufferGeometry();

    //     // 设置顶点位置属性
    //     geometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(vertices), 3));

    //     // 设置索引（可选，有助于减少重复顶点）
    //     geometry.setIndex(new THREE.BufferAttribute(new Uint16Array(indices), 1));
    //     geometry.computeVertexNormals(); // 自动计算法线

    //     // 创建材质并添加到场景中
    //     const material = new THREE.MeshPhongMaterial({
    //         color: 0x00ff00,
    //         side: THREE.DoubleSide,
    //         flatShading: true, // 可选：开启平滑着色或 flatShading: true 看不同效果
    //         // wireframe: true, // 显示线框以便观察三角剖分效果
    //         // shininess: 100,
    //         // specular: 0xffffff
    //     });
    //     // 线框材质
    //     const lineMaterial = new THREE.MeshBasicMaterial({
    //         color: 0xff0000,
    //         wireframe: true,
    //         transparent: true,
    //         opacity: 0.8,
    //         depthWrite: false
    //     });

    //     // 环境光（整体照明）
    //     const ambientLight = new THREE.AmbientLight(0xffffff, 0.2); 
    //     scene.add(ambientLight);

    //     // 方向光（模拟太阳光）
    //     const directionalLight = new THREE.DirectionalLight(0xffffff, 2);
    //     directionalLight.position.set(5, 10, 5).normalize();
    //     directionalLight.castShadow = true; // 开启阴影（可选）
    //     scene.add(directionalLight);

    //     const mesh = new THREE.Mesh(geometry, material);
    //     // const wireframeMesh = new THREE.Mesh(geometry, lineMaterial);
    //     // scene.add(wireframeMesh);
    //     scene.add(mesh);

    //     // 计算模型包围盒
    //     const box = new THREE.Box3().setFromObject(mesh);
    //     const center = new THREE.Vector3();
    //     box.getCenter(center);

    //     // 将模型居中
    //     mesh.position.sub(center);
    //     // wireframeMesh.position.sub(center); // 线框也居中


    //     // 根据模型尺寸设置相机初始位置
    //     const size = new THREE.Vector3();
    //     box.getSize(size);
    //     const cameraDistance = size.length() * 2; // 距离模型 2 倍尺寸
    //     camera.position.set(0, 0, cameraDistance); // 初始位置
    //     camera.lookAt(new THREE.Vector3(0, 0, 0));

    //     // 更新 OrbitControls 的目标点
    //     controls.target.set(0, 0, 0); // 对准模型中心
    //     controls.update(); // 重要！更新控件状态
    //     // scene.add(mesh);
    // }

    const initThreeScene = () => {
        if (pickupInfo.gcdPointList.length === 0) return;

        // 获取地形数据范围
        const xs = pickupInfo.gcdPointList.map(p => p[0]);
        const ys = pickupInfo.gcdPointList.map(p => p[1]);

        console.log("X Range:", Math.min(...xs), "to", Math.max(...xs));
        console.log("Y Range:", Math.min(...ys), "to", Math.max(...ys));

        // 设置洞的配置（假设洞位于地形中心）
        const xMin = Math.min(...xs);
        const xMax = Math.max(...xs);
        const yMin = Math.min(...ys);
        const yMax = Math.max(...ys);

        const holeCenterWcs = {
            x: (xMin + xMax) / 2,
            y: (yMin + yMax) / 2
        };

        const holeConfig = {
            center: holeCenterWcs, // 洞中心（WCS坐标系）
            radius: 120            // 根据需要调整大小
        };

        const referencePoint = globalDxf.wcsOff; // 格式应为 [x, y] 或 { x, y }
        const points = pickupInfo.gcdPointList.map(p => [
            p[0] - referencePoint.x,
            p[1] - referencePoint.y,
            p[2] * 20 // z 高程保留不变
        ]);

        // 提取 x, y 坐标用于三角剖分（Delaunay 不关心 z）
        const xyPoints = points.map(p => [p[0], p[1]]);
        const delaunay = d3Delaunay.Delaunay.from(xyPoints);

        // 准备顶点位置数组和索引数组
        const vertices = [];
        const indices = [];

        // 获取洞的信息
        const holeCenterRelativeToRef = {
            x: holeConfig.center.x - referencePoint.x,
            y: holeConfig.center.y - referencePoint.y
        };
        const holeRadius = holeConfig.radius;
        // 在场景中添加一个表示洞位置的小球
        const holeSphereGeometry = new THREE.SphereGeometry(holeRadius, 32, 32);
        const holeSphereMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000, wireframe: true });
        const holeSphere = new THREE.Mesh(holeSphereGeometry, holeSphereMaterial);
        holeSphere.position.set(holeCenterRelativeToRef.x, holeCenterRelativeToRef.y, 0); // 假设 z=0
        scene.add(holeSphere);

        // 工具函数：判断点是否在圆内
        function isPointInCircle(px, py, cx, cy, radius) {
            const dx = px - cx;
            const dy = py - cy;
            return dx * dx + dy * dy <= radius * radius;
        }

        // 工具函数：将点投影到圆周上
        function projectToCircle(px, py, cx, cy, radius) {
            const dx = px - cx;
            const dy = py - cy;
            const len = Math.sqrt(dx * dx + dy * dy);
            if (len === 0) return { x: cx + radius, y: cy }; // 避免除以0
            return {
                x: cx + (dx / len) * radius,
                y: cy + (dy / len) * radius
            };
        }

        // 遍历每个三角形面
        for (let i = 0; i < delaunay.triangles.length; i += 3) {
            const aIndex = delaunay.triangles[i];   // 第一个点索引
            const bIndex = delaunay.triangles[i + 1]; // 第二个点索引
            const cIndex = delaunay.triangles[i + 2]; // 第三个点索引

            const a = points[aIndex];
            const b = points[bIndex];
            const c = points[cIndex];

            const holeCenter = holeCenterRelativeToRef;

            // 判断每个顶点是否在洞内
            const aIn = isPointInCircle(a[0], a[1], holeCenter.x, holeCenter.y, holeRadius);
            const bIn = isPointInCircle(b[0], b[1], holeCenter.x, holeCenter.y, holeRadius);
            const cIn = isPointInCircle(c[0], c[1], holeCenter.x, holeCenter.y, holeRadius);

            const inCount = [aIn, bIn, cIn].filter(Boolean).length;

            if (inCount === 3) {
                // 完全在洞内，跳过
                continue;
            } else if (inCount === 0) {
                // 完全在洞外，正常添加
                vertices.push(...a, ...b, ...c);
                const base = (indices.length / 3) * 3;
                indices.push(base, base + 1, base + 2);
            } else {
                // 部分在洞内，需要投影顶点
                const processPoint = (p, isIn) => {
                    if (isIn) {
                        const proj = projectToCircle(p[0], p[1], holeCenter.x, holeCenter.y, holeRadius);
                        return [proj.x, proj.y, p[2]]; // 保留 z 值
                    }
                    return p;
                };
                const pa = processPoint(a, aIn);
                const pb = processPoint(b, bIn);
                const pc = processPoint(c, cIn);

                // 判断是否形成有效三角形（三点不共线）
                const area = Math.abs(
                    (pb[0] - pa[0]) * (pc[1] - pa[1]) - (pb[1] - pa[1]) * (pc[0] - pa[0])
                );
                if (area > 1e-5) {
                    vertices.push(...pa, ...pb, ...pc);
                    const base = (indices.length / 3) * 3;
                    indices.push(base, base + 1, base + 2);
                }
            }
        }

        // 创建 BufferGeometry
        const geometry = new THREE.BufferGeometry();

        // 设置顶点位置属性
        geometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(vertices), 3));

        // 设置索引（可选，有助于减少重复顶点）
        if (indices.length > 0) {
            geometry.setIndex(new THREE.BufferAttribute(new Uint16Array(indices), 1));
        }

        geometry.computeVertexNormals(); // 自动计算法线

        // 创建材质并添加到场景中
        const material = new THREE.MeshPhongMaterial({
            color: 0x00ff00,
            side: THREE.DoubleSide,
            flatShading: true
        });

        // 环境光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);

        // 方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(10, 20, 10).normalize();
        scene.add(directionalLight);

        const mesh = new THREE.Mesh(geometry, material);
        scene.add(mesh);

        // 居中显示
        const box = new THREE.Box3().setFromObject(mesh);
        const center = new THREE.Vector3();
        box.getCenter(center);
        mesh.position.sub(center);

        // 设置相机位置
        const size = new THREE.Vector3();
        box.getSize(size);
        const cameraDistance = size.length() * 2;
        camera.position.set(0, 0, cameraDistance);
        camera.lookAt(new THREE.Vector3(0, 0, 0));

        // 更新控制器
        controls.target.set(0, 0, 0);
        controls.update();
    };


    const cableMesh=(points,color='0xff0000')=>{
        points = [
            new THREE.Vector3(-5, 0, 20),
            new THREE.Vector3(-2, 2, 20),
            new THREE.Vector3(2, -2, 20),
            new THREE.Vector3(5, 0, 20),
        ];
        const path = new THREE.CatmullRomCurve3(points);
        // 1. 从 path 曲线中采样多个点
        const curvePoints = path.getPoints(50); // 50 个采样点
        // 2. 创建几何体（使用这些点）
        const geometry = new THREE.BufferGeometry().setFromPoints(curvePoints);
        // 3. 创建材质
        const material = new THREE.LineBasicMaterial({ color });
        // 4. 创建 Line 对象并添加到场景
        const curveObject = new THREE.Line(geometry, material);
        scene.add(curveObject);
    }

    const show = () => {
        elevationVisible.value = true;
        init()
        // initThreeScene()
    }
    const close=()=>{
        elevationVisible.value = false;
    }
    const btncs=()=>{
        initThreeScene()
        cableMesh()
    }



    defineExpose({
        show
    })

</script>

<style lang="scss" scoped>

</style>
  
  