import { DrawState, globalDxf, globalTip, poptip } from "dxf-viewer/src";
import { CadMessageReply } from "./CadMessageReply";
import { singal } from "emitter";
import { every, reject } from "lodash";
import { KeyboardKey } from "dxf-viewer/src/controls";
import { Vector3 } from "three";

export class DrawHelp extends CadMessageReply{
    constructor(viewer){
        super(viewer)
    }
    public async pointPick(opt){
        poptip.tip(opt)
        return new Promise((resolve,reject)=>{
            this.execPick(resolve,reject)
        })
    }

    public async lengthPick(opt){
        poptip.tip(opt)
        return new Promise((resolve,reject)=>{
            this.execPick(resolve,reject)
        })
    }

    public async radiusPick(opt){
        poptip.tip(opt)
        return new Promise((resolve)=>{
            var func=(event)=>{
                let point= this.getEventPoint(event)
                singal.pickPoint.remove(func)
                resolve(point)
            }
            singal.pickPoint.add(func);
        })
    }

    public async execPick(resolve,reject){
        var func=(event)=>{
            let point= this.getEventPoint(event)
            singal.pickPoint.remove(func)
            resolve(point)
        }
        var onkeyup=(event:KeyboardEvent)=>{
            if(event.key==KeyboardKey.Space){
                singal.cadkeyup.remove(onkeyup)
                resolve(new Vector3(Number(globalTip.tipconf.inputPosX),Number(globalTip.tipconf.inputPosY),0))
            }
        }
        singal.pickPoint.add(func);
        singal.cadkeyup.add(onkeyup)
    }

    public async select(){
        globalDxf.drawstate=DrawState.Pick
        return new Promise((resolve)=>{
            singal.selectObj.add(()=>{
                globalDxf.drawstate=DrawState.Drawing
                resolve([])
            })
        })
    }
    public override onKeyUp(event: KeyboardEvent): boolean {
        super.onKeyUp(event)
        singal.cadkeyup.dispatch(event)
        return true
    }
    public override onMouseMove(event: PointerEvent): boolean {
        super.onMouseMove(event)
        poptip.onmousemove(event,this.viewer)
        return true
    }
}
