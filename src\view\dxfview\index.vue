<template>
  <div class="dxfview">
    <DxfViewer ref="viewers" :dxfUrl="dxfUrl" :fonts="fonts" @dxf-loaded="_OnLoaded" @dxf-cleared="_OnCleared"
      @dxf-message="_OnMessage"></DxfViewer>
    <div class="mapboxviewer" ref="mapboxviewer">
    </div>
    <div class="mapbox_control_btns">
        <el-button class="btn1" style="margin-right: 10px;" @click="onLoadMapbox"> {{ map.tip }} </el-button>
        <!-- <el-button @click="onCloseMapbox"> 关闭地图 </el-button> -->
        <div v-show="map.tip == '关闭地图'">
          <el-button class="btn1" @click="loadTiff"> 加载tiff </el-button>
          <el-button class="btn1" @click="loadShp"> 识图结果 </el-button>
          <el-button class="btn1" @click="syncView"> 视图联动 </el-button>
          <el-button class="btn1" @click="freezeMap"> 冻结地图 </el-button>
          <el-button class="btn1" @click="freezeCAD"> 冻结CAD </el-button>
          <span style="color: white;"> 层级: </span><el-input style="width: 100px;" v-model="map.zoom">  </el-input>
          <span style="color: white;"> 经度: </span><el-input style="width: 100px;" v-model="map.lng">  </el-input>
          <span style="color: white;"> 纬度: </span> <el-input style="width: 100px;" v-model="map.lat">  </el-input>
          <el-button class="btn1" @click="locLngLat"> 定位 </el-button>
          <el-button class="btn1" @click="extractData"> 提取数据 </el-button>
          <el-button class="btn1" @click="recognition"> 识图 </el-button>
          <el-button class="btn1" @click="showDiff"> 结果对比 </el-button>
          <el-button class="btn1" @click="closeTiff"> 隐藏tiff </el-button>
          <el-button class="btn1" @click="closeShp"> 隐藏识图 </el-button>
          <el-button class="btn1" @click="closeCAD"> 隐藏CAD </el-button>
        </div>
      </div>
    <TextPop :viewers="viewers"></TextPop>
  </div>
</template>

<script setup>
import DxfViewer from './components/DxfViewer.vue'
import { DxfViewer as _DxfViewer, dxfViewMessageLevel ,screen2wcs} from "dxf-viewer"
import TextPop from './components/textPop.vue'
import mainFont from "@/assets/fonts/Roboto-LightItalic.ttf"
import aux1Font from "@/assets/fonts/NotoSansDisplay-SemiCondensedLightItalic.ttf"
import aux2Font from "@/assets/fonts/HanaMinA.ttf"
import aux3Font from "@/assets/fonts/NanumGothic-Regular.ttf"

import { mitts, btnFnObj, mittEvents } from 'sgdraw/mittBus'
import { apiTask } from '@/http/buildingBlockApi'

import { generateFileMD5 } from '@/util/index'

import { ElLoading, ElMessage } from "element-plus";
import { DxfviewerCad, dxfZsObj } from 'dxf-viewer'
import { DrawArea, CanvasLayer, ImageToPdf, captureScreenshot } from "dxf-viewer/src/utils/index"
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from "vue-router";
import mapboxgl from 'mapbox-gl';
const router = useRouter();
const emits = defineEmits([])
let shp
// 地图同步
import { MapboxViewerSync  } from 'src/mapviewsync'
import axios, { Axios } from 'axios'
const map = reactive({
  tip: '加载地图',
  lng:120,
  lat:30,
  zoom:1
})
let cadmapviewer
let cadapp
const  mapboxviewer=ref(null)
const onLoadMapbox=()=>{
  if (!cadmapviewer) {
    cadapp=viewers.value.GetViewer()
    cadmapviewer = new MapboxViewerSync(mapboxviewer.value, cadapp)
    // cadapp.backgroundAlpha = 0.1
    map.tip = '关闭地图'
  } else {
    if (map.tip == '关闭地图') {
      cadmapviewer.dispose()
      cadmapviewer=null
      // cadapp.backgroundColor = 0x000000
      // cadapp.backgroundAlpha = 1
      map.tip = '打开地图'
    } else {
      // cadapp.backgroundAlpha = 0.1
      map.tip = '关闭地图'
    }
  }
}
const loadShp=()=>{
  shp=cadmapviewer.loadShp({
    url:'maps/map1/output.geojson',
  })
}
const loadTiff=()=>{
  cadmapviewer.loadTiff({
    // lng: 107.61863708496094,
    // lat: 34.41399077791693,
    lng:118.06548603514608,
    lat:37.90810810776659,
    url: 'maps/map1/{z}/{x}/{y}.png',
    projId: 'aaa'
  })
}
const extractData=()=>{
  axios.get('maps/map1/output.geojson').then(res=>{
    let data=res.data;
    let newda=data.features.map(e=>{
      return e.geometry.coordinates.map(pts=>{
        return pts.map(pt=>{
          let {x,y}=lnglat2wcs(...pt)
          return {x,y}
        })
      })
    })
    console.log(newda)
  })
}
const lnglat2wcs=(lng,lat)=>{
  const t = cadmapviewer.map.transform.clone();
  let lnglat=new mapboxgl.LngLat(lng,lat)
  let {x,y}=t.locationPoint(lnglat)
  let cad=viewers.value.GetViewer()
  let wcs=screen2wcs({offsetX:x, offsetY:y, target:mapboxviewer.value}, cad.camera)
  return wcs
}
const locLngLat=()=>{
  let {lng,lat}=map
  cadmapviewer.map.jumpTo({center:{lng,lat}})
}
const syncView = () => {
  cadmapviewer.syncView()
}
const freezeMap = () => {
  cadmapviewer.freezeMap()
}
const freezeCAD = () => {
  cadmapviewer.freezeCAD()
}
const recognition = () => {

}
const showDiff = () => {
  
}
//
let viewers = ref(null)
const dxfUrl = ref(null)
const fonts = ref([
  mainFont, aux1Font, aux2Font, aux3Font
])
const dxfObj = reactive({
  layers: null,
  inputFile: null,
  isLocalFile: false,
  aboutDialog: false,
  urlDialog: false,
  inputUrl: null
})

mitts.on(btnFnObj.lsfn, async (file) => {
  if (dxfUrl.value && dxfObj.isLocalFile) {
    URL.revokeObjectURL(dxfUrl.value)
    // sg.DxfImportManager
  }
  let id = await generateFileMD5(file)
  dxfZsObj.drawingId = id

  //dxfZsObj.drawingId = ''//cad不需要缓存，设置为空字符串

  dxfObj.isLocalFile = true
  dxfObj.inputFile = file

  dxfUrl.value = URL.createObjectURL(file)
  console.log(file, 'filefilefile');

  await viewers.value?.Load(dxfObj.inputFile)
})



const _OnLoaded = () => {
  const obj = viewers.value.GetViewer().GetLayers()
  obj.forEach(lyr => lyr.isVisible = true)
  dxfObj.layers = obj
}

const _OnCleared = () => {
  dxfObj.layers = null
}
const _OnMessage = (e) => {
  let type = "info"
  switch (e.detail.level) {
    case dxfViewMessageLevel.WARN:
      type = "warning"
      break
    case dxfViewMessageLevel.ERROR:
      type = "negative"
      break
  }
  console.log(e.detail.message);
  //   this.$q.notify({ type, message: e.detail.message })
}


//导出
mitts.on(btnFnObj.export, () => {//导出dxf、
  let a = viewers.value.GetViewer().transaction.gpObj.saveToString()
  let params = {
    name: '测试',
    sourceData: a,
    taskType: 'drawingObject2dxf',
  }
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  apiTask(params).then(res => {
    console.log(res);
    ElMessage({ message: '导出成功，请到导出列表页查看', type: 'success', })
  }).finally(() => {
    loading?.close();
  })
})


//测试
onMounted(() => {
  DxfviewerCad.value = viewers.value
  // exportPicture()
})

const exportPicture = () => {
  let canvas = viewers.value.GetViewer().canvas
  let data = canvas.toDataURL('image/png')
  // remove class exportPicture
  let exportPicture = document.querySelector('.exportPicture')
  if (exportPicture) {
    exportPicture.remove()
  }
  const img = document.createElement('img')
  img.classList.add('exportPicture')
  Object.assign(img.style, {
    width: '300px',
    height: '300px',
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#000',
    objectFit: 'contain',
  })
  // img.src = data
  document.body.appendChild(img)
}
window.exportPicture = exportPicture

const openScreenshot = () => {
  // exportPicture()

  const canvas = viewers.value.GetViewer().canvas
  const renderer = viewers.value.GetViewer().renderer
  const scene = viewers.value.GetViewer().scene
  const camera = viewers.value.GetViewer().camera

  // const canvas = viewers.value.GetViewer().canvas
  // const globalCtx = canvas.getContext('2d')
  // console.log(globalCtx, 'globalCtx');

  const layerInstance = new CanvasLayer(canvas, { className: 'screenshot' });
  const tempLayer = layerInstance.layer;
  const tempCtx = layerInstance.ctx;

  // 将临时图层添加到DOM中
  canvas.parentNode.appendChild(tempLayer);

  // 初始化绘图区域
  const drawArea = new DrawArea(tempLayer, tempCtx);
  drawArea.openEvents();
  // console.log(drawArea, 'drawArea');
  drawArea.contextMenuHandler(() => {
    console.log('Custom callback executed!');

    const dataUrl = captureScreenshot(
      renderer,
      scene,
      camera,
      drawArea.rect,
    )
    const ITP = new ImageToPdf(dataUrl)
    ITP.export()

  });
  // console.log(drawArea.contextMenuHandler, 'drawArea.contextMenuHandler');

}
mitts.on(mittEvents.screenshot, (item) => {
  console.log('==================')
  openScreenshot()
})


</script>

<style lang="scss" scoped>
.dxfview {
  flex: auto;
  background-color: #fff;
  width: 100%;
  height: 100%;
  position: relative;
  
}

#map {
  height: 100%;
  // position: absolute;
  // top: 0;
  // left: 0;
}
.mapboxviewer{
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
  // background-color: #000;
  /* pointer-events: none; */
  z-index: 999;
}
.mapbox_control_btns{
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 1001;
  display: flex;
}
</style>