// 导入SparkMD5库
importScripts('/spark-md5.min.js');

// 接收主线程消息
self.onmessage = function (e) {
    const { file, chunkSize } = e.data;

    // 计算文件哈希
    calculateHash(file, chunkSize);
};

/**
 * 在Worker中计算文件哈希
 * @param {File} file - 要计算哈希的文件数据
 * @param {number} chunkSize - 分片大小(bytes)
 */
function calculateHash(file, chunkSize) {
    const chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    const spark = new self.SparkMD5.ArrayBuffer();
    const fileReader = new FileReader();

    // 发送开始计算消息
    self.postMessage({ type: 'START' });

    fileReader.onload = function (e) {
        if (e.target.result) {
            spark.append(e.target.result);
            currentChunk++;

            // 发送进度消息
            const progress = Math.floor((currentChunk / chunks) * 100);
            self.postMessage({ type: 'PROGRESS', data: progress });

            if (currentChunk < chunks) {
                // 继续加载下一个分片
                loadNext();
            } else {
                // 计算完成，发送哈希结果
                const hash = spark.end();
                self.postMessage({ type: 'COMPLETE', data: hash });
            }
        }
    };

    fileReader.onerror = function () {
        self.postMessage({ type: 'ERROR', data: '文件读取失败' });
    };

    // 加载文件分片
    function loadNext() {
        const start = currentChunk * chunkSize;
        const end = start + chunkSize >= file.size ? file.size : start + chunkSize;

        // 读取当前分片
        const chunk = file.slice(start, end);
        fileReader.readAsArrayBuffer(chunk);
    }

    // 开始处理第一个分片
    loadNext();
} 