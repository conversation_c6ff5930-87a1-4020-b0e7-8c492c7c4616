<template>
    <div class="layersSelection no-select">
        <div class="show row" @click="showLayerList">
          <el-icon class="ico zpointer" :color="curLayer.isVisible?'#1858a2':'gray'"  @click.stop="setlayerVisible()">
            <View v-if="curLayer.isVisible"/>
            <Hide v-else/>
          </el-icon>
          <el-icon class="ico zpointer" :color="curLayer.isFreeze?'#FFA500':'gray'" @click.stop="setlayerFreeze()">
            <Loading v-if="curLayer.isFreeze"/>
            <Sunny  v-else/>
          </el-icon>
          <el-icon class="ico zpointer" :color="curLayer.isLock?'#FFA500':'gray'" @click.stop="setlayerLock()">
            <Lock v-if="curLayer.isLock"/>
            <Unlock  v-else/>
          </el-icon>
          <span class="colorbtn" :style="getLayerColorStyle()" @click.stop="showColorPicker()"></span>
          <span class="text" >{{ curLayer.name }}</span>
        </div>

        <data class="itembox scroll" v-if="isShow">
            <div class="item zpointer" v-for="item,idx in layers" :key="idx" @click="pickLayer(idx)">
                <el-icon class="ico zpointer" :color="item.isVisible?'#1858a2':'gray'" @click.stop="setlayerVisible(idx)">
                  <View v-if="item.isVisible"/>
                  <Hide v-else/>
                </el-icon>
                <el-icon class="ico zpointer" :color="item.isFreeze?'#FFA500':'gray'"  @click.stop="setlayerFreeze(idx)">
                  <Loading v-if="item.isFreeze"/>
                  <Sunny v-else/>
                </el-icon>
                <el-icon class="ico zpointer" :color="item.isLock?'#FFA500':'gray'"  @click.stop="setlayerLock(idx)">
                  <Lock v-if="item.isLock"/>
                  <Unlock v-else/>
                </el-icon>
                <span class="colorbtn" :style="getLayerColorStyle(idx)" @click.stop="showColorPicker(idx)"></span>
                <span class="text">{{ item.name }}</span>
            </div>
        </data>
        <Sgcolorpicker ref="layercolorpicker" :color="modify.color" @colorPicked="onColorPicked"></Sgcolorpicker>
    </div>
</template>
  
<script setup>
import { View ,Sunny,Unlock,Lock,Loading,Hide} from '@element-plus/icons-vue'
import {dxflayers,curLayer,setCurLayer,getLayerColor,
  setlayerVisible,setlayerFreeze,setlayerLock
} from 'dxf-viewer'
import {PubSub} from 'emitter'
const layers=dxflayers.layers
const isShow=ref(false)
const layercolorpicker=ref()
const modify=reactive({
  idx:-1,
  color:'rgb(0,0,255)'
})
onMounted(()=>{
})

const getLayerColorStyle=computed(()=>(idx)=>{
    let color=getLayerColor(idx)
    return {"background-color":color}
}) 
const showLayerList=()=>{
  isShow.value=!isShow.value
}

const pickLayer=(idx)=>{
  setCurLayer(idx)

  isShow.value=false
}
const showColorPicker=(idx)=>{
  modify.idx=idx?idx:dxflayers.curLayerIdx
  modify.color=getLayerColor(idx)
  layercolorpicker.value.show()
}
const onColorPicked=(color)=>{//'颜色选择器返回颜色'
  console.log('颜色选择器返回颜色',color)
  PubSub.default.pub('layercolorchanged',{idx:modify.idx,color:color.color})
}
</script>
  
<style lang="scss" scoped>
  .layersSelection {
    position: relative;
    background-color: #212832;
    .show {
        width: 300px;
        height: 25px;
        line-height: 25px;
        align-items: center;
    }
    .itembox {
        width: 300px;
        position: absolute;
        z-index: 99;
        top: 30px;
        background-color: #212832;
        color:'#fff';
        min-height: 100px;
        max-height: 300px;
        overflow-y: scroll;
        .item {
            margin: 5px 0;
            display: flex;
            align-items: center;
            &:hover {
                background-color: #787878;
            }
        }
        
    }
    .ico {
        margin: 0 3px;
        &:hover {
            background-color: red;
        }
    }
    .text {
        margin-left: 30PX;
        color: #fff;
        font-size: 14px;
        font-weight: 100;
    }
  }

  .no-select {
    user-select: none;
    -moz-user-select: none; /* Firefox */
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* Internet Explorer/Edge */
  }
  .colorbtn{
    width: 15px;
    height: 15px;
    border: 1px solid gray;
  }
</style>
  
  