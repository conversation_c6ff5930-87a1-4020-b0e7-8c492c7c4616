import instance from '@/http/index'

// 获取API基础URL，从axios.js导入
import { API_URLS } from '../utils/axios.js';
// { BASE_URL, UPLOAD_URL, VITE_BASE_MAP_BOX_URL, VITE_STANDARD_DATA_BASE_URL, VITE_UPLOAD_URL }
// 创建项目
export function apiCreateProject(data) {
  return instance({
    url: API_URLS.VITE_ISOLAR_BUILD_URL + "/v1/project/create",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 更新项目
export function apiUpdateProject(projectData) {
  return instance({
    url: API_URLS.VITE_BASE_MAP_BOX_URL + `/v1/project/${projectData.projectCode}`,
    method: 'POST',
    data: {
      "cadStatus": projectData.cadStatus,
      "cadUrl": projectData.projectCadPath,
      "latitude": projectData.projectLatitude,
      "longitude": projectData.projectLongitude,
      "projectDesc": projectData.projectIntro,
      "projectName": projectData.projectName,
      "projectType": projectData.projectType,
      "tiffStatus": projectData.tiffStatus,
      "tiffUrl": projectData.projectTiffPath
    },
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/* 获取项目列表 */
export function apiGetProjectList(data) {
  return instance({
    url: API_URLS.VITE_BASE_MAP_BOX_URL + "/v1/project/list",
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/* 获取项目详情 */
export function apiGetProjectDetail(code) {
  return instance({
    url: API_URLS.VITE_BASE_MAP_BOX_URL + `/v1/project/${code}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

/**
 * 更新项目文件信息
 * @param projectCode
 * @param data {{ cadUrl?: string, tiffUrl?: string, longitude?: number, latitude?: number }}
 * @returns
 */
export const apiUpdateProjectFile = (
  projectId,
  data
) => {
  return instance({
    url: `${API_URLS.VITE_BASE_MAP_BOX_URL}/v1/project/${projectId}`,
    method: "put",
    data: data,
  });
};


