import { Scene } from "three"
import { DxfWasmScene } from "./DxfWasmScene"
import { Batch } from "../batch"
import { BatchEntityType } from "../constant"

export class DxfIdScene extends Scene{
    public viewer
    public dxfScene:DxfWasmScene
    constructor(dxfscene:DxfWasmScene) {
        super()
        this.viewer=dxfscene.viewer
        this.dxfScene=dxfscene
    }
    public loadBatch() {
        this.dxfScene.batches.each((b)=>{
            this._LoadBatch(b)
        })
    }
    public _LoadBatch( batch) {
        if (batch.key.blockName !== null &&
            batch.key.geometryType !== BatchEntityType.BLOCK_INSTANCE &&
            batch.key.geometryType !== BatchEntityType.POINT_INSTANCE) {
            /* Block definition. */
            return
        }
        const objects = new Batch(this.viewer,  batch).CreateIdObjects()
        for (const obj of objects) {
            batch.idMeshes.add(obj)
            this.add(obj)
        }
    }
}