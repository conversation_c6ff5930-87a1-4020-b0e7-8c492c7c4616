import DxfParser from "../parser/DxfParser"

/** Fetches and parses DXF file. */
export class DxfFetcher {
    public url:string
    public encoding:string
    constructor(url, encoding = "utf-8") {
        this.url = url
        this.encoding = encoding
    }

    /** @param progressCbk {Function} (phase, receivedSize, totalSize) */
    async Fetch(progressCbk?:Function) {
        const response:any = await fetch(this.url)
        const totalSize = +response.headers.get('Content-Length')

        const reader = response.body.getReader()
        let receivedSize = 0
        //XXX streaming parsing is not supported in dxf-parser for now (its parseStream() method
        // just accumulates chunks in a string buffer before parsing. Fix it later.
        let buffer = ""
        let decoder = new TextDecoder(this.encoding)
        while(true) {
            const {done, value} = await reader.read()
            if (done) {
                buffer += decoder.decode(new ArrayBuffer(0), {stream: false})
                break
            }
            buffer += decoder.decode(value, {stream: true})
            receivedSize += value.length
            if (progressCbk !== undefined) {
                progressCbk("fetch", receivedSize, totalSize)
            }
        }

        if (progressCbk !== undefined) {
            progressCbk("parse", 0, null)
        }
        const parser = new DxfParser()
        return parser.parseSync(buffer)
    }
}
