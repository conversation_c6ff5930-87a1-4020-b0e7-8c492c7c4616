import instance from './index'
import { API_URLS } from '../utils/axios.js';

//发起图纸导出任务
export function apiTask(data) {
    return instance({
        url: API_URLS.VITE_DIGITAL_COMPONENT_BASE_URL + "/v1/cad/task",
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//获取任务列表
export function apiTaskList(params) {
    return instance({
        url: API_URLS.VITE_DIGITAL_COMPONENT_BASE_URL + "/v1/cad/task",
        method: 'get',
        params,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//获取上传文件地址
export function apiQueryUploadFileUrl(data) {
    return instance({
        url: API_URLS.VITE_UPLOAD_URL + "/v1/storage/presignedUrl/upload",
        method: 'POST',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//获取任务详情
export function apiTaskDetail(id) {
    return instance({
        url: API_URLS.VITE_DIGITAL_COMPONENT_BASE_URL + "/v1/cad/task/" + id,
        method: 'get',
        // headers:{
        //     'Content-Type':'application/json'
        // }
    })
}