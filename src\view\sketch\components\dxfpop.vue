<template>
    <div class="dxfpop" v-show="dialogVisible">
        <DxfViewer ref="viewerss" :fonts="fonts"
            @dxf-loaded="_OnLoaded" @dxf-cleared="_OnCleared" @dxf-message="_OnMessage"
        ></DxfViewer>
    </div>
</template>

<script lang="ts" setup>
// import Dxfview from '../../dxfview/index.vue'

import DxfViewer from './DxfViewer.vue'


import { ref,onMounted,nextTick,reactive } from 'vue'
import { ElLoading ,ElMessage } from "element-plus";


const emits = defineEmits()
const props = defineProps({
  fonts: {
    type: Array,
    default: []
  },
})
const dialogVisible=ref(false)
const viewerss=ref(null)
const tableData=ref([{isVisible:true,layerColor:'pink'}])

let viewers=ref(null)

const render=(gp)=>{
    viewerss.value.GetViewer().gpShow(gp)
    
}
const clean=()=>{
    viewerss.value.GetViewer().Clear()
}
onMounted(()=>{
    // viewerss.value.GetViewer().ceshi666()
    viewerss.value.GetViewer().canvasvisibility('hidden')
    
})

const switchPop=()=>{
    dialogVisible.value=!dialogVisible.value
    let str=dialogVisible.value ?'visible':'hidden'
    viewerss.value.GetViewer().canvasvisibility(str)
}

defineExpose({
    switchPop,
    render,
    clean,
})
</script>

<style lang="scss" scoped>
.dxfpop {
    // min-width: 100px;
    // min-height: 100px;
    // background-color: red;
}

</style>

