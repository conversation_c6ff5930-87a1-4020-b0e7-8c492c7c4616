
import { PubSub } from "packages/emitter"
import { screen2wcs } from "../controls"
import { dxfZsObj, globalDxf } from "../global"
import { DxfViewer } from "../viewer"



export class LodManage {
    public viewer: DxfViewer
    public isBusy: boolean = false//是否繁忙状态
    public _last: boolean = false//是否有新任务来到
    public lastTime: number//上次执行时间
    public timerId
    public wait: number = 200//等待执行时间 防抖
    public maxSleep: number = 1000 //连续多个任务，超过maxSleep需要响应一次
    public lastIds:any[]=[]
    public set last(_last: boolean) {
        this._last = _last
        if (performance.now() - this.lastTime > this.maxSleep) {
            this.run()
        } else if (_last) {
            clearTimeout(this.timerId)
            this.timerId = setTimeout(() => {
                this.run()
                this._last = false
            }, this.wait);
        }
    }
    constructor(viewer) {
        this.viewer = viewer
        this.lastTime = performance.now()
    }

    public require() {//接收到新请求，旧请求还在运行中，终止旧任务
        if (this.isBusy) {
            this.last = true
        } else {
            this.run()
        }
    }
    public async run() {
        this.lastTime = performance.now()
        this.isBusy = true
        await new Promise((resolve, reject) => {
            this.processTask(resolve, reject)
        })
        if (!this._last) this.isBusy = false
    }

    public async processTask(resolve, reject) {//
        setTimeout(() => {
            let objs=this.getObjsInScreen()
            this.requireLod(objs)
            resolve(true)
        }, this.wait);

    }

    public async processTask1(resolve, reject) {//
        setTimeout(() => {
            console.log(111)
            resolve(2)
        }, 3000)
    }

    public getObjsInScreen() {//获取屏幕范围内所有对象
        let { canvas, camera, canvasWidth, canvasHeight } = this.viewer
        let p1 = screen2wcs({ offsetX: 0, offsetY: 0, target: canvas }, camera)
        let p2 = screen2wcs({ offsetX: canvasWidth, offsetY: canvasHeight, target: canvas }, camera)
        let objs:any[] = sg.getRectDrawingGroup(dxfZsObj.drawingId,new sg.Point2d(p1.x,p1.y),new sg.Point2d(p2.x,p2.y))
        objs=[3,4,5,6,7]
        globalDxf.idsInScreen=objs
        // objs=objs.map((id)=>{
        //     return this.viewer.transaction.gpObj.getPBaseObjById(id)
        // })
        return objs
    }
    public requireLod(ids:any[]){
        this.viewer.rebuildScene()
    }
}