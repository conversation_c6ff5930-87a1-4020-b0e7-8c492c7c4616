import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from "unplugin-vue-components/vite";
import AutoImport from "unplugin-auto-import/vite";
import { TDesignResolver } from 'unplugin-vue-components/resolvers';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from "path"
import copy from 'rollup-plugin-copy'

const MODULES = path.join(__dirname, 'modules');
export default defineConfig({
	base: '/',
	plugins: [
		vue(),
		createSvgIconsPlugin({
			iconDirs: [
				path.resolve(process.cwd(), 'src/svg/svgIcon/layerManage'),
				path.resolve(process.cwd(), 'src/svg/svgIcon/sidebar'),
				path.resolve(process.cwd(), 'src/svg/svgIcon/rightSidebar'),
				path.resolve(process.cwd(), 'src/svg/svgIcon/head'),
				path.resolve(process.cwd(), 'src/svg/svgIcon/exportcenter'),
				path.resolve(process.cwd(), 'src/svg/svgIcon/misc'),
				path.resolve(process.cwd(), 'src/svg/svgIcon/cadpoint'),
				path.resolve(process.cwd(), 'src/svg/svgIcon/mapbox')
			],
			symbolId: 'icon-[dir]-[name]'
		}),
		// 自动导入vue-api
		AutoImport({
			imports: ["vue", "vue-router"],
			dirs: ["src/store"],
			dts: "src/auto-import.d.ts",
			resolvers: [TDesignResolver({
				library: 'vue-next'
			})],
		}),
		Components({
			dirs: ["src/components"], // 要导入组件的目录路径
			deep: true, // 搜索子目录
			dts: "src/components.d.ts", // 生成 `components.d.ts` 全局声明
			// 配置vxe-table的自动注册组件
			resolvers: [
				// ElementPlusResolver(),
				(componentName) => {
					if (componentName.startsWith("Vxe"))
						return { name: componentName.slice(3), from: "vxe-table" };
				},
				TDesignResolver({
					library: 'vue-next'
				})
			],
		}),
		copy({
			targets: [
				{ src: 'sgdraw/**', dest: 'dist/sgdraw' },
				{ src: 'sgdraw/sgdd', dest: 'CadUI' },
				{ src: 'public/spark-md5.min.js', dest: 'dist/' }, // 复制SparkMD5库到dist目录
			],
			flatten: false,
			hook: 'writeBundle' // notice here
		}),
	],
	build: {
		outDir: 'dist',
		// assetsDir: '/assets', // 静态资源目录
	},
	// build: {
	// 	outDir: "CadUI", //输出文件名称
	// 	lib: {
	// 		entry: path.resolve(__dirname, "src/npmPublish/sketch.js"), //指定组件编译入口文件
	// 		name: "CadUI",
	// 		fileName: "CadUI",
	// 	}, //库编译模式配置
	// 	rollupOptions: {
	// 		// 确保外部化处理那些你不想打包进库的依赖
	// 		external: ["vue"],
	// 		output: {
	// 			// 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
	// 			globals: {
	// 				vue: "Vue",
	// 			},
	// 		},
	// 	}, // rollup打包配置
	// },
	resolve: {
		alias: {
			'@': path.resolve(__dirname, 'src'),
			'src': path.resolve(__dirname, 'src'),
			'sgdraw': path.resolve(__dirname, 'sgdraw'),
			'PriumCAD': path.resolve(__dirname, 'PriumCAD'),
			'SLabutment': path.resolve(__dirname, 'SLabutment'),
		},
	},
	server: {
		host: 'localhost',
		port: 3021,
		//处理ffmpeg的SharedArrayBuffer is not defined
		headers: {
			// "Cross-Origin-Embedder-Policy": "require-corp",
			'Cross-Origin-Opener-Policy': 'same-origin',
			// 'Cross-Origin-Opener-Policy': '*',
		},
		proxy: {
			// '/v1': {
			// 	target: 'http://************:280',
			// 	changeOrigin: true,
			// },
			// "/v3": {
			// 	target: "https://storage-dev.sungrow-re.com/server/", //上传目标服务器地址
			// 	changeOrigin: true,
			// 	rewrite: (path) => path.replace(/^\/v3/, ""),
			// },
			// "/con1": {
			// 	target: 'ws://localhost:3100/',
			// 	changeOrigin: true,
			// 	wsv: true,
			// 	rewrite: (path) => path.replace(/~\/con1/, "")
			// },
			// "/upload": {
			// 	target: "http://***************:8080",
			// 	changeOrigin: true,
			// 	rewrite: (path) => path.replace(/^\/upload/, ""),
			// },
		},
	},
	css: {
		preprocessorOptions: {
			scss: {
				//additionalData: '@import"@/assets/css/globalstyle.scss";'
				//👆这样写是不行的。因为如果项目中其他的css文件有@forward关键字，就会提示错误
				additionalData: `
					@use "@/assets/css/globalstyle.scss" as *;
					@use "@/assets/css/element/index.scss" as *;
				`,
				//👆这样写是可以的，但是要注意
				//!!!!一定要将其他地方引入该css文件的地方删除，如index.html,否则会提示已经加载该module的错误！！！
			},
		},
	},
	worker: {
		format: 'es', // Worker 格式
		plugins: [], // Worker 插件
		rollupOptions: {
			// Rollup选项
		},
	},
	// 添加功能标志定义
	define: {
		__VUE_OPTIONS_API__: false, // 启用Options API支持
		__VUE_PROD_DEVTOOLS__: false, // 生产环境不启用开发工具
		__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false // 关闭生产环境下的水合不匹配详细信息
	},
})







