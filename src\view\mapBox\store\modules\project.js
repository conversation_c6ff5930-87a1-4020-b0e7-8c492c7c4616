import { defineStore } from 'pinia';

export const useProjectStore = defineStore('project', {
  state: () => ({
    projectInfo: {
      projectCode: undefined,
      projectName: undefined,
      cadUrl: undefined,           // CAD文件URL
      tiffUrl: undefined,          // TIFF文件URL
      recognitionStatus: undefined, // 识别状态
    },
    // 项目状态枚举
    statusEnum: {
      WORKING: 'WORKING',  // 处理中
      FAILED: 'FAILED',    // 失败
      SUCCESS: 'SUCCESS',  // 成功
    }
  }),
  actions: {
    setProjectInfo(infoData) {
      this.projectInfo = {
        ...infoData,
        projectCode: infoData?.code,
        projectName: infoData?.name,
        cadUrl: infoData?.cadUrl,
        tiffUrl: infoData?.tiffUrl,
        recognitionStatus: infoData?.recognitionStatus,
      };
    },
    async setProjectData(info) {
      this.setProjectInfo(info);
    },
    // 更新项目处理状态
    updateProjectStatus(status) {
      this.projectInfo.recognitionStatus = status;
    },
    // 检查项目是否已准备好
    isProjectReady() {
      // 项目准备就绪标准：
      // 1. CAD文件已上传 (cadUrl存在)
      // 2. TIFF文件已上传 (tiffUrl存在)
      // 3. 图像识别处理成功 (recognitionStatus为SUCCESS)
      const { cadUrl, tiffUrl, recognitionStatus } = this.projectInfo;
      return cadUrl && tiffUrl && recognitionStatus === this.statusEnum.SUCCESS;
    },
  },
  getters: {
    getProjectInfo() {
      return this.projectInfo;
    },
  },
});