// 为外部模块提供类型声明
declare module "sgdraw/mittBus" {
  export const mitts: any;
  export const btnFnObj: {
    [key: string]: any;
    lsfn?: string;
  };
  export const mittEvents: {
    [key: string]: any;
  };
}

declare module "@/http/buildingBlockApi.js" {
  export function apiTask(params: {
    name: string;
    sourceData: string;
    taskType: string;
  }): Promise<any>;
  
  export function apiTaskDetail(id: string): Promise<any>;
}

declare module "sgdraw/util/index" {
  export function uploadfileFn(files: File[]): Promise<any>;
}

declare module "dxf-viewer/src" {
  export const globalDxf: {
    filenam: string;
    [key: string]: any;
  };
} 