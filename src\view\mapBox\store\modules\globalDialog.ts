import { defineStore } from 'pinia';

export const useGlobalDialogStore = defineStore('globalDialog', {
  state: () => ({}),
  getters: {},
  actions: {
    projectInfoDialog(): void {
      console.log('projectInfoDialog');
    },
    projectPictureDialog(): void {
      console.log('projectPictureDialog');
    },
    projectPeopleDialog(): void {
      console.log('projectPeopleDialog');
    },
  },
  persist: {},
});
