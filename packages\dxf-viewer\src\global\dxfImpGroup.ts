import { GraphicType } from "../controls";
import { DrawState } from "./constant";
import { initDxfLayerData } from "./dxfLayers";
import { initDxfLinetypesData } from "./dxfLinetypes";
import { initDxfLineTypesData_2 } from '../viewer/DxfMaterials'
import { textRender } from "../entities"
export enum HighLMod{
    Block='Block',
    Element='Element'
}
export const globalConf={
    defaultPointSize:12
}
//1 英寸（inch） ≈ 0.0254 米
//1 英尺 (foot)≈ 0.3048 米（meter）
export const units={
    // foot:0.3048,
    inch:0.0254,
    foot:0.0508,
}
export const globalDxf=reactive( {
    filenam:'',
    screenpos:{x:0,y:0},
    mouse:{x:0,y:0},//当前鼠标位置
    origin:{x:0,y:0},//原点位置
    wcsOff:{x:0,y:0},//偏移
    wcsmouse:{x:0,y:0},
    operation:GraphicType.None,
    drawstate:DrawState.Pick,
    isdraw:false,//是否图元绘制中，绘制时可打开捕捉吸附
    isSnapOn:true,//捕捉吸附开关
    isLineWidOn:false,//是否使用线宽
    isTrackOn:false,//是否开启追踪
    isLodOn:false,
    highlMod:HighLMod.Element,
    highlId:'',//鼠标移动时id
    blockId:'',//块id
    view:{//视图模型空间大小
       wid:100,
       hei:100
    },
    selectFilter:null,
    highlFilter:null,
    selectBoxFilter:null,
    isMetric:true,
    idsInScreen:[],//用于lod
    statistic:{
        hatch:0,
        text:0,
        mtext:0
    },
    lineStyleUniforms:{lineStyle:[],dash:[]},
    miss:{
        linestyles:new Set()
    }
})
watch(()=>globalDxf.highlId,()=>{
    // console.log(globalDxf.highlId)
})
// let count=0
// watchEffect(()=>{
//     console.log(globalDxf.wcsOff,'watch globalDxf.wcsOff',count++);
// })

const setCursorStyle=(domId)=>{
    var dom = document.getElementById(domId)
        switch(globalDxf.drawstate){
            case DrawState.Pick:
                if(globalDxf.operation==GraphicType.None){
                    dom.style.cursor= 'url(/img/crossdot.png) 64 64,auto';
                }else{
                    dom.style.cursor= 'url(/img/dot.png) 64 64,auto';
                }
                break
            case DrawState.Drawing:
                dom.style.cursor= `url(/img/cross.png) 64 64,auto`;
                // dom.style.cursor= "crosshair";
                break
            default:
                dom.style.cursor= "move";
                // dom.style.cursor="default"
                break;
        }
}
export const setupCursorStyle=(domId:string)=>{//设置鼠标样式,外部调用，需要传入dom的id
    watch(()=>[globalDxf.drawstate,globalDxf.operation],()=>{ 
        setCursorStyle(domId)
    },{immediate:true})
}

export const initDxfBaseData = async (wasmCtx)=>{
    initDxfLayerData(wasmCtx)
    // initDxfLinetypesData(wasmCtx)
    initDxfLineTypesData_2(wasmCtx)
    await textRender.initDefault()
}

