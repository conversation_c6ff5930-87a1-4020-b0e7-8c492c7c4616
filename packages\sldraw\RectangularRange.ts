import { DrawHelp } from "../dxfdraw/drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState,pickupInfo,SlScopeType } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"
import { PubSub } from 'emitter';
import { Snap } from "dxf-viewer/src/scene/Snap"

export class RectangularRange extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    public color:string='orange'
    public draw:any=true


    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        const material = new LineBasicMaterial({ color:this.color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        PubSub.default.sub("slidentifyclear", this.clear.bind(this));
        this.init()
        Snap.turnOffSnap()//关闭捕捉吸附
    }
    public async init(){
        while (this.draw) {
            let p0=await this.pointPick({
                tip:"指定第一个点",
                pos:'',
            })
            this.setV(p0)
            let p1=await this.lengthPick({
                tip:'指定第二个点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
        }

        // this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            let vertices=this.getvertices(secpt)
            this.drawTempGraphic(vertices)
        }
        return true;
    }

    setV(v:Vector3){
        if(this.pnts.length<2) {
            this.pnts.push(v)
            pickupInfo.identifyScope=this.pnts
        }else {
            pickupInfo.identifyScope=[]
            this.viewer.transientSceneWasm.scene.remove(this.mesh)
            if(pickupInfo.slcurrentstate !==SlScopeType.RectangularRange) return
            this.pnts=[v]
            const geometry = new BufferGeometry().setFromPoints([]);
            // 创建线的材质
            const material = new LineBasicMaterial({ color:this.color});
            // 使用几何体和材质创建线
            this.mesh = new ThLine(geometry, material);
            this.viewer.transientSceneWasm.scene.add(this.mesh)
        }
        
    }
    public drawTempGraphic(vertices){
        if(this.pnts.length<2) {
            this.mesh.geometry=new BufferGeometry().setFromPoints(vertices);
            // this.mesh.geometry.setFromPoints(vertices);
        }

    }
    
    clear(){
        this.pnts=[]
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        this.dispose()
        this.viewer.Render()
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
    }

    private getvertices(v2:Vector3) {
        const pointA = this.pnts[0]
		const pointC = v2
		const pointB = new Vector3(pointC.x, pointA.y, pointA.z);
		const pointD = new Vector3(pointA.x, pointC.y, pointC.z);
		// 定义矩形的所有顶点
		const vertices = [pointA,pointB,pointC,pointD,pointA];
        return vertices
    }
}

