import { EventDispatcher } from "./EventDispatcher"

export class My<PERSON>vent extends EventDispatcher{
    public _onmousemove
    public _onmousedown
    public _onmouseup
    public _onwheel
    constructor(){
        super()
        this.bindEvents()
    }
    public dispose(){

    }
    public onMouseMove(e:PointerEvent|MouseEvent|any){
        // console.log('基类 onMouseMove')
    }
    public onMouseDown(e:PointerEvent|MouseEvent|any){
        console.log('基类 onMouseDown')
    }
    public onMouseUp(e:PointerEvent|MouseEvent|any){
        console.log('基类 onMouseUp')
    }
    public onWheel(e:MouseEvent|any){
        console.log('基类 onWheel')
    }
    public bindEvents(){
        this._onmousemove=this.onMouseMove.bind(this)
        this._onmousedown=this.onMouseDown.bind(this)
        this._onmouseup=this.onMouseUp.bind(this)
        this._onwheel=this.onWheel.bind(this)
    }
}