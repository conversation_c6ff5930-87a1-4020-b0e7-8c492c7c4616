import TileLayer from "ol/layer/Tile"
import { XYZ } from "ol/source"
import { OL, tokens } from "./constants"
import VectorLayer from "ol/layer/Vector"
import VectorSource from "ol/source/Vector"
import GeoJSON from "ol/format/GeoJSON"
import { register } from 'ol/proj/proj4'
import proj4 from 'proj4'
import { Vector2 } from "three"
import { Map, View } from "ol"
import { DxfViewer,globalDxf,dxfZsObj } from "dxf-viewer"
import { MyEvent } from "./myevent"
import { Projection, get as getProj, transform, setUserProjection } from "ol/proj"
import * as proj from 'ol/proj'
import { Style, Fill, Stroke } from 'ol/style';
import { ButtonStatus,metalist } from './global';
import * as THREE from "three"

import WebGLTileLayer from 'ol/layer/WebGLTile';


export interface IXY {
    x: number
    y: number
}
export interface ICadPoint {
    mapcoord: IXY
    position: IXY
}
export interface IProjInf {
    pid: string,//项目project ID
    urlCad: '',
    urlTiff: '',
    urlGeoJson: '',
    lnglat: number[], //确定带号
    degree?: number, //计算中央经线
    pmin?: ICadPoint,
    pmax?: ICadPoint,
    offset?: Vector2, // 偏移量
    urlShp?: string
}
/**
 * 
 */
export class OpenlayerCad extends MyEvent {
    public map: Map
    public cad: DxfViewer
    public projInf
    public kruger: string = ''
    public krugerProjection: Projection | undefined
    public cScale: number = 1 //图纸本身比例,完工量检查 =图纸尺寸/地图尺寸=position/mapcoord
    public baseLayer: TileLayer
    public _tiffLayer: TileLayer | undefined
    public _GeoJSONLayer: VectorLayer | undefined
    public cCenter: ICadPoint = { mapcoord: { x: 0, y: 0 }, position: { x: 0, y: 0 } }
    public cadContainer
    public geoJson
    public cadTexts
    public _redLineLayer:any
    public _redLineAnimation:boolean=false
    public _drag:boolean=false
    constructor(mapContainer, cad: DxfViewer) {
        super()
        this.baseLayer = mapboxLayer()
        this.cad = cad
        this.cadContainer = cad.domContainer
        this.map = new Map({
            target: mapContainer,
            layers: [this.baseLayer],
            view: new View({
                center: [127, 30],
                zoom: 15,
                minZoom: 2,
                maxZoom: 24,
                constrainResolution: true, // 禁止非整数级缩放，非整数级会糊，
                smoothResolutionConstraint: false,
            })
        })
        
    }
    public getMap(){
        return this.map
    }
    public initDxf() {

    }
    public convertCad2Mapcoord(p: Vector2): Vector2 {
        let vec = p.clone().sub(new Vector2().copy(this.cCenter.position)).multiplyScalar(this.cScale)
        return new Vector2().copy(this.cCenter.mapcoord).add(vec)
    }
    public convertMapcoord2Cad(p: Vector2) {
        let vec = p.clone().sub(new Vector2().copy(this.cCenter.mapcoord)).multiplyScalar(1/this.cScale)
        return new Vector2().copy(this.cCenter.position).add(vec)
    }
    public async initMap(projInf: IProjInf, hasCoords: boolean = true, hasPminPmax: boolean = false) {//读取项目信息，并设置初始视角,
        this.projInf = projInf
        let cadbuff = await fetchFileAsBlob(projInf.urlCad)
        if(hasPminPmax) {
            await this.cad.Load({
                url: cadbuff, progressCbk: () => {
                    console.log('------------------>', 'cad加载完成')
                }
            })
        }

        
        // 根据hasPminPmax参数决定是否需要从CAD中获取pmin和pmax
        // 如果外部已经设置了pmin和pmax，则不需要再次获取
        if (!projInf.pmin || !projInf.pmax) {
            this.getPRange() // 获取pmin和pmax
        }
        
        this.initBaseInfo()
        this.initProj4()
        this.addTiffLayer(projInf.urlTiff)
        this.addVecLayer(projInf.urlGeoJson)
        setTimeout(() => {
            this.syncViewToMap()//第一次打开，应该以地图为主
        }, 30);
        this.cadContainer.style.pointerEvents='all'
        this.cad.canvas.addEventListener('wheel',this._onwheel)
        this.cad.canvas.addEventListener('pointerdown', this._onmousedown)
        this.cad.canvas.addEventListener('pointerup', this._onmouseup)
        this.cad.canvas.addEventListener('pointermove', this._onmousemove)
        return this
    }
    public initBaseInfo() {//计算cad比例值
        let { pmin, pmax,offset } = this.projInf
        let p0m = new Vector2().copy(pmin.mapcoord)
        let p1m = new Vector2().copy(pmax.mapcoord)
        let p0p = new Vector2().copy(pmin.position).add(offset)
        let p1p = new Vector2().copy(pmax.position).add(offset)
        this.cScale = p1p.clone().sub(p0p).length() / p1m.clone().sub(p0m).length()
        Object.assign(this.cCenter,{
            position: getMiddle(p0p,p1p),//中点
            mapcoord: getMiddle(p0m,p1m)
        })
    }
    public async initProj4() {
        this.kruger = setKurgerProj(this.projInf.lnglat[0], 3, 500000)
        this.krugerProjection = new Projection({
            code: this.kruger,
            units: 'm'
        })
        setUserProjection(this.krugerProjection)
        let view = new View({
            projection: this.kruger,
            center: [this.cCenter.mapcoord.x, this.cCenter.mapcoord.y],
            zoom: 15,
            minZoom: 2,
            maxZoom: 24,
            
        })
        this.map.setView(view)
        setTimeout(this.test.bind(this),30)
    }

    public test() {
        let { pmin, pmax } = this.projInf
        let p0m = new Vector2().copy(pmin.mapcoord)
        let p1m = new Vector2().copy(pmax.mapcoord)
        let pc0 = this.map.getPixelFromCoordinate([p0m.x, p0m.y])
        let pc1 = this.map.getPixelFromCoordinate([p1m.x, p1m.y])
        let pccenter = this.map.getPixelFromCoordinate([this.cCenter.mapcoord.x, this.cCenter.mapcoord.y])
        let vpc0 = new Vector2(pc0[0], pc0[1])
        let vpc1 = new Vector2(pc1[0], pc1[1])
    }

    public override onWheel(e: any): void{
        setTimeout(()=>{
            this.syncViewToCad()
        },0)
    }
    public override onMouseMove(e:  MouseEvent): void {
        if(this._drag){
            setTimeout(()=>{
                this.syncViewToCad()
            },0)
        }
    }
    public override onMouseDown(e:  MouseEvent): void {
        if (e.button == ButtonStatus.Middle) {
            this.panStart(e)
        } 
    }
    public override onMouseUp(e: MouseEvent): void {
        this.panEnd(e)
    }
    public panStart(event: MouseEvent){
        this._drag=true
    }
    public panEnd(event:MouseEvent){
        this._drag=false
    }

    public syncViewToMap() {//同步cad和map视图，应该以Map为基准
        let view = this.map.getView()
        let center: number[] = view.getCenter() as []
        console.log(center,'centercentercenter');
        
        let mapzoom = view.getZoom()!
        let scale = (2 ** mapzoom) / OL.zoomUnit * this.cScale
        this.cad.zoomTo(new Vector2(center[0], center[1]), scale)
        this.cad.Render()
    }
    /**
     * 
     * @returns 
     */
    public syncViewToCad() {//同步cad和map视图，应该以cad为基准
        //1 计算cad屏幕中心点,计算对应大地坐标 2 计算cad的zoom值，计算map对应的zoom值
        if (this.kruger == '') return
        let cadCenter=this.cad.getCenter()
        let mapcoord=this.convertCad2Mapcoord(new Vector2(cadCenter.x,cadCenter.y))
        let cadzoom=this.cad.zoom
        let zoom=Math.log2(cadzoom*OL.zoomUnit)
        // console.log(mapcoord,zoom)
        this.setMapView(mapcoord, zoom)
    }
    public addTiffLayer(url) {//添加
        if (this._tiffLayer) this.map.removeLayer(this._tiffLayer);
        this._tiffLayer = tiffLayer(url)
        this.map.addLayer(this._tiffLayer)
    }
    public hideTiff(flag:boolean){
        this._tiffLayer.setVisible(flag);
    }
    public async addVecLayer(urlGeoJson) {
        // if (this._redLineLayer) this.map.removeLayer(this._redLineLayer);
        // 'maps/map1/output.geojson'  RecognitionResult
        download(urlGeoJson).then(async res=>{
            this.geoJson=res
            await this.setGeoJsonLayer(this.geoJson,[])
        })
    }
    public async setGeoJsonLayer (GeoJSON,idColorRanges:any){
        if (this._GeoJSONLayer) this.map.removeLayer(this._GeoJSONLayer);
        this._GeoJSONLayer=await this.addlayer(GeoJSON,idColorRanges)
    }
    public async addlayer(geoJson,idColorRanges:any){
        let layer=await vecLayer(geoJson,this.kruger,idColorRanges)
        this.map.addLayer(layer)
        return layer
    }

    public async redLineLayer(geoJson,color){
        if (this._redLineLayer) this.map.removeLayer(this._redLineLayer);
        const format = new GeoJSON({
            dataProjection: 'EPSG:4326',
            featureProjection: this.kruger // 比如 'EPSG:3857'
        });
        const features = format.readFeatures(geoJson);
        if (features.length === 0) {
            console.warn('⚠️ No features were parsed!');
            return;
        }
        this._redLineLayer= new VectorLayer({
            source: new VectorSource({
                features: features
            }),
            style: function(feature) {
                return createFlowLineStyle(feature, Date.now(),color)
            },
            zIndex: 3
        });

        this.map.addLayer(this._redLineLayer)
    }
    public removeLayer(arr:string[]){
        arr.forEach(name=>{
           this[name] && this.map.removeLayer(this[name])
        })
    }


    public setMapView(center, zoom) {
        let view = this.map.getView()
        view.setCenter([center.x, center.y])//大地坐标
        view.setZoom(zoom)
    }
    public convertLnglat2Mapcoord(lng, lat){
        return proj.fromLonLat([lng, lat],this.kruger);
    }
    public convertLnglat2Cad(lng,lat){
        let [x,y]=proj.fromLonLat([lng, lat],this.kruger)
        return this.convertMapcoord2Cad(new Vector2(x,y))
    }
    public convertCadLnglat(plist){
        return transform(plist, this.kruger, 'EPSG:4326')
    }
    public getPRange(){ 
        let gp = this.cad.transaction.gpObj
        try {
            this.cadTexts = gp.createTxt()
        } catch (error) {
            console.error("获取CAD文本失败:", error)
        }
        
        // 如果已经存在pmin和pmax，则直接返回
        if(this.projInf?.pmin?.mapcoord?.x || this.projInf?.pmax?.mapcoord?.x) {
            return {
                pmin: this.projInf.pmin,
                pmax: this.projInf.pmax
            }
        }
        
        try {
            let vlist = gp.getPairPoints()
            let mapcoords: any = []
            let positions: any = []
            
            if(!vlist || vlist.size() === 0) {
                console.warn("获取点对失败或点对为空")
                return {
                    pmin: undefined,
                    pmax: undefined
                }
            }
            
            for (let i = 0; i < vlist.size(); i++){
                let p = vlist.get(i)
                if (i % 2 === 0) {
                    mapcoords.push([p.y(), p.x()])
                } else {
                    positions.push([p.x(), p.y()])
                }
            }
            
            let { minXPoint, minXIndex, maxXPoint, maxXIndex } = findFirstMinAndMaxXPointsWithIndex(positions)
            
            // 创建pmin和pmax对象
            const pmin = {
                mapcoord: {
                    x: Number(mapcoords[minXIndex][0].toFixed(4)),
                    y: Number(mapcoords[minXIndex][1].toFixed(4)),
                },
                position: {
                    x: Number(minXPoint[0].toFixed(4)),
                    y: Number(minXPoint[1].toFixed(4)),
                },
            }
            
            const pmax = {
                mapcoord: {
                    x: Number(mapcoords[maxXIndex][0].toFixed(4)),
                    y: Number(mapcoords[maxXIndex][1].toFixed(4)),
                },
                position: {
                    x: Number(maxXPoint[0].toFixed(4)),
                    y: Number(maxXPoint[1].toFixed(4)),
                },
            }
            
            // 将结果保存到projInf
            if(!this.projInf) {
                this.projInf = {} as any
            }
            this.projInf.pmin = pmin
            this.projInf.pmax = pmax
            
            return {
                pmin,
                pmax
            }
        } catch (error) {
            console.error("获取pmin和pmax失败:", error)
            return {
                pmin: undefined,
                pmax: undefined
            }
        }
    }
    public startCameraPin
    public checkView(){
        this.cadContainer.style.pointerEvents='all'
        this.cad.canvas.addEventListener('wheel',this._onwheel)
        this.cad.canvas.addEventListener('pointerdown', this._onmousedown)
        this.cad.canvas.addEventListener('pointerup', this._onmouseup)
        this.cad.canvas.addEventListener('pointermove', this._onmousemove)
        let endCameraPin=new Vector2(this.cad.camera.position.x,this.cad.camera.position.y)
        // console.log(this.startCameraPin,'CameraPin');
        // console.log(endCameraPin,'EndCameraPin');
        // let offset=new Vector2().subVectors( endCameraPin ,this.startCameraPin )
        this.projInf.offset=new Vector2().subVectors( endCameraPin ,this.startCameraPin )
        this.initBaseInfo()
    }
     public freezeMap(){
        this.cadContainer.style.pointerEvents='all'
        this.cad.canvas.removeEventListener('pointerdown', this._onmousedown)
        this.cad.canvas.removeEventListener('pointerup', this._onmouseup)
        this.cad.canvas.removeEventListener('pointermove', this._onmousemove)
        this.cad.canvas.removeEventListener('wheel', this._onwheel)
        this.startCameraPin=new Vector2(this.cad.camera.position.x,this.cad.camera.position.y)
        // console.log(this.startCameraPin,'CameraPin');
    }

}


const mapboxLayer = () => {
    const amapKey = '********************************';
    const qqKey='ARYBZ-KE2LJ-J3QFH-XGZNY-N4UU5-ECBES'
    return new TileLayer({
        source: new XYZ({
            url: `https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token=${tokens.mapbox}`,
            // url: `https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}&key=${amapKey}`,
            // attributions: '© 高德地图'
            tilePixelRatio: window.devicePixelRatio || 1,
            crossOrigin: 'anonymous',
        }),
        zIndex: 0
    })
}
const download=(url)=>{
    return new Promise((resolve, reject) => {
        fetch(url)
            .then(res => {
                if (!res.ok) {
                    throw new Error('Network response was not ok');
                }
                return res.json();
            })
            .then(data => {
                // console.log('Raw GeoJSON:', data);
                delete data.crs;
                resolve(data); // 将处理后的数据作为Promise的结果返回
            })
            .catch(error => {
                reject(error); // 捕获并返回错误
            });
    });
}
const tiffLayer = (baseurl = 'maps/map1') => {
    console.log(baseurl,'baseurl');
    // return new TileLayer({
     return new TileLayer({
        // source:new XYZ({
        //     tileUrlFunction: function(tileCoord) {
        //         const z = tileCoord[0];
        //         const x = tileCoord[1];
        //         // TMS 规则：y = (Math.pow(2, z) - y - 1)
        //         const tmsY = (1 << z) - tileCoord[2] - 1;
        //         return `${baseurl}/${z}/${x}/${tmsY}.png`;
        //     },
        //     crossOrigin: 'anonymous',
        // }),
        source: new XYZ({
            url: `${baseurl}/{z}/{x}/{y}.png`,
        }),
        zIndex: 1
    })
}
const vecLayer = async (data, epsgNam, idColorRanges:any = []) => {
    // console.log(data,'data');
    
    const format = new GeoJSON({
        dataProjection: 'EPSG:4326',
        featureProjection: epsgNam // 比如 'EPSG:3857'
    });
    const features = format.readFeatures(data);
    if (features.length === 0) {
        console.warn('⚠️ No features were parsed!');
        return;
    }
    return new VectorLayer({
        source: new VectorSource({
            features: features
        }),
        style: function(feature) {
            const id = feature.get('id');
            let fill = ''; // 初始化为空
            // 遍历数组查找当前id是否在某个ids数组中
            for (const item of idColorRanges) {
                if (item.ids.includes(id)) {
                    fill = item.color;
                    break;
                }
            }
            // 如果没找到匹配的颜色，使用默认颜色
            if (!fill) {
                fill = 'rgba(255, 192, 203, 0.5)';
            }
            return new Style({
                fill: new Fill({ color: fill }),
                stroke: new Stroke({ color: '#885588', width: 1 })
            });
        },
        zIndex: 2
    });
};
function createFlowLineStyle(feature, time, color='#00f', speed=1) {//线条流动效果
    const geometry = feature.getGeometry();
    if (geometry.getType() !== 'LineString') return null;
    const dash = [10, 10]; // 虚线段和空隙长度
    const dashLength = dash[0] + dash[1]; // 总周期长度
    // 使用 speed 控制流动速度
    const offset = -(time * speed / 50) % dashLength;
    return new Style({
        stroke: new Stroke({
            color,
            width: 4,
            lineDash: dash,
            lineDashOffset: offset
        })
    });
}
const setKurgerProj = (lng, degree = 3, dx = 0) => {
    let center = getKrugerLng(lng, degree)
    let epsgNam = `kruger_${center}_${dx}`
    let epsgVal = `+proj=tmerc +lat_0=0 +lon_0=${center} +k=1 +x_0=${dx} +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs`
    proj4.defs(epsgNam, epsgVal)
    register(proj4)
    return epsgNam
}
const getKrugerLng = (lng, degree = 3) => {
    if (degree == 6) {//6°带范围从0°~6°，取3°
        return Math.floor(lng / 6) * 6 + 3
    } else {//3°带范围从-1.5°~1.5°，取0°
        return Math.ceil((lng - 1.5) / 3) * 3
    }
}

async function fetchFileAsBlob(url) {
    try {
        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP 错误: ${response.status}`);

        const blob = await response.blob(); // 转换为 Blob 对象
        return blob;
    } catch (error) {
        console.error('请求失败:', error);
    }
}
const getMiddle=(p0:IXY,p1:IXY)=>{
    return new Vector2().copy(p0).add(new Vector2().copy(p1)).multiplyScalar(0.5)
}
const getLenth=(p0:IXY,p1:IXY)=>{
    return new Vector2().copy(p1).sub(new Vector2().copy(p0)).length()
}
function findFirstMinAndMaxXPointsWithIndex(points) {
    let minX = Infinity;
    let maxX = -Infinity;
    let minXPoint: any = null;
    let maxXPoint: any = null;
    let minXIndex = -1;
    let maxXIndex = -1;

    for (let i = 0; i < points.length; i++) {
        const [x, y] = points[i];

        // 更新最小值
        if (x < minX) {
            minX = x;
            minXPoint = [x, y];
            minXIndex = i;
        }

        // 更新最大值
        if (x > maxX) {
            maxX = x;
            maxXPoint = [x, y];
            maxXIndex = i;
        }
    }

    return {
        minXPoint,
        minXIndex,
        maxXPoint,
        maxXIndex
    };
}
export const getRedLinePAreas=( wasmObj )=>{
    let size=wasmObj.size()
    let vList:any=[]
    for(let i=0;i<size;i++){
        let pl=wasmObj.get(i)
        let plv=pl.getVertex()
        let arr:any=[]
        for(let j=0;j<plv.size();j++){
            arr.push([plv.get(j).x(),plv.get(j).y()])
        }
        vList.push(arr)
    }
    return vList
}

export const olplistMovecamera=(obj)=>{//根据坐标移动镜头并高亮
    let {x,y}=globalDxf.wcsOff
    let viewer=obj.viewer
    viewer.selectionScene.dynmScene.clear()
    let vertices:any=[]
    obj.plist.forEach(item=>{
        let v1=new THREE.Vector3(item.x-x, item.y-y, 0)
        vertices.push(v1)
    })
    // 2. 创建包围盒并从所有点计算
    const boundingBox = new THREE.Box3().setFromPoints(vertices);
    let centerP= boundingBox.getCenter(new THREE.Vector3())
    let size=boundingBox.getSize(new THREE.Vector3())
    let width=size.x
    let heigth=size.y
    const geometry = new THREE.BufferGeometry().setFromPoints(vertices);
    const material = new THREE.LineBasicMaterial({ color:'red'});
    const mesh1 = new THREE.LineLoop(geometry, material); //LineLoop
    viewer.selectionScene.dynmScene.add(mesh1)
    obj.cadmapviewer.map.getView().setCenter([centerP.x+x,centerP.y+y]);
    let view = obj.cadmapviewer.map.getView()
    let center= view.getCenter()
    let zoom=viewer.canvasWidth*5 / (Math.max(width, heigth)) 
    viewer.SetViewToObj(new Vector2(center[0],center[1]),zoom)
    viewer.Render()
    obj.cadmapviewer.syncViewToCad()
}
export const metaSearch=async(obj)=>{
    let metas=obj.metaList
    let ppc = new sg.ProgressCheck(obj.sgBDG, obj.VPlList);
    const vmetas = new sg.vector_ElementSelection
    metas.forEach(item=>{
        let els = new sg.ElementSelection();
        els.loadFromString(item.meta);
        els.setPaperId(dxfZsObj.drawingId);
        vmetas.push_back(els)
    })
    ppc.setPvElement(vmetas)
    ppc.match();
    const matchNums=ppc.getMatchElementCount()
    const matchNumList:any=[]
    let totals=await metstotal(metas)
    for(let i=0;i<matchNums.size();i++){
        let matchNum=matchNums.get(i)
        matchNumList.push({
            matchNum,
            total:totals[i],
            uuid:metas[i].uuid,
        })
    }
    console.log(matchNumList,'匹配总数');
    return matchNumList
}
const metstotal=(metaList)=>{
    let totals:any=[]
    for (let item of metaList) {
        let els = new sg.ElementSelection();
        els.loadFromString(item.meta);
        els.setPaperId(dxfZsObj.drawingId);
        let resObj = new sg.CadElementMatchAlgor(els)
        resObj.setNotIncludeText(true);
        let res= resObj.getResult()
        let total=res.getResultCount()
        totals.push(total)
    }
    return totals
}

