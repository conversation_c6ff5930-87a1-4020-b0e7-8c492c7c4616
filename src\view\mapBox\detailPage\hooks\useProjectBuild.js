import { ref } from 'vue'
import {
  queryMaterialComponentTree,
  queryCadMetaDetail
} from '@/view/mapBox/api/projectBuildApi'
import ExcelGenerator from './useExcel'


const useProjectBuild = (olBoxRef) => {
  const materialComponentTree = ref([]);
  const cadletaDetail = ref([]);

  const CBComponent = ref([
  ])
  const FixedComponent = ref([
  ])
  const CB_Fixed_Count = ref([
  ])
  const initial = async () => {
    // await loadMaterialComponentTree();
    await loadcadletaDetail();
    // exportExcel()

  }
  const handleFlatTree = (tree) => {
    const flatTree = [];
    const traverse = (node) => {
      flatTree.push(node);
      if (node.child) {
        node.child.forEach(ch => traverse(ch));
      }
    };
    tree.forEach(node => traverse(node));
    return flatTree;
  }
  const loadMaterialComponentTree = async () => {
    const res = await queryMaterialComponentTree();
    if (res?.data) {
      materialComponentTree.value = handleFlatTree(res.data);
      console.log('=========================================>', materialComponentTree.value);
    }
  }
  const loadcadletaDetail = async () => {
    const data = [
      {
        "field": "identifyItem",
        "value": "IDENTIFY",
        "pattern": "EQUAL"
      },
      {
        "field": "projectCode",
        "value": "PX20250724000003",
        "pattern": "EQUAL"
      }
    ]
    const params = {
      materialComponentCode: 'SWGJL0003',
      // materialComponentCode: 'SWGJL0006',
    }
    const res = await queryCadMetaDetail(data, params);
    if (res?.data) {
      const { data = [] } = res
      const legendList = data.map(({ uuid, meta }) => { return { uuid, meta } })
      const olRes = await olBoxRef.value.metaMatchNum(legendList)
      // 1. 预处理 res，建立映射
      const resMap = new Map(olRes.map(({ uuid, matchNum, total }) => [uuid, { matchNum, total }]));
      // 2. 一次性处理 data
      const result = data.map(item => {
        const stats = resMap.get(item.uuid);
        const insertRow = stats ? [{ label: '统计信息', ...stats }] : [];

        return item.materialList.reduce((acc, material) => {
          acc[material.materialComponentCode] = [
            ...material.attributes,
            ...(material.attributes.length > 0 ? insertRow : [])
          ];
          return acc;
        }, {});
      });

      //   {
      //     "dictionaryCode": "SWGJL0003",
      //     "SX001": "605",
      //     "SX002": "2",
      //     "SX033": "片",
      //     "matchNum": 0,
      //     "total": 3344
      // }
      const tableData = formatData(result)
      debugger
      // tableData.forEach(tab => {
      //   const total = tab.total
      //   const progress = Number(total) * Number(tab.SX001) / (10 ** 6)
      //   if (tab.dictionaryCode == "SWGJL0003") {
      //     CBComponent.value.push(
      //       ['-', tab.SX001 + 'Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', total, tab.SX033, progress + 'MW'],
      //     )
      //   } else if (tab.dictionaryCode == "SWGJL0006") {
      //     FixedComponent.value.push(
      //       ['-', tab.SX001 + 'Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', total, tab.SX033, progress + 'MW'],
      //     )
      //   }
      // })
      // 绑定 常规支架组件
      tableData['SWGJL0003']?.forEach((tab, index) => {
        const total = tab.total
        const progress = Number(total) * Number(tab.SX001) / (10 ** 6)
        CBComponent.value.push(
          [`1.${index + 1}`, tab.SX001 + 'Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', total, tab.SX033, progress + 'MW'],
        )
      })
      // 
      // 绑定 固定支架
      tableData['SWGJL0006']?.forEach((tab, index) => {
        const total = tab.total
        const progress = Number(total) * Number(tab.SX001) / (10 ** 6)
        FixedComponent.value.push(
          [`2.${index + 1}`, tab.SX001 + 'Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', total, tab.SX033, progress + 'MW'],
        )
      })


      function getTotals(result) {
        let totalSum = 0;
        let weightedSum = 0;

        Object.keys(result).forEach(code => {
          const items = result[code];
          if (!Array.isArray(items)) return;

          items.forEach(item => {
            const total = item.total || 0;
            const sx001 = parseFloat(item.SX001) || 0;

            totalSum += total;
            weightedSum += total * sx001; // total × SX001
          });
        });

        return {
          total: totalSum,
          wm_total: weightedSum / (10 ** 6)
        };
      }
      const { total, wm_total } = getTotals(tableData)
      CB_Fixed_Count.value = [
        ['总计', '', '', '', total, '片', `${wm_total}MW`],
      ]
      exportExcel()
      console.log(tableData);
      debugger
    }
  }
  const getExcelRowData = () => {
    return [
      ['一', '低压安装'],
      ['1', '常规支架组件'],
      ...CBComponent.value,
      ['2', '固定支架'],
      ...FixedComponent.value,
      ...CB_Fixed_Count.value,
      ['二', '低压安装'],
      ['三', '光伏支架基础/桥架基础/箱变基础'],
    ]
  }
  const exportExcel = async () => {
    const generator = new ExcelGenerator();
    generator.generate(getExcelRowData());
  }
  // 处理常规支架组件,固定支架
  const formatData = (data) => {
    const result = data.reduce((acc, item) => {
      // 遍历每个 materialComponent，如 SWGJL0003、SWGJL0006
      Object.keys(item).forEach(code => {
        const attrs = item[code];
        if (!Array.isArray(attrs) || attrs.length === 0) return;

        // 提取属性和统计信息
        const properties = attrs.filter(attr => attr.dictionaryCode);
        const stats = attrs.find(attr => attr.label === '统计信息');

        if (!stats) return;

        const sx001 = properties.find(p => p.dictionaryCode === 'SX001');
        if (!sx001) return;

        // 🎯 初始化该 component 的数组
        if (!acc[code]) {
          acc[code] = [];
        }

        // 查找当前 component 是否已有基于 SX001 的对象
        let existing = acc[code].find(item => item.SX001 === sx001.defaultValue);

        if (!existing) {
          // 🔹 如果没有，创建新对象
          existing = {
            SX001: sx001.defaultValue,
            SX002: null,
            SX033: null,
            matchNum: 0,
            total: 0
          };
          acc[code].push(existing);
        }

        // 补充 SX002（只填一次）
        if (!existing.SX002) {
          const sx002 = properties.find(p => p.dictionaryCode === 'SX002');
          if (sx002) existing.SX002 = sx002.defaultValue;
        }

        // 补充 SX033 单位（只填一次）
        if (!existing.SX033) {
          const sx033 = properties.find(p => p.dictionaryCode === 'SX033');
          if (sx033) {
            existing.SX033 = sx033.calculateRuleUnitName || sx033.calculateRuleUnitCode || '';
          }
        }

        // 🔺 累加统计值
        existing.matchNum += stats.matchNum || 0;
        existing.total += stats.total || 0;
      });

      return acc;
    }, {});
    return result
  }
  // initial()
  return {
    materialComponentTree,
    cadletaDetail,
    initial,


  }
};
export default useProjectBuild;