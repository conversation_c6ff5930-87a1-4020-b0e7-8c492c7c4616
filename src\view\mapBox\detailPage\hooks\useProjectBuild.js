import { ref, computed, reactive } from 'vue'
import {
  queryMaterialComponentTree,
  queryCadMetaDetail
} from '@/view/mapBox/api/projectBuildApi'
import { generateExcel } from './useExcel'

/**
 * 项目构建数据管理 Hook
 * @param {Object} olBoxRef - OL地图组件引用
 * @param {Object} options - 配置选项
 * @returns {Object} 项目构建相关的状态和方法
 */
const useProjectBuild = (olBoxRef, options = {}) => {
  // 常量定义
  const CONSTANTS = {
    MATERIAL_CODES: {
      CONVENTIONAL_BRACKET: 'SWGJL0003', // 常规支架组件
      FIXED_BRACKET: 'SWGJL0006',        // 固定支架
    },
    DICTIONARY_CODES: {
      POWER: 'SX001',      // 功率
      THICKNESS: 'SX002',  // 厚度
      UNIT: 'SX033',       // 单位
    },
    POWER_CONVERSION_FACTOR: 10 ** 6, // MW转换因子
    DEFAULT_PROJECT_CODE: 'PX20250724000003',
    EXCEL_SECTIONS: {
      LOW_VOLTAGE_INSTALL: '低压安装',
      CONVENTIONAL_BRACKET: '常规支架组件',
      FIXED_BRACKET: '固定支架',
      FOUNDATION: '光伏支架基础/桥架基础/箱变基础'
    }
  }

  // 响应式状态
  const state = reactive({
    materialComponentTree: [],
    cadletaDetail: [],
    CBComponent: [],
    FixedComponent: [],
    CB_Fixed_Count: [],
    loading: false,
    error: null,
    projectCode: options.projectCode || CONSTANTS.DEFAULT_PROJECT_CODE
  })

  // 计算属性
  const totalComponents = computed(() =>
    state.CBComponent.length + state.FixedComponent.length
  )

  const hasData = computed(() =>
    state.CBComponent.length > 0 || state.FixedComponent.length > 0
  )

  /**
   * 扁平化树结构
   * @param {Array} tree - 树形数据
   * @returns {Array} 扁平化后的数组
   */
  const flattenTree = (tree) => {
    if (!Array.isArray(tree)) {
      console.warn('flattenTree: 输入不是数组格式')
      return []
    }

    const result = []

    const traverse = (node) => {
      if (!node) return

      result.push(node)

      if (Array.isArray(node.child)) {
        node.child.forEach(traverse)
      }
    }

    tree.forEach(traverse)
    return result
  }

  /**
   * 加载材料组件树
   */
  const loadMaterialComponentTree = async () => {
    try {
      state.loading = true
      state.error = null

      const response = await queryMaterialComponentTree()

      if (response?.data) {
        state.materialComponentTree = flattenTree(response.data)
      } else {
        throw new Error('材料组件树数据为空')
      }
    } catch (error) {
      state.error = `加载材料组件树失败: ${error.message}`
      console.error('loadMaterialComponentTree error:', error)
    } finally {
      state.loading = false
    }
  }
  /**
   * 创建查询参数
   * @param {string} projectCode - 项目代码
   * @param {string} materialComponentCode - 材料组件代码
   * @returns {Object} 查询参数对象
   */
  const createQueryParams = (projectCode, materialComponentCode) => {
    return {
      data: [
        {
          field: "identifyItem",
          value: "IDENTIFY",
          pattern: "EQUAL"
        },
        {
          field: "projectCode",
          value: projectCode,
          pattern: "EQUAL"
        }
      ],
      params: {
        materialComponentCode
      }
    }
  }

  /**
   * 处理图例匹配数据
   * @param {Array} data - 原始数据
   * @param {Array} olRes - OL匹配结果
   * @returns {Array} 处理后的数据
   */
  const processLegendData = (data, olRes) => {
    if (!Array.isArray(data) || !Array.isArray(olRes)) {
      throw new Error('processLegendData: 输入参数必须是数组')
    }

    // 建立UUID到统计信息的映射
    const statsMap = new Map(
      olRes.map(({ uuid, matchNum, total }) => [uuid, { matchNum, total }])
    )

    return data.map(item => {
      const stats = statsMap.get(item.uuid)
      const statsRow = stats ? [{ label: '统计信息', ...stats }] : []

      return item.materialList.reduce((acc, material) => {
        const { materialComponentCode, attributes = [] } = material

        acc[materialComponentCode] = [
          ...attributes,
          ...(attributes.length > 0 ? statsRow : [])
        ]

        return acc
      }, {})
    })
  }

  /**
   * 计算总计数据
   * @param {Object} formattedData - 格式化后的数据
   * @returns {Object} 总计信息
   */
  const calculateTotals = (formattedData) => {
    let totalSum = 0
    let weightedSum = 0

    Object.values(formattedData).forEach(items => {
      if (!Array.isArray(items)) return

      items.forEach(item => {
        const total = Number(item.total) || 0
        const power = Number(item[CONSTANTS.DICTIONARY_CODES.POWER]) || 0

        totalSum += total
        weightedSum += total * power
      })
    })

    return {
      total: totalSum,
      weightedTotal: weightedSum / CONSTANTS.POWER_CONVERSION_FACTOR
    }
  }

  /**
   * 生成Excel行数据
   * @param {Object} componentData - 组件数据
   * @param {string} prefix - 序号前缀
   * @param {string} description - 描述信息
   * @returns {Array} Excel行数据
   */
  const generateExcelRows = (componentData, prefix, description = '1.材质：镀锌钢转支架;\n2.固定桩;') => {
    if (!Array.isArray(componentData)) return []

    return componentData.map((item, index) => {
      const total = Number(item.total) || 0
      const power = Number(item[CONSTANTS.DICTIONARY_CODES.POWER]) || 0
      const unit = item[CONSTANTS.DICTIONARY_CODES.UNIT] || '片'
      const progress = (total * power) / CONSTANTS.POWER_CONVERSION_FACTOR

      return [
        `${prefix}.${index + 1}`,
        `${power}Wp`,
        description,
        '按图纸设计数量以吨计算',
        total,
        unit,
        `${progress.toFixed(2)}MW`
      ]
    })
  }

  /**
   * 加载CAD元数据详情
   */
  const loadCadMetaDetail = async () => {
    try {
      state.loading = true
      state.error = null

      // 清空之前的数据
      state.CBComponent = []
      state.FixedComponent = []
      state.CB_Fixed_Count = []

      // 查询常规支架组件数据
      const conventionalQuery = createQueryParams(
        state.projectCode,
        CONSTANTS.MATERIAL_CODES.CONVENTIONAL_BRACKET
      )

      const conventionalRes = await queryCadMetaDetail(
        conventionalQuery.data,
        conventionalQuery.params
      )

      if (!conventionalRes?.data) {
        throw new Error('常规支架组件数据查询失败')
      }

      // 处理数据
      await processComponentData(conventionalRes.data, CONSTANTS.MATERIAL_CODES.CONVENTIONAL_BRACKET)

      // 查询固定支架数据（如果需要）
      const fixedQuery = createQueryParams(
        state.projectCode,
        CONSTANTS.MATERIAL_CODES.FIXED_BRACKET
      )

      const fixedRes = await queryCadMetaDetail(
        fixedQuery.data,
        fixedQuery.params
      )

      if (fixedRes?.data) {
        await processComponentData(fixedRes.data, CONSTANTS.MATERIAL_CODES.FIXED_BRACKET)
      }

      // 计算总计
      const allData = {
        [CONSTANTS.MATERIAL_CODES.CONVENTIONAL_BRACKET]: state.CBComponent,
        [CONSTANTS.MATERIAL_CODES.FIXED_BRACKET]: state.FixedComponent
      }

      const { total, weightedTotal } = calculateTotals(allData)
      state.CB_Fixed_Count = [
        ['总计', '', '', '', total, '片', `${weightedTotal.toFixed(2)}MW`]
      ]

      // 自动导出Excel
      await exportExcel()

    } catch (error) {
      state.error = `加载CAD元数据详情失败: ${error.message}`
      console.error('loadCadMetaDetail error:', error)
    } finally {
      state.loading = false
    }
  }
  /**
   * 处理组件数据
   * @param {Array} data - 原始数据
   * @param {string} materialCode - 材料代码
   */
  const processComponentData = async (data, materialCode) => {
    try {
      if (!olBoxRef?.value?.metaMatchNum) {
        throw new Error('olBoxRef 或 metaMatchNum 方法不可用')
      }

      // 提取图例列表
      const legendList = data.map(({ uuid, meta }) => ({ uuid, meta }))

      // 获取匹配数据
      const olRes = await olBoxRef.value.metaMatchNum(legendList)

      // 处理数据
      const processedData = processLegendData(data, olRes)
      const formattedData = formatComponentData(processedData)

      // 根据材料代码分配到对应的组件数组
      if (materialCode === CONSTANTS.MATERIAL_CODES.CONVENTIONAL_BRACKET) {
        const conventionalData = formattedData[materialCode] || []
        state.CBComponent = generateExcelRows(conventionalData, '1')
      } else if (materialCode === CONSTANTS.MATERIAL_CODES.FIXED_BRACKET) {
        const fixedData = formattedData[materialCode] || []
        state.FixedComponent = generateExcelRows(fixedData, '2')
      }

    } catch (error) {
      console.error(`处理组件数据失败 (${materialCode}):`, error)
      throw error
    }
  }

  /**
   * 格式化组件数据
   * @param {Array} data - 处理后的数据
   * @returns {Object} 格式化后的数据
   */
  const formatComponentData = (data) => {
    if (!Array.isArray(data)) {
      console.warn('formatComponentData: 输入不是数组格式')
      return {}
    }

    return data.reduce((acc, item) => {
      Object.keys(item).forEach(code => {
        const attributes = item[code]
        if (!Array.isArray(attributes) || attributes.length === 0) return

        // 分离属性和统计信息
        const properties = attributes.filter(attr => attr.dictionaryCode)
        const stats = attributes.find(attr => attr.label === '统计信息')

        if (!stats) return

        // 查找功率属性
        const powerAttr = properties.find(p =>
          p.dictionaryCode === CONSTANTS.DICTIONARY_CODES.POWER
        )
        if (!powerAttr) return

        // 初始化组件数组
        if (!acc[code]) {
          acc[code] = []
        }

        // 查找或创建基于功率的对象
        let existing = acc[code].find(item =>
          item[CONSTANTS.DICTIONARY_CODES.POWER] === powerAttr.defaultValue
        )

        if (!existing) {
          existing = {
            [CONSTANTS.DICTIONARY_CODES.POWER]: powerAttr.defaultValue,
            [CONSTANTS.DICTIONARY_CODES.THICKNESS]: null,
            [CONSTANTS.DICTIONARY_CODES.UNIT]: null,
            matchNum: 0,
            total: 0
          }
          acc[code].push(existing)
        }

        // 补充其他属性
        const thicknessAttr = properties.find(p =>
          p.dictionaryCode === CONSTANTS.DICTIONARY_CODES.THICKNESS
        )
        if (thicknessAttr && !existing[CONSTANTS.DICTIONARY_CODES.THICKNESS]) {
          existing[CONSTANTS.DICTIONARY_CODES.THICKNESS] = thicknessAttr.defaultValue
        }

        const unitAttr = properties.find(p =>
          p.dictionaryCode === CONSTANTS.DICTIONARY_CODES.UNIT
        )
        if (unitAttr && !existing[CONSTANTS.DICTIONARY_CODES.UNIT]) {
          existing[CONSTANTS.DICTIONARY_CODES.UNIT] =
            unitAttr.calculateRuleUnitName || unitAttr.calculateRuleUnitCode || '片'
        }

        // 累加统计值
        existing.matchNum += Number(stats.matchNum) || 0
        existing.total += Number(stats.total) || 0
      })

      return acc
    }, {})
  }

  /**
   * 获取Excel行数据
   * @returns {Array} Excel行数据
   */
  const getExcelRowData = () => {
    return [
      ['一', CONSTANTS.EXCEL_SECTIONS.LOW_VOLTAGE_INSTALL],
      ['1', CONSTANTS.EXCEL_SECTIONS.CONVENTIONAL_BRACKET],
      ...state.CBComponent,
      ['2', CONSTANTS.EXCEL_SECTIONS.FIXED_BRACKET],
      ...state.FixedComponent,
      ...state.CB_Fixed_Count,
      ['二', CONSTANTS.EXCEL_SECTIONS.LOW_VOLTAGE_INSTALL],
      ['三', CONSTANTS.EXCEL_SECTIONS.FOUNDATION],
    ]
  }

  /**
   * 导出Excel文件
   */
  const exportExcel = async () => {
    try {
      const excelData = getExcelRowData()

      if (excelData.length <= 4) { // 只有标题行，没有实际数据
        throw new Error('没有可导出的数据')
      }

      await generateExcel(excelData, {
        filename: `光伏工程量清单_${state.projectCode}.xlsx`,
        title: '光伏区安装分部分项工程量清单'
      })

    } catch (error) {
      state.error = `导出Excel失败: ${error.message}`
      console.error('exportExcel error:', error)
      throw error
    }
  }

  /**
   * 初始化数据
   */
  const initialize = async () => {
    try {
      await loadMaterialComponentTree()
      await loadCadMetaDetail()
    } catch (error) {
      state.error = `初始化失败: ${error.message}`
      console.error('initialize error:', error)
    }
  }

  /**
   * 重置状态
   */
  const reset = () => {
    state.CBComponent = []
    state.FixedComponent = []
    state.CB_Fixed_Count = []
    state.error = null
    state.loading = false
  }

  /**
   * 设置项目代码
   * @param {string} projectCode - 项目代码
   */
  const setProjectCode = (projectCode) => {
    if (typeof projectCode === 'string' && projectCode.trim()) {
      state.projectCode = projectCode.trim()
    }
  }

  // 返回公共接口
  return {
    // 状态
    ...state,

    // 计算属性
    totalComponents,
    hasData,

    // 方法
    initialize,
    loadMaterialComponentTree,
    loadCadMetaDetail,
    exportExcel,
    reset,
    setProjectCode,

    // 工具方法
    getExcelRowData,

    // 兼容性 - 保持原有接口
    initial: initialize,
    materialComponentTree: computed(() => state.materialComponentTree),
    cadletaDetail: computed(() => state.cadletaDetail)
  }
}
export default useProjectBuild;