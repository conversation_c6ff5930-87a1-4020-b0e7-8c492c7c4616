
import { NativeArray, NativeTypedArray } from "../constant"
/** Typed-array-based buffer which can be dynamically extended. */
export class DynamicBuffer {
    /**
     * @param type Array type, see NativeType.
     * @param initialCapacity Initial capacity, number of elements.
     */
    public type:number
    public capacity:number
    public size:number
    public buffer:NativeTypedArray
    constructor(type, initialCapacity = 16) {
        this.type = type
        this.capacity = initialCapacity
        this.size = 0
        this.buffer = new (NativeArray(type))(initialCapacity)
    }

    GetSize() {
        return this.size
    }

    /**
     * Append new value to the buffer end.
     * @return Appended value position in the buffer.
     */
    Push(value) {
        this._CheckGrow()
        const pos = this.size
        this.buffer[pos] = value
        this.size++
        return pos
    }

    Get(index) {
        if (index >= this.size) {
            throw new Error(`Index out of range: ${index}/${this.size}`)
        }
        return this.buffer[index]
    }

    /** Copy content to the specified buffer.
     * @param dstBuffer Destination buffer, should be typed array of the same type.
     * @param dstOffset {number} Offset in elements in the destination buffer.
     * @param srcOffset {number} Offset in elements in this buffer.
     * @param size {number} Number of elements to copy, -1 (default) to copy till this buffer end.
     */
    CopyTo(dstBuffer, dstOffset, srcOffset = 0, size = -1) {
        if (size === -1) {
            size = this.size - srcOffset
        }
        const src = new (NativeArray(this.type))(this.buffer.buffer, srcOffset, size)
        dstBuffer.set(src, dstOffset)
    }

    _CheckGrow() {
        if (this.size < this.capacity) {
            return
        }
        this.capacity *= 2
        const newBuffer = new (NativeArray(this.type))(this.capacity)
        newBuffer.set(this.buffer)
        this.buffer = newBuffer
    }
}

