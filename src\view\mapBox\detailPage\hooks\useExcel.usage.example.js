/**
 * useExcel.js 使用示例
 * 展示如何使用修改后的Excel生成功能，特别是特殊合并标题行功能
 */

import { generateExcel, createExcelGenerator } from './useExcel.js'

/**
 * 示例1: 使用便捷函数生成Excel
 */
export async function example1_ConvenienceFunction() {
  // 模拟从 useProjectBuild 获取的数据
  const excelData = [
    ['一', '低压安装'],
    ['1', '常规支架组件'],  // 这行会被特殊处理，B:G列合并
    ['1.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 3344, '片', '2023.32MW'],
    ['1.2', '610Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 1500, '片', '915.00MW'],
    ['2', '固定支架'],      // 这行会被特殊处理，B:G列合并
    ['2.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 2500, '片', '1512.50MW'],
    ['总计', '', '', '', 7344, '片', '4450.82MW'],
    ['二', '低压安装'],
    ['三', '光伏支架基础/桥架基础/箱变基础']
  ]

  try {
    await generateExcel(excelData, {
      title: '光伏区安装分部分项工程量清单',
      filename: '光伏工程量清单_示例1.xlsx'
    })
    
    console.log('✅ Excel文件生成成功 - 示例1')
    console.log('📋 特殊处理的标题行:')
    console.log('   - "常规支架组件" 行的B:G列已合并')
    console.log('   - "固定支架" 行的B:G列已合并')
    
  } catch (error) {
    console.error('❌ Excel生成失败:', error.message)
  }
}

/**
 * 示例2: 使用Excel生成器类进行更精细的控制
 */
export async function example2_ExcelGeneratorClass() {
  const excelData = [
    ['一', '低压安装'],
    ['1', '常规支架组件'],
    ['1.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 100, '片', '60.50MW'],
    ['1.2', '610Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 200, '片', '122.00MW'],
    ['2', '固定支架'],
    ['2.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 150, '片', '90.75MW'],
    ['总计', '', '', '', 450, '片', '273.25MW'],
    ['二', '低压安装'],
    ['三', '光伏支架基础/桥架基础/箱变基础']
  ]

  const generator = createExcelGenerator({
    title: '自定义标题 - 光伏工程量清单',
    worksheetName: '工程量清单_详细',
    columnWidths: [12, 35, 35, 45, 18, 18, 35] // 自定义列宽
  })

  try {
    await generator.generate(excelData, '光伏工程量清单_示例2.xlsx')
    
    console.log('✅ Excel文件生成成功 - 示例2')
    console.log('📋 使用了自定义配置:')
    console.log('   - 自定义标题')
    console.log('   - 自定义工作表名称')
    console.log('   - 自定义列宽')
    
  } catch (error) {
    console.error('❌ Excel生成失败:', error.message)
  } finally {
    // 确保资源被清理
    generator.dispose()
  }
}

/**
 * 示例3: 与 useProjectBuild 集成使用
 */
export async function example3_IntegrateWithProjectBuild() {
  // 模拟 useProjectBuild 的使用场景
  
  // 假设这是从 useProjectBuild 获取的数据
  const projectBuildData = {
    CBComponent: [
      ['1.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 3344, '片', '2023.32MW'],
      ['1.2', '610Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 1500, '片', '915.00MW']
    ],
    FixedComponent: [
      ['2.1', '605Wp', '1.材质：镀锌钢转支架;\n2.固定桩;', '按图纸设计数量以吨计算', 2500, '片', '1512.50MW']
    ],
    CB_Fixed_Count: [
      ['总计', '', '', '', 7344, '片', '4450.82MW']
    ]
  }

  // 构建Excel数据结构
  const excelData = [
    ['一', '低压安装'],
    ['1', '常规支架组件'],  // 特殊合并标题
    ...projectBuildData.CBComponent,
    ['2', '固定支架'],      // 特殊合并标题
    ...projectBuildData.FixedComponent,
    ...projectBuildData.CB_Fixed_Count,
    ['二', '低压安装'],
    ['三', '光伏支架基础/桥架基础/箱变基础']
  ]

  try {
    await generateExcel(excelData, {
      title: '光伏区安装分部分项工程量清单',
      filename: `光伏工程量清单_${new Date().toISOString().slice(0, 10)}.xlsx`
    })
    
    console.log('✅ 与 useProjectBuild 集成成功')
    console.log('📊 数据统计:')
    console.log(`   - 常规支架组件: ${projectBuildData.CBComponent.length} 项`)
    console.log(`   - 固定支架: ${projectBuildData.FixedComponent.length} 项`)
    console.log(`   - 总行数: ${excelData.length} 行`)
    
  } catch (error) {
    console.error('❌ 集成使用失败:', error.message)
  }
}

/**
 * 示例4: 错误处理和数据验证
 */
export async function example4_ErrorHandlingAndValidation() {
  console.log('🔍 测试错误处理和数据验证...')

  // 测试用例1: 无效数据格式
  const invalidData1 = [
    ['1', '常规支架组件'],
    'invalid row', // 这行会导致验证失败
    ['1.1', '605Wp']
  ]

  try {
    await generateExcel(invalidData1, {
      filename: '测试_无效数据.xlsx'
    })
  } catch (error) {
    console.log('✅ 正确捕获了无效数据错误:', error.message)
  }

  // 测试用例2: 空数据
  const emptyData = []

  try {
    await generateExcel(emptyData, {
      filename: '测试_空数据.xlsx'
    })
    console.log('✅ 空数据处理成功')
  } catch (error) {
    console.log('ℹ️ 空数据处理结果:', error.message)
  }

  // 测试用例3: 正常数据但包含特殊字符
  const specialCharData = [
    ['一', '低压安装'],
    ['1', '常规支架组件'],
    ['1.1', '605Wp\n多行文本', '特殊字符: @#$%^&*()', '计算规则\n换行测试', 100, '片/套', '60.50MW']
  ]

  try {
    await generateExcel(specialCharData, {
      filename: '测试_特殊字符.xlsx'
    })
    console.log('✅ 特殊字符处理成功')
  } catch (error) {
    console.error('❌ 特殊字符处理失败:', error.message)
  }
}

/**
 * 示例5: 性能测试（大数据量）
 */
export async function example5_PerformanceTest() {
  console.log('⚡ 开始性能测试...')

  // 生成大量测试数据
  const largeData = [
    ['一', '低压安装'],
    ['1', '常规支架组件']
  ]

  // 添加1000行常规支架组件数据
  for (let i = 1; i <= 1000; i++) {
    largeData.push([
      `1.${i}`,
      `${600 + i}Wp`,
      '1.材质：镀锌钢转支架;\n2.固定桩;',
      '按图纸设计数量以吨计算',
      Math.floor(Math.random() * 1000) + 100,
      '片',
      `${(Math.random() * 1000).toFixed(2)}MW`
    ])
  }

  largeData.push(['2', '固定支架'])

  // 添加500行固定支架数据
  for (let i = 1; i <= 500; i++) {
    largeData.push([
      `2.${i}`,
      `${605 + i}Wp`,
      '1.材质：镀锌钢转支架;\n2.固定桩;',
      '按图纸设计数量以吨计算',
      Math.floor(Math.random() * 500) + 50,
      '片',
      `${(Math.random() * 500).toFixed(2)}MW`
    ])
  }

  largeData.push(
    ['总计', '', '', '', 750000, '片', '450000.00MW'],
    ['二', '低压安装'],
    ['三', '光伏支架基础/桥架基础/箱变基础']
  )

  const startTime = performance.now()

  try {
    await generateExcel(largeData, {
      filename: '性能测试_大数据量.xlsx'
    })
    
    const endTime = performance.now()
    const duration = endTime - startTime
    
    console.log('✅ 性能测试完成')
    console.log(`📊 数据统计:`)
    console.log(`   - 总行数: ${largeData.length}`)
    console.log(`   - 处理时间: ${duration.toFixed(2)}ms`)
    console.log(`   - 平均每行: ${(duration / largeData.length).toFixed(2)}ms`)
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error.message)
  }
}

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  console.log('🚀 开始运行所有Excel使用示例\n')

  const examples = [
    { name: '便捷函数使用', fn: example1_ConvenienceFunction },
    { name: 'Excel生成器类使用', fn: example2_ExcelGeneratorClass },
    { name: '与useProjectBuild集成', fn: example3_IntegrateWithProjectBuild },
    { name: '错误处理和数据验证', fn: example4_ErrorHandlingAndValidation },
    { name: '性能测试', fn: example5_PerformanceTest }
  ]

  for (const { name, fn } of examples) {
    console.log(`\n📋 运行示例: ${name}`)
    console.log('─'.repeat(50))
    
    try {
      await fn()
    } catch (error) {
      console.error(`❌ 示例 "${name}" 运行失败:`, error.message)
    }
    
    console.log('─'.repeat(50))
  }

  console.log('\n🎉 所有示例运行完成')
}

// 如果在浏览器环境中，将示例函数挂载到全局对象
if (typeof window !== 'undefined') {
  window.excelExamples = {
    example1_ConvenienceFunction,
    example2_ExcelGeneratorClass,
    example3_IntegrateWithProjectBuild,
    example4_ErrorHandlingAndValidation,
    example5_PerformanceTest,
    runAllExamples
  }
  
  console.log('📋 Excel使用示例已挂载到 window.excelExamples')
  console.log('💡 使用 window.excelExamples.runAllExamples() 运行所有示例')
}
