import { Float32BufferAttribute, LineSegments, Vector2 } from "three";

const _start = /*@__PURE__*/ new Vector2();
const _end = /*@__PURE__*/ new Vector2();
export class CadLineSegments extends LineSegments{
    override computeLineDistances(){
        const geometry = this.geometry;
		const ids=geometry.attributes.entityId.array;
		// we assume non-indexed geometry
		if ( geometry.index === null ) {

			const positionAttribute = geometry.attributes.position;
			const lineDistances = [ 0 ];

			for ( let i = 1, l = positionAttribute.count; i < l; i ++ ) {

				_start.fromBufferAttribute( positionAttribute, i - 1 );
				_end.fromBufferAttribute( positionAttribute, i );

				lineDistances[ i ] = lineDistances[ i - 1 ];
				lineDistances[ i ] += _start.distanceTo( _end );
				if(ids[i]!=ids[i-1]){
					lineDistances[i]=0
				}
			}

			geometry.setAttribute( 'lineDistance', new Float32BufferAttribute( lineDistances, 1 ) );

		} else {

			console.warn( 'computeLineDistances(): Computation only possible with non-indexed BufferGeometry.' );

		}
        return this
    }
}