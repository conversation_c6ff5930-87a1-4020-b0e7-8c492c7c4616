import { MyEvent } from "./myevent"
import { EventDispatcher } from "./EventDispatcher"

export type HTMLEventElement=HTMLCanvasElement|HTMLDivElement

export interface IXY{
    x:number,
    y:number
}

export class DOMEvent extends MyEvent{
    public dom:HTMLEventElement
    public _drag:boolean=false
    public _st
    public _en
    constructor(dom:HTMLEventElement){
        super()
        this.dom=dom
    }
    public override dispose(){
        this.removeEvents()
    }
    public addEvents(){
        this.dom.addEventListener('mousemove',this._onmousemove)
        this.dom.addEventListener('mousedown',this._onmousedown)
        this.dom.addEventListener('mouseup',this._onmouseup)
        this.dom.addEventListener('wheel',this._onwheel)
    }
    public removeEvents(){
        this.dom.removeEventListener('mousemove',this._onmousemove)
        this.dom.removeEventListener('mousedown',this._onmousedown)
        this.dom.removeEventListener('mouseup',this._onmouseup)
        this.dom.removeEventListener('wheel',this._onwheel)
    }
}