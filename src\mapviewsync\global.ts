import { reactive } from "vue";

export const tips=reactive({
    lnglat:{
        lng:0,
        lat:0
    },
    mouse:{
        x:0,
        y:0
    },
    zoom:2,
    scale:1,
    syncview:1
})

export enum ButtonStatus {
    Left = 0,
    Middle = 1,
    Right = 2
}

export const metalist=[
    {
        uuid:'111',
        meta:"{\n   \"m_objList\" : [\n      {\n         \"curve\" : {\n            \"curves\" : [\n               {\n                  \"endpoint\" : {\n                     \"x\" : 593011.34312099998,\n                     \"y\" : 4198342.6455699978\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 592946.73912100005,\n                     \"y\" : 4198342.6455699978\n                  },\n                  \"type\" : \"LineSegment2d\"\n               }\n            ],\n            \"m_bulge\" : [ 0.0, 0.0 ],\n            \"m_isClosed\" : false,\n            \"m_vertex\" : [\n               {\n                  \"x\" : 592946.73912100005,\n                  \"y\" : 4198342.6455699978\n               },\n               {\n                  \"x\" : 593011.34312099998,\n                  \"y\" : 4198342.6455699978\n               }\n            ],\n            \"type\" : \"PolyLine2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 2,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjComBinCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670002\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 593010.39112100005,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 32,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670032\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 593006.091121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 31,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670031\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 593001.591121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 30,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670030\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592997.091121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 29,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670029\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592992.591121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 28,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670028\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592988.091121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 27,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670027\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592983.591121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 26,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670026\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592979.091121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 25,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670025\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592970.09112100024,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 23,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670023\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592965.59112100024,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 22,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670022\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592961.09112100012,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 21,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670021\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592956.591121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 20,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670020\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592952.091121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 19,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670019\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592947.79112099996,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 18,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670018\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 593010.39112100005,\n               \"y\" : 4198340.5142740784\n            },\n            \"startpoint\" : {\n               \"x\" : 593010.39112100005,\n               \"y\" : 4198344.7768659201\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 17,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670017\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 593006.091121,\n               \"y\" : 4198344.7768659201\n            },\n            \"startpoint\" : {\n               \"x\" : 593006.091121,\n               \"y\" : 4198340.5142740784\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 16,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670016\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 593001.591121,\n               \"y\" : 4198340.5142740784\n            },\n            \"startpoint\" : {\n               \"x\" : 593001.591121,\n               \"y\" : 4198344.7768659201\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 15,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670015\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592997.091121,\n               \"y\" : 4198344.7768659201\n            },\n            \"startpoint\" : {\n               \"x\" : 592997.091121,\n               \"y\" : 4198340.5142740784\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 14,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670014\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592992.591121,\n               \"y\" : 4198340.5142740784\n            },\n            \"startpoint\" : {\n               \"x\" : 592992.591121,\n               \"y\" : 4198344.7768659201\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 13,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670013\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592983.591121,\n               \"y\" : 4198340.5142740784\n            },\n            \"startpoint\" : {\n               \"x\" : 592983.591121,\n               \"y\" : 4198344.7768659201\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 11,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670011\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592979.091121,\n               \"y\" : 4198344.7768659201\n            },\n            \"startpoint\" : {\n               \"x\" : 592979.091121,\n               \"y\" : 4198340.5142740784\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 10,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670010\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592974.591121,\n               \"y\" : 4198340.5142740784\n            },\n            \"startpoint\" : {\n               \"x\" : 592974.591121,\n               \"y\" : 4198344.7768659201\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 9,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670009\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592970.09112100024,\n               \"y\" : 4198344.7768659201\n            },\n            \"startpoint\" : {\n               \"x\" : 592970.09112100024,\n               \"y\" : 4198340.5142740784\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 8,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670008\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592965.59112100024,\n               \"y\" : 4198340.5142740784\n            },\n            \"startpoint\" : {\n               \"x\" : 592965.59112100024,\n               \"y\" : 4198344.7768659201\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 7,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670007\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592956.591121,\n               \"y\" : 4198340.5142740784\n            },\n            \"startpoint\" : {\n               \"x\" : 592956.591121,\n               \"y\" : 4198344.7768659201\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 5,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670005\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592952.091121,\n               \"y\" : 4198344.7768659201\n            },\n            \"startpoint\" : {\n               \"x\" : 592952.091121,\n               \"y\" : 4198340.5142740784\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 4,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670004\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592947.79112099996,\n               \"y\" : 4198340.5142740784\n            },\n            \"startpoint\" : {\n               \"x\" : 592947.79112099996,\n               \"y\" : 4198344.7768659201\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 3,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670003\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592961.09112100012,\n               \"y\" : 4198344.7768659201\n            },\n            \"startpoint\" : {\n               \"x\" : 592961.09112100012,\n               \"y\" : 4198340.5142740784\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 6,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670006\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 592988.091121,\n               \"y\" : 4198344.7768659201\n            },\n            \"startpoint\" : {\n               \"x\" : 592988.091121,\n               \"y\" : 4198340.5142740784\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 12,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670012\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 592974.591121,\n               \"y\" : 4198342.6455699978\n            },\n            \"radius\" : 0.14999999999999999,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 24,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670024\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"curves\" : [\n               {\n                  \"endpoint\" : {\n                     \"x\" : 592946.73912100005,\n                     \"y\" : 4198340.5142740784\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 592946.73912100005,\n                     \"y\" : 4198344.7768659201\n                  },\n                  \"type\" : \"LineSegment2d\"\n               },\n               {\n                  \"endpoint\" : {\n                     \"x\" : 593011.34312099998,\n                     \"y\" : 4198340.5142740784\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 592946.73912100005,\n                     \"y\" : 4198340.5142740784\n                  },\n                  \"type\" : \"LineSegment2d\"\n               },\n               {\n                  \"endpoint\" : {\n                     \"x\" : 593011.34312099998,\n                     \"y\" : 4198344.7768659201\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 593011.34312099998,\n                     \"y\" : 4198340.5142740784\n                  },\n                  \"type\" : \"LineSegment2d\"\n               },\n               {\n                  \"endpoint\" : {\n                     \"x\" : 592946.73912100005,\n                     \"y\" : 4198344.7768659201\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 593011.34312099998,\n                     \"y\" : 4198344.7768659201\n                  },\n                  \"type\" : \"LineSegment2d\"\n               }\n            ],\n            \"m_bulge\" : [ 0.0, 0.0, 0.0, 0.0 ],\n            \"m_isClosed\" : true,\n            \"m_vertex\" : [\n               {\n                  \"x\" : 592946.73912100005,\n                  \"y\" : 4198344.7768659201\n               },\n               {\n                  \"x\" : 592946.73912100005,\n                  \"y\" : 4198340.5142740784\n               },\n               {\n                  \"x\" : 593011.34312099998,\n                  \"y\" : 4198340.5142740784\n               },\n               {\n                  \"x\" : 593011.34312099998,\n                  \"y\" : 4198344.7768659201\n               }\n            ],\n            \"type\" : \"PolyLine2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 1,\n         \"m_layer\" : \"Array_沾化-56-1\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjComBinCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#206670001\"\n               }\n            ]\n         }\n      }\n   ],\n   \"m_objSelectType\" : 1,\n   \"m_paperId\" : \"DD20250730000001\",\n   \"m_stdString\" : \"\",\n   \"m_type\" : 2\n}\n"
    },
    {
        uuid:'112',
        meta:"{\n   \"m_objList\" : [\n      {\n         \"curve\" : {\n            \"curves\" : [\n               {\n                  \"endpoint\" : {\n                     \"x\" : 0.42426406871192424,\n                     \"y\" : 0.42426406871193229\n                  },\n                  \"startpoint\" : {\n                     \"x\" : -0.42426406871192668,\n                     \"y\" : 0.42426406871192668\n                  },\n                  \"type\" : \"LineSegment2d\"\n               },\n               {\n                  \"endpoint\" : {\n                     \"x\" : 0.42426406871193018,\n                     \"y\" : -0.42426406871193018\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 0.42426406871192429,\n                     \"y\" : 0.42426406871193229\n                  },\n                  \"type\" : \"LineSegment2d\"\n               },\n               {\n                  \"endpoint\" : {\n                     \"x\" : -0.42426406871193473,\n                     \"y\" : -0.42426406871192229\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 0.42426406871193018,\n                     \"y\" : -0.42426406871193018\n                  },\n                  \"type\" : \"LineSegment2d\"\n               },\n               {\n                  \"endpoint\" : {\n                     \"x\" : -0.42426406871194028,\n                     \"y\" : 0.42426406871192662\n                  },\n                  \"startpoint\" : {\n                     \"x\" : -0.42426406871193473,\n                     \"y\" : -0.42426406871192229\n                  },\n                  \"type\" : \"LineSegment2d\"\n               }\n            ],\n            \"m_bulge\" : [ 0.0, 0.0, 0.0, 0.0, 0.0 ],\n            \"m_isClosed\" : false,\n            \"m_vertex\" : [\n               {\n                  \"x\" : -0.42426406871192668,\n                  \"y\" : 0.42426406871192668\n               },\n               {\n                  \"x\" : 0.42426406871192429,\n                  \"y\" : 0.42426406871193229\n               },\n               {\n                  \"x\" : 0.42426406871193018,\n                  \"y\" : -0.42426406871193018\n               },\n               {\n                  \"x\" : -0.42426406871193473,\n                  \"y\" : -0.42426406871192229\n               },\n               {\n                  \"x\" : -0.42426406871194028,\n                  \"y\" : 0.42426406871192668\n               }\n            ],\n            \"type\" : \"PolyLine2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 0\n         },\n         \"m_id\" : 4,\n         \"m_layer\" : \"0\",\n         \"m_lineType\" : \"Continuous\",\n         \"m_lineTypeBL\" : 1,\n         \"m_lineWidth\" : -1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjComBinCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#137330004\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"curves\" : [\n               {\n                  \"endpoint\" : {\n                     \"x\" : 0.84852813742385869,\n                     \"y\" : -0.84852813742385869\n                  },\n                  \"startpoint\" : {\n                     \"x\" : -0.84852813742385536,\n                     \"y\" : 0.84852813742385536\n                  },\n                  \"type\" : \"LineSegment2d\"\n               }\n            ],\n            \"m_bulge\" : [ 0.0, 0.0 ],\n            \"m_isClosed\" : false,\n            \"m_vertex\" : [\n               {\n                  \"x\" : -0.84852813742385536,\n                  \"y\" : 0.84852813742385536\n               },\n               {\n                  \"x\" : 0.84852813742385869,\n                  \"y\" : -0.84852813742385869\n               }\n            ],\n            \"type\" : \"PolyLine2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 3,\n         \"m_layer\" : \"sgpile\",\n         \"m_lineType\" : \"BYLAYER\",\n         \"m_lineTypeBL\" : -1,\n         \"m_lineWidth\" : 30.0,\n         \"m_lineWidthBL\" : 30,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjComBinCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#137330003\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"endpoint\" : {\n               \"x\" : 0.84852813742385402,\n               \"y\" : 0.8485281374238598\n            },\n            \"startpoint\" : {\n               \"x\" : -0.84852813742386424,\n               \"y\" : -0.84852813742384958\n            },\n            \"type\" : \"LineSegment2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 2,\n         \"m_layer\" : \"sgpile\",\n         \"m_lineType\" : \"BYLAYER\",\n         \"m_lineTypeBL\" : -1,\n         \"m_lineWidth\" : 30.0,\n         \"m_lineWidthBL\" : 30,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#137330002\"\n               }\n            ]\n         }\n      },\n      {\n         \"curve\" : {\n            \"center\" : {\n               \"x\" : 1.7e-15,\n               \"y\" : -1.7e-15\n            },\n            \"radius\" : 1.1999999999999991,\n            \"type\" : \"Circle2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 255,\n            \"m_G\" : 255,\n            \"m_R\" : 255,\n            \"m_mode\" : 1\n         },\n         \"m_id\" : 1,\n         \"m_layer\" : \"sgpile\",\n         \"m_lineType\" : \"BYLAYER\",\n         \"m_lineTypeBL\" : -1,\n         \"m_lineWidth\" : 30.0,\n         \"m_lineWidthBL\" : 30,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : {\n            \"ACAD\" : [\n               {\n                  \"1000\" : \"m_ID#137330001\"\n               }\n            ]\n         }\n      }\n   ],\n   \"m_objSelectType\" : 1,\n   \"m_paperId\" : \"DD20250730000001\",\n   \"m_stdString\" : \"\",\n   \"m_type\" : 2\n}\n"
    }
]