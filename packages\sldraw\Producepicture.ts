import { DrawHelp } from "../dxfdraw/drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState,pickupInfo,SlScopeType,dxflSobj } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"
import { PubSub } from 'emitter';

export class Producepicture extends DrawHelp {//出图功能
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    public color:string='red'
    public draw:any=true


    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        const material = new LineBasicMaterial({ color:this.color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        PubSub.default.sub("slidentifyclear", this.clear.bind(this));
        this.init()
        
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        let p1=await this.lengthPick({
            tip:'指定第二个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p1)
        this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    public override onRButtonDown( event:PointerEvent ) : boolean{
       
        if(this.pnts.length<2) {
            this.pnts=[]
        }
               
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            let vertices=this.getvertices(secpt)
            this.drawTempGraphic(vertices)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)  
    }
    public drawTempGraphic(vertices){
        if(this.pnts.length<2) {
            this.mesh.geometry=new BufferGeometry().setFromPoints(vertices);
        }
    }
    
    complete(){
        let {x,y}=globalDxf.wcsOff
        dxflSobj.ids=this.pnts.map(item=>{
            return{
                x:item.x+x,
                y:item.y+y,
                z:item.z
            }
        })
        dxflSobj.isdxfPop=true
        poptip.dispose()
        this.dispose()

    }
    clear(){
        this.pnts=[]
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        this.viewer.Render()
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
    }

    private getvertices(v2:Vector3) {
        const pointA = this.pnts[0]
		const pointC = v2
		const pointB = new Vector3(pointC.x, pointA.y, pointA.z);
		const pointD = new Vector3(pointA.x, pointC.y, pointC.z);
		// 定义矩形的所有顶点
		const vertices = [pointA,pointB,pointC,pointD,pointA];
        return vertices
    }
}

