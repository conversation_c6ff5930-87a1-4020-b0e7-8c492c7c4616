<template>
  <button @click="btn">按钮1</button>
  <button @click="btn2">按钮2</button>
  <button @click="btn2_1">测试按钮展示</button>
  <button @click="btn3">图层</button>
  <button @click="btn4">搜图</button>
  <button @click="btn5">范围</button>
  <button @click="btn6">数据</button>
  <button @click="btn7">图纸对比结果开关</button>
  <button @click="btn8">属性选择图元</button>
  <button @click="btn8_2">属性选择范围</button>
  <button @click="btn8_1">属性批量识别结果</button>
  <button @click="btn9">菜单1</button>
  <button @click="btn10">AI</button>
  <button @click="btn11">识图继承</button>
  
  <button @click="btn12">图纸对比</button>
  <button @click="btn13">重新打开</button>
  <button @click="btn14">支架</button>
  <button @click="btn15">选择图元</button>
  <button @click="btn16">识别</button>
  <button @click="btn17">电缆</button>
  <button @click="btn18">镜头移动</button>
  <button @click="btn19">比例尺</button>
  <div style="width: 100vw;height: 100vh;display: flex;">
    <CadUI ref="cad" :fonts="fonts" @changescale='changescale' @cadgraphicelementAttribute="cadgraphicelementAttribute" @cadgraphicelementImg="cadgraphicelementImg"
      @cadRightClickMenuconfirm="rightClickMenuconfirm" @cadLeftClickMenuconfirm="cadLeftClickMenuconfirm" @cadsuisave="cadsuisave"></CadUI>
  </div>
</template>

<script setup>
import mainFont from "@/assets/fonts/Roboto-LightItalic.ttf"
import aux1Font from "@/assets/fonts/NotoSansDisplay-SemiCondensedLightItalic.ttf"
import aux2Font from "@/assets/fonts/HanaMinA.ttf"
import aux3Font from "@/assets/fonts/NanumGothic-Regular.ttf"


const cad = ref(null)
let drawingUrl = ref('')
const drawingId = ref('444444444444')
const fonts = ref([
  mainFont, aux1Font, aux2Font, aux3Font
])


  const btn = () => { 
  //sketchtest

  let drawingUrl='http://localhost:3000/沾化晴阳项目光伏场区桩型分区图.dxf' //demo8  沾化晴未渲染图层2  快绘制  line4 S70-04电缆及敷设图

  const obj = {
    drawingUrl,
    drawingId: '999',
    dxfBoxselection: [],
    plotBtnisshow: false,
    state: '',
    isZip:false,
    otherSelectedIdObj: {
      elements:[
        {
          materialComponentName:'预应力混凝土管桩',
          idList:[
            {
              hashCodeGraph:'1212',
              ids:['243380007', '243380017','243380013',243380014,243380010,243380015,243380016,243380012,243380011,243380009,243380008,243380006],
            },
          ]
        },
        // {
        //   materialComponentName:'围栏',
        //   idList:[
        //     { 
        //       ids:['A3'],
        //     },
        //   ]
        // }
      ],
      groups:[
        // {
        //   materialComponentName:"预应力混凝土管桩5",
        //   graphGroups:[
        //     {
        //       hashCodeGraph:'12698',
        //       idList:[
        //         {
        //           ids:[144660001, 144660004, 144660003, 144660002],
        //           middlePoint:{x: 550604.0175, y: 2704765.8443, z: 0},
        //         },
        //       ]
        //     },
        //   ]
        // }
      ]
    },
    layerObj:{
    }
  };
  cad.value.btnDisplaydrawings(obj);
};
const btn2 = () => {
  let drawingUrl='http://localhost:3000/demo82.dxf'
const obj = {
  drawingUrl,
  drawingId: '9999',
  dxfBoxselection: [],
  plotBtnisshow: false,
  state: '',
  isZip:false,
  otherSelectedIdObj: {
    elements:[
      {
        materialComponentName:'预应力混凝土管桩',
        idList:[
          {
            hashCodeGraph:'1212',
            ids:['98','103'],
          },
        ]
      },
      // {
      //   materialComponentName:'围栏',
      //   idList:[
      //     {
      //       ids:['A8'],
      //     },
      //   ]
      // }
    ],
    groups:[
      // {
      //   materialComponentName:"预应力混凝土管桩5",
      //   graphGroups:[
      //     {
      //       hashCodeGraph:'12698',
      //       idList:[
      //         {
      //           ids:[144660001, 144660004, 144660003, 144660002],
      //           middlePoint:{x: 550604.0175, y: 2704765.8443, z: 0},
      //         },
      //       ]
      //     },
      //   ]
      // }
    ]
  },
  layerObj:{
    // layerInfo: "[\n   {\n      \"curve\" : {\n         \"curves\" : [\n            {\n               \"endpoint\" : {\n                  \"x\" : 46.315916129129583,\n                  \"y\" : 61.277615555103843\n               },\n               \"startpoint\" : {\n                  \"x\" : -1.7720361858823566,\n                  \"y\" : 55.157332620891722\n               },\n               \"type\" : \"LineSegment2d\"\n            },\n            {\n               \"endpoint\" : {\n                  \"x\" : 49.06379911855884,\n                  \"y\" : 50.660796820007498\n               },\n               \"startpoint\" : {\n                  \"x\" : 46.315916129129583,\n                  \"y\" : 61.277615555103843\n               },\n               \"type\" : \"LineSegment2d\"\n            },\n            {\n               \"endpoint\" : {\n                  \"x\" : 68.174076272316839,\n                  \"y\" : 58.529734471554903\n               },\n               \"startpoint\" : {\n                  \"x\" : 49.06379911855884,\n                  \"y\" : 50.660796820007498\n               },\n               \"type\" : \"LineSegment2d\"\n            },\n            {\n               \"endpoint\" : {\n                  \"x\" : 72.795515845447852,\n                  \"y\" : 53.408677903556438\n               },\n               \"startpoint\" : {\n                  \"x\" : 68.174076272316839,\n                  \"y\" : 58.529734471554903\n               },\n               \"type\" : \"LineSegment2d\"\n            }\n         ],\n         \"m_bulge\" : null,\n         \"m_isClosed\" : false,\n         \"m_vertex\" : [\n            {\n               \"x\" : -1.7720361858823566,\n               \"y\" : 55.157332620891722\n            },\n            {\n               \"x\" : 46.315916129129583,\n               \"y\" : 61.277615555103843\n            },\n            {\n               \"x\" : 49.06379911855884,\n               \"y\" : 50.660796820007498\n            },\n            {\n               \"x\" : 68.174076272316839,\n               \"y\" : 58.529734471554903\n            },\n            {\n               \"x\" : 72.795515845447852,\n               \"y\" : 53.408677903556438\n            }\n         ],\n         \"type\" : \"PolyLine2d\"\n      },\n      \"m_color\" : {\n         \"m_A\" : 255,\n         \"m_B\" : 35,\n         \"m_G\" : 154,\n         \"m_R\" : 245,\n         \"m_mode\" : 2\n      },\n      \"m_id\" : 8,\n      \"m_layer\" : \"\",\n      \"m_lineType\" : \"ByLayer\",\n      \"m_lineTypeBL\" : -1,\n      \"m_lineWidth\" : 1.0,\n      \"m_lineWidthBL\" : -1,\n      \"m_linetypeScale\" : 1.0,\n      \"type\" : \"SGObjComBinCurve\",\n      \"xDataDouble\" : null,\n      \"xDataInt\" : null,\n      \"xDataString\" : null\n   }\n]\n"
  }
};
cad.value.btnDisplaydrawings(obj);
};
const btn2_1=()=>{
  cad.value.btnisshow()
}
const btn3 = () => {
  cad.value.pickupResultClear()
}
const btn4 = () => {
  // cad.value.identifyTheImages()
  let obj = cad.value.getCadData('identifyTheImages')
  console.log(obj, 'xxxxxxxxxxxxxxxxxxxxxx');
}
const btn5 = () => {
  cad.value.drawcommand('AI')
}

const cadsuisave = (obj) => {
  console.log(obj, '子组建');
}
const btn6 = () => {
  // cad.value.seeResultList()

}
const cadgraphicelementImg = (obj) => {
  console.log('子组件', obj);
}
const btn7 = () => {
  cad.value.drawingComparisonSwitch()
}
const btn8 = () => {
  cad.value.drawcommand('PeiDistinguish')
}
const btn8_2 = () => {
  cad.value.btnIdentificationScope('PolygonRange')
}
const btn8_1 = () => {
 let obj= cad.value.identifyAttribute({isSameLayer:true,isSameColor:true,isSameLineType:true})
 console.log(obj,'算量');
 
}

const rightClickMenuconfirm = (obj) => {
  console.log(obj, 'rightClickMenuconfirm');

}
const cadLeftClickMenuconfirm=(obj)=>{
  console.log(obj, 'cadLeftClickMenuconfirm');
}

  const changescale=(value)=>{
    console.log(value,'value');
    
  }
  const btn9=()=>{
    cad.value.switchMenu({
      identifyItem:'IDENTIFY',//LENGTH  SubsidiaryConstruction 
    })
  }
  const btn10=()=>{
    cad.value.AIdistinguish()
  }
  const btn11=async()=>{//识图继承
    let obj={
      url:'http://localhost:3000/demo14.dxf',
      elements : [
        {
          materialComponentCode: "ad456",
          materialComponentItemGroupCode: "f123",
          hashCode: "541",
          ids: [10,24],
        },
        {
          materialComponentCode: "ad4566",
          materialComponentItemGroupCode: "f1236",
          hashCode: "5412",
          ids: [23],
        }
      ],
      groups :[
        {
          materialComponentCode: "ere855",
          materialComponentItemGroupCode: "yuy222",
          groupCode : "1145",
          hashCode :"21012",
          ids: [100330003,100330004,100330001,100330002]//0: 100330003, 1: 100330004, 2: 100330001, 3: 100330002}
        },
        {
          materialComponentCode: "ere855",
          materialComponentItemGroupCode: "yuy222",
          groupCode : "1145",
          hashCode :"21012",
          ids: [100330003,100330004,100330001,9999999]//0: 100330003, 1: 100330004, 2: 100330001, 3: 100330002}
        },
        // {
        //   materialComponentCode: "ere8556",
        //   materialComponentItemGroupCode: "yuy2226",
        //   groupCode : "11456",
        //   hashCode :"210126",
        //   ids: [97,98]
        // }				
      ]
    }
  let objjjj=await cad.value.drawingInheritance(obj)

  console.log(objjjj,'objjjj');
  
  }
  //
  const btn12=()=>{//图纸对比
    let url='http://localhost:3000/S20_宜州山地光伏区总平面布置图.dxf'
    let obj={
      url,
      isZip:false
    }
  let objjjj= cad.value.drawingComparisonFn(obj)
  }
  const btn13=()=>{//重新打开
    let obj={
    dxfBoxselection: [],

    otherSelectedIdObj: {
      elements:[
        {
          materialComponentName:'预应力混凝土管桩',
          idList:[
            {
              hashCodeGraph:'1212',
              ids:['243380007', '243380017','243380013',243380014,243380010,243380015,243380016,243380012,243380011,243380009,243380008,243380006],
            },
          ]
        },
        // {
        //   materialComponentName:'围栏',
        //   idList:[
        //     {
        //       ids:['A128'],
        //     },
        //   ]
        // }
      ],
      groups:[
        // {
        //   materialComponentName:"预应力混凝土管桩5",
        //   graphGroups:[
        //     {
        //       hashCodeGraph:'12698',
        //       idList:[
        //         {
        //           ids:[144660001, 144660004, 144660003, 144660002],
        //           middlePoint:{x: 550604.0175, y: 2704765.8443, z: 0},
        //         },
        //       ]
        //     },
        //   ]

        // }
      ]
    },
    layerObj:{
      layerInfo: "{\n   \"addLayers\" : [\n      {\n         \"attributes\" : {\n            \"color\" : 0,\n            \"color24\" : -1,\n            \"handle\" : -1,\n            \"inPaperSpace\" : false,\n            \"layer\" : \"围栏\",\n            \"linetype\" : \"BYLAYER\",\n            \"linetypeScale\" : 1.0,\n            \"width\" : 0\n         },\n         \"data\" : {\n            \"m_flags\" : 0,\n            \"m_name\" : \"围栏\",\n            \"m_off\" : false\n         },\n         \"type\" : \"SGLayer\"\n      }\n   ],\n   \"objs\" : [\n      {\n         \"curve\" : {\n            \"curves\" : [\n               {\n                  \"endpoint\" : {\n                     \"x\" : 502877.45077169448,\n                     \"y\" : 3489337.8592012431\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 498153.36875450873,\n                     \"y\" : 3492213.3866020981\n                  },\n                  \"type\" : \"LineSegment2d\"\n               },\n               {\n                  \"endpoint\" : {\n                     \"x\" : 506420.51385162224,\n                     \"y\" : 3491135.0635329578\n                  },\n                  \"startpoint\" : {\n                     \"x\" : 502877.45077169448,\n                     \"y\" : 3489337.8592012431\n                  },\n                  \"type\" : \"LineSegment2d\"\n               }\n            ],\n            \"m_bulge\" : null,\n            \"m_isClosed\" : false,\n            \"m_vertex\" : [\n               {\n                  \"x\" : 498153.36875450873,\n                  \"y\" : 3492213.3866020981\n               },\n               {\n                  \"x\" : 502877.45077169448,\n                  \"y\" : 3489337.8592012431\n               },\n               {\n                  \"x\" : 506420.51385162224,\n                  \"y\" : 3491135.0635329578\n               }\n            ],\n            \"type\" : \"PolyLine2d\"\n         },\n         \"m_color\" : {\n            \"m_A\" : 255,\n            \"m_B\" : 35,\n            \"m_G\" : 154,\n            \"m_R\" : 245,\n            \"m_mode\" : 2\n         },\n         \"m_id\" : 320,\n         \"m_layer\" : \"围栏\",\n         \"m_lineType\" : \"ByLayer\",\n         \"m_lineTypeBL\" : -1,\n         \"m_lineWidth\" : 1.0,\n         \"m_lineWidthBL\" : -1,\n         \"m_linetypeScale\" : 1.0,\n         \"type\" : \"SGObjComBinCurve\",\n         \"xDataDouble\" : null,\n         \"xDataInt\" : null,\n         \"xDataString\" : null\n      }\n   ]\n}\n"

    }
    }
    cad.value.againOpenOperated(obj)
  }

  const btn14=()=>{
    let obj= {
      elements:[
        {
          hashCodeGraph:"HhGr20241107000092",
          materialComponentName:'庄',
          ids:[104]
        },
      ],
      groups:[
        {
          hashCodeGraph:"HhGr20241107000091",
          materialComponentName:'庄',
          ids:[98,103]
        },
      ]
    }
   let log= cad.value.generateSubComponent(obj)
   console.log(log,'log');
   
  }
  const btn15=()=>{
    cad.value.btnSelectGraphicElements()
  }
  const btn16=async ()=>{
    let obj=await cad.value.distinguish()
    console.log(obj,'obj');
    
  }

  const btn17=()=>{
      let arr=[
        {
          items : ["A8"],
          attributes: [
            {
              code : "SX003",
              name : "型号规格",
              value : "123"
            },
            {
              code : "SX004",
              name : "长度",
              value : "1"
            }
          ]
        },
      ]
      let obj={
        arr,
        type:'head',
        length:10,
      }
      let csres=cad.value.cableMidHead(obj)
      console.log(csres,'csres');
  }
  const btn18=()=>{
    let arr=['A96083']
    cad.value.cadidmovecamera(arr)
  }
  const btn19=()=>{
    cad.value.openControlPanelPop()
  }
  const cadgraphicelementAttribute=(obj)=>{
    console.log(obj,'obj');
    
  }




</script>

<style lang="scss" scoped>
button {
  border: none;
  outline: none;
  padding: 8px 30px;
  margin: 5px 10px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #aadd8e;
    transform: scale(1.05);
  }
}
</style>



