import { MTextEntityType, MTextState } from "./constant"

/** Single letter format codes which are not terminated by ";". */
const shortFormats = new Set([
    "L", "l", "O", "o", "K", "k", "P", "X", "~"
])

const longFormats = new Set([
    "f", "F", "p", "Q", "H", "W", "S", "A", "C", "T"
])

const validEscapes = new Set([
    "\\", "{", "}"
])

export class MTextFormatParser {
    public entities:any[]
    constructor() {
        this.entities = []
    }

    Parse(text) {
        const n = text.length
        let textStart = 0
        let state = MTextState.TEXT
        let scopeStack :any[] = []
        let curEntities = this.entities
        let curPos = 0
        const _this = this

        function EmitText() {
            if (state !== MTextState.TEXT || textStart === curPos) {
                return
            }
            curEntities.push({
                type: MTextEntityType.TEXT,
                content: text.slice(textStart, curPos)
            })
            textStart = curPos
        }

        function EmitEntity(type) {
            curEntities.push({type: type})
        }

        function PushScope() {
            const scope = {
                type: MTextEntityType.SCOPE,
                content: []
            }
            curEntities.push(scope)
            curEntities = scope.content
            scopeStack.push(scope)
        }

        function PopScope() {
            if (scopeStack.length === 0) {
                /* Stack underflow, just ignore now. */
                return
            }
            scopeStack.pop()
            if (scopeStack.length === 0) {
                curEntities = _this.entities
            } else {
                curEntities = scopeStack[scopeStack.length - 1].content
            }
        }

        for ( ;curPos < n; curPos++) {
            const c = text.charAt(curPos)

            switch (state) {

            case MTextState.TEXT:
                if (c === "{") {
                    EmitText()
                    PushScope()
                    textStart = curPos + 1
                    continue
                }
                if (c === "}") {
                    EmitText()
                    PopScope()
                    textStart = curPos + 1
                    continue
                }
                if (c === "\\") {
                    EmitText()
                    state = MTextState.ESCAPE
                    continue
                }
                continue

            case MTextState.ESCAPE:
                if (shortFormats.has(c)) {
                    switch (c) {
                    case "P":
                        EmitEntity(MTextEntityType.PARAGRAPH)
                        break
                    case "~":
                        EmitEntity(MTextEntityType.NON_BREAKING_SPACE)
                        break
                    }
                    state = MTextState.TEXT
                    textStart = curPos + 1
                    continue
                }
                if (longFormats.has(c)) {
                    switch (c) {
                    case "p":
                        state = MTextState.PARAGRAPH1
                        continue
                    }
                    state = MTextState.SKIP_FORMAT
                    continue
                }
                /* Include current character into a next text chunk. Backslash is also included if
                 * character is not among allowed ones (that is how Autodesk viewer behaves).
                 */
                if (validEscapes.has(c)) {
                    textStart = curPos
                } else {
                    textStart = curPos - 1
                }
                state = MTextState.TEXT
                continue

            case MTextState.PARAGRAPH1:
                state = c === "x" ? MTextState.PARAGRAPH2 : MTextState.SKIP_FORMAT
                continue

            case MTextState.PARAGRAPH2:
                state = c === "q" ? MTextState.PARAGRAPH3 : MTextState.SKIP_FORMAT
                continue

            case MTextState.PARAGRAPH3:
                curEntities.push({type: MTextEntityType.PARAGRAPH_ALIGNMENT, alignment: c})
                state = MTextState.SKIP_FORMAT
                continue

            case MTextState.SKIP_FORMAT:
                if (c === ";") {
                    textStart = curPos + 1
                    state = MTextState.TEXT
                }
                continue

            default:
                throw new Error("Unhandled state")
            }
        }

        EmitText()
    }

    /** @typedef MTextFormatEntity
     * @property type One of MTextEntityType
     *
     * @return {MTextFormatEntity[]} List of format chunks. Each chunk is either a text chunk with
     * TEXT type or some format entity. Entity with type SCOPE represents format scope which has
     * nested list of entities in "content" property.
     */
    GetContent() {
        return this.entities
    }

    /** Return only text chunks in a flattened sequence of strings. */
    *GetText() {

        function *TraverseItems(items) {
            for (const item of items) {
                if (item.type === MTextEntityType.TEXT) {
                    yield item.content
                } else if (item.type === MTextEntityType.SCOPE) {
                    yield *TraverseItems(item.content)
                }
            }
        }

        yield *TraverseItems(this.GetContent())
    }
}