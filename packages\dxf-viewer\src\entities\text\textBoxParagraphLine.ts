import { TextBoxParagraphAlign } from "./constant"

export class  TextBoxParagraphLine{
    public paragraph
    public startChunkIdx
    public numChunks
    public width
    constructor(paragraph, startChunkIdx, numChunks, width) {
        this.paragraph = paragraph
        this.startChunkIdx = startChunkIdx
        this.numChunks = numChunks
        this.width = width
    }

    ApplyAlignment(boxWidth, defaultAlignment) {
        let alignment = this.paragraph.alignment ?? defaultAlignment
        switch (alignment) {
        case TextBoxParagraphAlign.LEFT:
            break
        case TextBoxParagraphAlign.CENTER: {
            const offset = (boxWidth - this.width) / 2
            this.ForEachChunk(chunk => chunk.position += offset)
            break
        }
        case TextBoxParagraphAlign.RIGHT: {
            const offset = boxWidth - this.width
            this.ForEachChunk(chunk => chunk.position += offset)
            break
        }
        case TextBoxParagraphAlign.JUSTIFY: {
            const space = boxWidth - this.width
            if (space <= 0 || this.numChunks === 1) {
                break
            }
            const step = space / (this.numChunks - 1)
            let offset = 0
            this.ForEachChunk(chunk => {
                chunk.position += offset
                offset += step
            })
            break
        }
        default:
            throw new Error("Unhandled alignment: " + this.paragraph.alignment)
        }
    }

    ForEachChunk(handler) {
        for (let i = 0; i < this.numChunks; i++) {
            handler(this.paragraph.chunks[this.startChunkIdx + i])
        }
    }
}