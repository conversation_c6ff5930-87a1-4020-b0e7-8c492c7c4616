<template>
  <div class="layerManagePop">
    <el-dialog :model-value="isVisible"
      title="图层管理"
      width="800"
      :before-close="onCancel">   
        <div class="cadDlgBody">
            <div class="btnbox">
                <el-button class="btn" >新增图层</el-button>
                <el-button class="btn" >删除图层</el-button>
                <el-button class="btn">置为当前</el-button>
                <el-button class="btn"  @click="btnALLhide">
                  {{layers.every(item=>item.isVisible)?'关闭':'打开'}}所有图层
                </el-button>
            </div>
            <div>
                <el-table :data="layers"  style="width: 100%;" max-height="50vh"
                  :header-cell-style="{ background: '#454f61', color: '#fff' }"
                  :row-style="rowStyle"
                  @row-click="handleRowClick" >
                    <el-table-column prop="layerName" label="图层名称" width="220" >
                        <template #default="scope">
                            <div class="row">
                                <div style="width: 30px;" >
                                    <el-icon color="#16f822" v-if="scope.$index==dxflayers.curLayerIdx"><Select /></el-icon>
                                </div>
                                <!-- scope.row.layerName = $event.target.value -->
                                <input type="text" class="int"   @blur="onNameChange(scope.row,scope.$index)"
                                :disabled="scope.row.name==='0'"
                                v-model="scope.row.name">
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="name" label="可见" width="80" >
                        <template #default="scope">
                            <el-icon class="ico zpointer" size="16"  color="#c27c16" @click.stop="setlayerVisible()" >
                              <View v-if="scope.row.isVisible" />
                              <Hide v-else />
                            </el-icon>
                        </template>
                    </el-table-column>

                    <el-table-column prop="name" label="冻结" width="80" >
                        <template #default="scope">
                            <el-icon class="ico zpointer"  size="16" color="#c27c16" >
                              <Loading v-if="scope.row.isFreeze"/>
                              <Sunny v-else/>
                            </el-icon>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="锁定" width="80" >
                        <template #default="scope">
                            <el-icon class="ico zpointer"  size="16" color="#c27c16">
                              <Lock v-if="scope.row.isLocking"/>
                              <Unlock v-else/>
                            </el-icon>
                        </template>
                    </el-table-column>
                    <el-table-column prop="layerColor" label="颜色" width="150" >
                        <template #default="scope">
                          <div class="colorbtn" :style="getLayerColorStyle(scope.$index)" @click.stop="showColorPicker(scope.$index)"></div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="linear" label="线性"  >
                    </el-table-column>
                </el-table>
            </div>
        </div>
        
        <template #footer>
          <div class="dialog-footer">
            <div class="bottomBtn">
                <el-button style="background-color: #2e3440;color: #fff;" class="btn" >反选</el-button>
                <span>
                    <el-button  type="primary"  class="btn" @click="onConfirm">确认</el-button>
                    <el-button class="btn" style="background-color: #2e3440;color: #fff;" @click="btnCancellaye">关闭</el-button>
                </span>
            </div>
          </div>
        </template>
    </el-dialog>
    <Sgcolorpicker ref="layercolorpicker" :color="modify.color" @colorPicked="onColorPicked"></Sgcolorpicker>
    
  </div>
</template>

<script setup>

  import { View ,Sunny,Unlock,Lock,Loading,Hide,Select} from '@element-plus/icons-vue'
  import {dxflayers,curLayer,getLayerColor,setlayerAllVisible,
    setlayerVisible,setlayerFreeze,setlayerLock
  } from 'dxf-viewer'
  import {PubSub} from 'emitter'
  import { ElLoading ,ElMessage } from "element-plus";

  const layers=dxflayers.layers
  const isVisible=ref(false)
  const modify=reactive({
    idx:-1,
    color:'rgb(0,0,255)'
  })
  const layercolorpicker=ref()
  const allIsVisible=ref(true)


  const emits = defineEmits(['cancellayePop'])

  const props = defineProps({
    layerManagePopVisible:{
        type: Boolean,
        default:false
    }
  })
  const activeRowIndex=ref([])

  const kllayerList=ref([]) //临时图层数组
  const klcurrentLayer=ref({})  //临时当前图层

  const handleRowClick=(row, event, column)=>{
    activeRowIndex.value=[row]
  }
  const  rowStyle=({ row, rowIndex }) =>{
    if (activeRowIndex.value.includes(row)) {
      return  { backgroundColor: '#2c7bb6' }; // 返回被点击行的样式
    }
    return {}; // 返回其他行的默认样式
  }



  const onNameChange=(item,idx)=>{
    if(layers.map(item=>item.displayName).includes(item.name)) {
      layers[idx].name=layers[idx].displayName
      ElMessage({ message: '该名称已存在', type: 'warning',})
      return
    }
    let newname=item.name || '0'
    let usedName=item.displayName
    PubSub.default.pub('layernamechanged',{newname,usedName})
  }

  const getLayerColorStyle=computed(()=>(idx)=>{
      let color=getLayerColor(idx)
      return {"background-color":color}
  }) 
  const onColorPicked=(color)=>{//'颜色选择器返回颜色'
    PubSub.default.pub('layercolorchanged',{idx:modify.idx,color:color.color})
  }
  const showColorPicker=(idx)=>{
    modify.idx=idx?idx:dxflayers.curLayerIdx
    modify.color=getLayerColor(idx)
    layercolorpicker.value.show()
  }
  const btnALLhide=()=>{
    allIsVisible.value=!allIsVisible.value
    layers.forEach(item=>{
        item.isVisible=allIsVisible.value
    })
    setlayerAllVisible(allIsVisible.value)
  }

  
  //取消
  const onCancel=()=>{
    isVisible.value=false
  }
  //确定
  const onConfirm = () => {

}
const show=()=>{
    isVisible.value=true
}
  defineExpose({
    show
  })
</script>

<style lang="scss" scoped>
.layerManagePop {
    .btnbox {
        margin: 20px 0 10px;
        .btn {
            background-color: #2e3440;
            color: #fff;
            border: none;
            height: 40px;
        }
    }
    .ico {
      &:hover {
          background-color: #fff;
      }
    }
    .bottomBtn {
        display: flex;
        justify-content: space-between;
        .btn {
            width: 100px;
            border: none;
            height: 40px;
        }
    }
    .int {
      background-color: #343b48;
      color: #fff;
      border: none;
    }

    :deep(.el-table__body) {
      tr:hover > td {
        background-color: #5d626d ;
        opacity:0.333;
      }
      tr{
        background-color: #343b48;
        color: #fff;
      }
    }
    :deep(.el-table) {
      .el-table__cell {
        border: none;
      }
    }

    :deep(.el-dialog) {
    background-color: #3a4352;
    }
    :deep(.el-dialog__body) {
        padding:0 !important;
    }
    :deep(.el-dialog__headerbtn) {
        width: 16px !important;
        height: 26px !important;
        top:1px;
        
    }
    :deep(.el-dialog__header) {
        padding:0 !important;
        padding-bottom:0;
        
        .el-dialog__title {
        font-size:16px;
        color: #fff !important;
        }
    }
    .colorbtn{
      width:15px;
      height: 15px;
      border: 1px solid gray;
    }
}
</style>

  