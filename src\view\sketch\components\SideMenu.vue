<template>
    <div class="SideMenu">
        <div class="ButtonStyle">
            <el-icon class="ico" :size="20">
                <Grid  />
            </el-icon>
            <div>图层设置</div>
        </div>
        <div class="ButtonStyle">
            <el-icon class="ico" :size="20">
                <Grid  />
            </el-icon>
            <div>图层管理</div>
        </div>
        <div class="ButtonStyle">
            <el-icon class="ico" :size="20">
                <Grid  />
            </el-icon>
            <div>图层管理</div>
        </div>
        <div class="ButtonStyle">
            <el-icon class="ico" :size="20">
                <Grid  />
            </el-icon>
            <div>图层管理</div>
        </div>
        <div class="ButtonStyle">
            <el-icon class="ico" :size="20">
                <Grid  />
            </el-icon>
            <div>图层管理</div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref,onMounted,nextTick,reactive } from 'vue'
import { ElLoading ,ElMessage } from "element-plus";
import { Position ,ScaleToOriginal,Top,Delete,FullScreen,Connection,
    Share,TopLeft,SetUp,DocumentCopy,Grid,Notebook,
} from '@element-plus/icons-vue'


const emits = defineEmits()
const props = defineProps({

})
onMounted(()=>{

})

defineExpose({

})
</script>

<style lang="scss" scoped>
.SideMenu {
    display: flex;
    flex-direction: column;
    position: absolute;
    // background-color: red;
    color: #fff;
    z-index: 1;
    left: 10px;
    top: 60px;
    .ButtonStyle {
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }

}
</style>

