import { ElMessage } from "element-plus";

import { apiTask, apiTaskDetail } from "../api/buildMapBoxApi.js";
//@ts-ignore
import { apiQueryUploadFileUrl } from "@/view/mapBox/api/buildingBlockApi.js";
import { uploadfileFn } from "../api/buildMapBoxApi.js";
import JSZip, { JSZipGeneratorOptions } from "jszip";
import * as fflate from "fflate";
import axios from "axios";
import md5 from "js-md5";

// 添加类型声明
interface TaskResult {
  code: number;
  message: string;
  data: {
    id: string;
    status: string;
    resultDownloadUrl?: string;
  };
}

interface OssData {
  storageFileId: string;
  [key: string]: any;
}

interface FileHandleParams {
  file: File;
  ossData: OssData;
  newFile: File;
}

// 定义转换结果接口
export interface ConversionResult {
  success: boolean;
  message: string;
  file?: File;
  originalFile?: File;
  storageFileId?: string;
}

// 定义进度回调函数类型
export type ProgressCallback = (progress: number) => void;

export class LoadDxf {
  /**
   * 上传并转换文件
   * @param file 要上传和转换的文件
   * @param progressCallback 进度回调函数
   * @returns 转换结果
   */
  public async uploadDxfOrConvertDwgToDxf(
    file: File,
    progressCallback?: ProgressCallback
  ): Promise<ConversionResult> {
    if (!file) {
      return {
        success: false,
        message: "未提供文件",
      };
    }

    const fileName = file.name || "";
    const extension =
      fileName.lastIndexOf(".") !== -1
        ? fileName.slice(fileName.lastIndexOf(".") + 1).toLowerCase()
        : "";

    if (extension !== "dwg" && extension !== "dxf") {
      return {
        success: false,
        message: "只支持DWG和DXF格式文件",
      };
    }

    try {
      // 如果文件是DXF格式，直接上传到OSS
      if (extension === "dxf") {
        if (progressCallback) progressCallback(10);

        const ossData = await this.uploadFileToOSS([file], (progress) => {
          // DXF上传进度：10-90%
          if (progressCallback)
            progressCallback(10 + Math.floor(progress * 0.8));
        });

        console.log("DXF文件上传到OSS服务器", ossData);

        if (!ossData || !ossData.storageFileId) {
          return {
            success: false,
            message: "文件上传到OSS服务器失败",
          };
        }

        if (progressCallback) progressCallback(100);
        return {
          success: true,
          message: "DXF文件上传成功",
          file: file,
          storageFileId: ossData.storageFileId,
        };
      }
      // 如果是DWG文件，需要转换处理
      else {
        if (progressCallback) progressCallback(5);

        // 压缩文件
        const newFile = await this.compressFile(file);
        if (!newFile) {
          return {
            success: false,
            message: "文件压缩失败",
          };
        }

        if (progressCallback) progressCallback(15);

        // 上传到OSS服务器
        const ossData = await uploadfileFn([newFile]);
        if (!ossData || !ossData.storageFileId) {
          return {
            success: false,
            message: "文件上传到服务器失败",
          };
        }

        if (progressCallback) progressCallback(35);

        // 处理DWG文件转换
        const result = await this.processDwgConversion(
          {
            file,
            ossData,
            newFile,
          },
          progressCallback
        );

        // 如果转换成功且有文件，则上传转换后的DXF文件到OSS
        if (result.success && result.file) {
          if (progressCallback) progressCallback(80);

          const dxfOssData = await this.uploadFileToOSS(
            [result.file],
            (progress) => {
              // 转换后DXF上传进度：80-95%
              if (progressCallback)
                progressCallback(80 + Math.floor(progress * 0.15));
            }
          );

          console.log("转换后的DXF文件上传到OSS服务器", dxfOssData);
          if (!dxfOssData || !dxfOssData.storageFileId) {
            return {
              success: false,
              message: "转换后的DXF文件上传到OSS服务器失败",
            };
          }

          if (progressCallback) progressCallback(100);

          // 返回成功结果，包含storageFileId
          return {
            success: true,
            message: "DWG文件转换并上传成功",
            file: result.file,
            originalFile: file,
            storageFileId: dxfOssData.storageFileId,
          };
        }

        return result;
      }
    } catch (error) {
      console.error("文件转换过程出错:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "文件转换失败",
      };
    }
  }

  /**
   * 上传文件到OSS
   * @param files 要上传的文件数组（通常只使用第一个文件）
   * @param progressCallback 进度回调函数
   * @returns Promise 返回包含storageFileId的对象
   */
  public async uploadFileToOSS(
    files: File[],
    progressCallback?: ProgressCallback
  ): Promise<{ storageFileId: string }> {
    return new Promise((resolve, reject) => {
      if (!files || files.length === 0) {
        reject(new Error("没有选择文件"));
        return;
      }

      let params = {
        name: files[0].name,
        size: files[0].size,
        md5: "",
      };

      // 上传进度计算所需的变量
      const totalSteps = 3; // 1.计算MD5 2.获取上传URL 3.上传文件
      let currentStep = 0;

      if (progressCallback) progressCallback(0);

      const reader = new FileReader();
      reader.onloadend = function (e: any) {
        // 从事件对象中获取已读取的文件内容（转换为字节数组）
        const content = e.target.result;
        // 使用js-md5计算文件内容的MD5散列值
        // @ts-ignore
        params.md5 = md5(content);

        // MD5计算完成，更新进度
        currentStep++;
        if (progressCallback)
          progressCallback(Math.floor((currentStep / totalSteps) * 100));

        // 使用封装的API函数获取上传URL
        apiQueryUploadFileUrl(params)
          .then((res: any) => {
            // 获取上传URL完成，更新进度
            currentStep++;
            if (progressCallback)
              progressCallback(Math.floor((currentStep / totalSteps) * 100));

            if (res && res.code === 200) {
              if (res.data.url) {
                // 验证URL格式是否有效
                try {
                  new URL(res.data.url);
                } catch (error) {
                  reject(new Error(`无效的上传URL: ${res.data.url}`));
                  return;
                }

                var customAxios = axios.create();
                customAxios.interceptors.request.use((config) => {
                  // 删除Content-Type，仅针对PUT请求
                  if (config.method === "put") {
                    config.headers["Content-Type"] = "";
                  }
                  return config;
                });

                customAxios
                  .put(res.data.url, files[0], {
                    // 添加上传进度监听
                    onUploadProgress: (progressEvent) => {
                      if (progressEvent.total) {
                        const percentCompleted = Math.round(
                          (progressEvent.loaded * 100) / progressEvent.total
                        );
                        // 文件上传进度
                        if (progressCallback) {
                          // 2/3 + 最后1/3的进度
                          const adjustedProgress = Math.floor(
                            ((2 + percentCompleted / 100) / totalSteps) * 100
                          );
                          progressCallback(Math.min(adjustedProgress, 99));
                        }
                      }
                    },
                  })
                  .then(() => {
                    // 上传完成，更新进度为100%
                    if (progressCallback) progressCallback(100);

                    let fileReader = new FileReader();
                    fileReader.readAsDataURL(files[0]);
                    fileReader.onload = () => {
                      resolve({ storageFileId: res.data.storageFileId });
                    };
                  })
                  .catch((error) => {
                    reject(error);
                  });
              } else {
                // 如果没有URL，说明文件已存在，直接获取storageFileId
                if (progressCallback) progressCallback(100);
                resolve({ storageFileId: res.data.storageFileId });
              }
            } else {
              reject(new Error(`上传文件失败: ${res?.message || "未知错误"}`));
            }
          })
          .catch((error) => {
            reject(error);
          });
      };

      // 开始读取文件内容
      reader.readAsBinaryString(files[0]);
    });
  }

  private async compressFile(file: File): Promise<File | undefined> {
    try {
      const zip = new JSZip();
      zip.file(file.name, file);
      const ZP: JSZipGeneratorOptions = {
        type: "blob",
        compression: "DEFLATE",
        compressionOptions: { level: 9 },
      };
      const content = await zip.generateAsync(ZP);
      const tempFile = new File([content as Blob], "temp.zip", {
        type: "application/zip",
      });
      return tempFile;
    } catch (error) {
      console.log(error);
      return undefined;
    }
  }

  public async decompression(file: File): Promise<File | undefined> {
    try {
      const zipData = await file.arrayBuffer();
      const unzipped = fflate.unzipSync(new Uint8Array(zipData));

      for (const [fileName, fileData] of Object.entries(unzipped)) {
        const dataArray = fileData as Uint8Array;
        const blob = new Blob([dataArray], {
          type: "application/octet-stream",
        });

        const unzippedFile = new File([blob], fileName, {
          type: "application/octet-stream",
        });

        console.log(`解压成功: ${fileName}`, unzippedFile);
        return unzippedFile;
      }

      return undefined;
    } catch (error) {
      console.log("解压失败" + error);
      return undefined;
    }
  }

  /**
   * 监听是否解析完成
   */
  private async analysisCompleted(
    id: string,
    progressCallback?: ProgressCallback
  ): Promise<string> {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const maxWaitTime = 120000; // 最长等待时间：2分钟
      const interval = 1000; // 轮询间隔：1秒

      let lastProgress = 35; // 转换开始进度
      const maxProgress = 75; // 转换结束进度

      const monitor = setInterval(() => {
        apiTaskDetail(id).then((detailRes: any) => {
          if (detailRes.code == 200) {
            if (detailRes.data.status.toUpperCase() === "FAILED") {
              clearInterval(monitor);
              ElMessage({ message: "解析失败,DWG文件被加密！", type: "error" });
              resolve(""); // 返回 url
            }
            if (detailRes.data.status.toUpperCase() === "SUCCESS") {
              clearInterval(monitor);
              if (progressCallback) progressCallback(maxProgress);
              resolve(detailRes.data.resultDownloadUrl || ""); // 返回 url
            }
          }

          // 计算经过的时间比例，更新进度
          const elapsedTime = Date.now() - startTime;
          if (progressCallback && elapsedTime < maxWaitTime) {
            const progressIncrement =
              (elapsedTime / maxWaitTime) * (maxProgress - lastProgress);
            const newProgress = Math.min(
              Math.floor(lastProgress + progressIncrement),
              maxProgress - 1
            );
            if (newProgress > lastProgress) {
              lastProgress = newProgress;
              progressCallback(lastProgress);
            }
          }
        });
      }, interval);
    });
  }

  // 把url转成file对象
  private async urlToDxfFile(url: string): Promise<File | undefined> {
    if (!url) return undefined;

    try {
      const response = await fetch(url);
      const blob = await response.blob();
      return new File([blob], "converted.zip", {
        type: "application/zip",
      });
    } catch (error) {
      console.error("获取转换后文件失败:", error);
      return undefined;
    }
  }

  /**
   * 处理DWG转换为DXF的完整流程
   */
  private async processDwgConversion(
    params: FileHandleParams,
    progressCallback?: ProgressCallback
  ): Promise<ConversionResult> {
    const { file, ossData, newFile } = params;

    try {
      // 调用转换API
      const res: any = await apiTask({
        name: file.name || "tempFile",
        sourceData: ossData.storageFileId,
        taskType: "dwgzip2dxfzip",
      });

      if (res.code !== 200) {
        return {
          success: false,
          message: res.message || "API调用失败",
        };
      }

      let sourceId = res.data.id;

      // 显示处理中状态
      console.log("DWG文件正在处理中，请稍等...");

      // 获取转换后的文件URL - 并在转换过程中更新进度
      const fileUrl = await this.analysisCompleted(sourceId, progressCallback);

      if (!fileUrl) {
        return {
          success: false,
          message: "转换失败，未获得转换后的文件URL",
        };
      }

      // 下载转换后的zip文件
      const convertedZip = await this.urlToDxfFile(fileUrl);
      if (!convertedZip) {
        return {
          success: false,
          message: "无法下载转换后的文件",
        };
      }

      if (progressCallback) progressCallback(77);

      // 解压缩转换后的文件
      const dxfFile = await this.decompression(convertedZip);

      if (!dxfFile) {
        return {
          success: false,
          message: "解压转换后的文件失败",
        };
      }

      if (progressCallback) progressCallback(79);

      // 创建一个新的DXF文件，保持原来的文件名但改为.dxf后缀
      const originalName = file.name;
      const newName =
        originalName.substring(0, originalName.lastIndexOf(".")) + ".dxf";
      const finalDxfFile = new File([dxfFile], newName, { type: dxfFile.type });

      return {
        success: true,
        message: "文件转换成功",
        file: finalDxfFile,
        originalFile: file,
      };
    } catch (error) {
      console.error("DWG转换处理失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "DWG转换处理失败",
      };
    }
  }
}
