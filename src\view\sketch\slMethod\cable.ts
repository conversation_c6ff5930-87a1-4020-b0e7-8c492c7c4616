import { pickupInfo,LegendType,dxfZsObj} from 'dxf-viewer'
import {coordinateMovecamera} from 'dxf-viewer/src/libs'
import { cloneDeep } from 'lodash';
import {wasmBoundingBox} from './utils'


let dxfview:any=null
let lsLvCIdentifyAlgor=null
export class Cable {
  public buriedAmplify:any=0 
  constructor(obj){
      dxfview=obj
  }

  public async cableCalculation(data,obj2){
    if(obj2.steptype==1) {//第一步识别电缆桥架
      return this.theFirstStep(data,obj2) || []
    }else if(obj2.steptype==2) {//第二步识别电缆段
      return this.theSecondStep(data) || []
    }else if(obj2.steptype=='Highlight') {//高亮电缆段
      this.cableHighlight(data)
    }else if(obj2.steptype==3) {//光伏电缆计算
      return this.photovoltaicCable(data,obj2) || []
    }
  }
  //第一步识别电缆桥架
  public async theFirstStep(data,obj2){
    this.buriedAmplify=0
    let gp=dxfview.GetViewer().transaction.gpObj
    let cableBridge=new sg.CableBridge()
    let sheathVlist=new sg.vector_Sheath()
    if(data.laying) {
        if(data.laying.bridge) {
            const bridge=data.laying.bridge

            bridge.forEach(async item=>{
                let wasmResObj=await obj2.identifyImgObj.legendpickupIds(item.legend,LegendType.LinetypeLegend)
                cableBridge.addBridge(gp,wasmResObj,item.relativeHeight,item.width,item.height)
            })
        }
        if(data.laying.buried) {
          // console.log('11111111111111');
            const buried=data.laying.buried
            buried.forEach(async (item,index)=>{
              index==0 && (this.buriedAmplify=Number(item.terrainCoefficient) )
                let wasmResObj=await obj2.identifyImgObj.legendpickupIds(item.legend,LegendType.LinetypeLegend)
                // console.log(item.relativeDepth,'item.relativeHeight');
                
                cableBridge.addBridge(gp,wasmResObj,-item.relativeDepth,0,0)
            })
        }
        if(data.laying.bushing) {
          // console.log('22222222222222');
          let bushing=data.laying.bushing
          bushing.forEach(async (item)=>{
              let wasmResObj=await obj2.identifyImgObj.legendpickupIds(item.legend,LegendType.LinetypeLegend)
              for(let i=0;i<wasmResObj.size();i++){
                let id=wasmResObj.get(i)
                let dlobj=gp.getBaseObjById(id)
                let sheath=new sg.Sheath(id,-item.relativeDepth,dlobj)
                sheathVlist.push_back(sheath)
              }
          })
        }
    }

    let finalResult:any=[]
    if(data.setLegend) {
        if(data.setLegend.combinerBox) {
            let combinerBox=data.setLegend.combinerBox
            let resObj1=await obj2.identifyImgObj.legendpickupIds(combinerBox.startLegend,LegendType.GraphicLegend)
            let resObj2=await obj2.identifyImgObj.legendpickupIds(combinerBox.endLegend,LegendType.GraphicLegend)
            if(!resObj1 || !resObj2) return []

            console.log(resObj2,'resObj2combinerBox');
            
            //汇流箱 箱逆变
            let startRelativeHeight=combinerBox.startRelativeHeight
            let endRelativeHeight=combinerBox.endRelativeHeight
            let obj={
              startRelativeHeight,
              endRelativeHeight,
              cableBridge,
              sheathVlist,
              deviceName:'汇流箱',
              boxName:'箱逆变',
            }
            let res=this.resLVCIdentifyAlgor(resObj1,resObj2,obj)
            finalResult.push(...res)

            console.log(res,'groupData');
        }
        if(data.setLegend.inverter) {
          // console.log('3333333333');
          
            let inverter=data.setLegend.inverter
            let resObj1=await obj2.identifyImgObj.legendpickupIds(inverter.startLegend,LegendType.GraphicLegend)
            let resObj2=await obj2.identifyImgObj.legendpickupIds(inverter.endLegend,LegendType.GraphicLegend)
            // console.log(resObj2,'resObj2resObj2resObj2');
            if(!resObj1 || !resObj2) return []
            let startRelativeHeight=inverter.startRelativeHeight
            let endRelativeHeight=inverter.endRelativeHeight
            let obj={
              startRelativeHeight,
              endRelativeHeight,
              cableBridge,
              sheathVlist,
              deviceName:'逆变器',
              boxName:'箱变',
            }

            let res=this.resLVCIdentifyAlgor(resObj1,resObj2,obj)
            // console.log(res,'groupData');
            finalResult.push(...res)
            
        }


    }
    return finalResult
    // return this.theSecondStep(finalResult)
  }
  public resLVCIdentifyAlgor(resObj1,resObj2,obj){
    let gp=dxfview.GetViewer().transaction.gpObj
    let startDevice=resObj1.result
    let endDevice=resObj2.result
    let startDeviceVlist=new sg.vector_InverterBox()
    let containerTransVlist=new sg.vector_ContainerTrans()

    for (let i=0;i<resObj1.count;i++){
      let inverterBox=new sg.InverterBox(startDevice,i,Number(obj.startRelativeHeight))
      startDeviceVlist.push_back(inverterBox)
    }
    for (let i=0;i<resObj2.count;i++){
      let containerTrans=new sg.ContainerTrans(endDevice,i,Number(obj.endRelativeHeight))
      containerTransVlist.push_back(containerTrans)
    }
    let lvCIdentifyAlgor=new sg.LVCIdentifyAlgor(gp,obj.cableBridge,containerTransVlist,startDeviceVlist)
    console.log(lvCIdentifyAlgor.serializeToString(),'lvCIdentifyAlgor.serializeToString()');

    lvCIdentifyAlgor.setSheath(obj.sheathVlist)
    console.log(11111);
    
    lvCIdentifyAlgor.bridgeCheck()//桥架检查
    console.log(22222);
    
    lsLvCIdentifyAlgor=lvCIdentifyAlgor
    // let top= lvCIdentifyAlgor.getPathById(6, 1); //测试写死的桥架
    let topolist=lvCIdentifyAlgor.calLvcTopo()
    console.log(33333);
    
    let arr:any=[]
    for(let i=0;i<topolist.size();i++){
      let topo=topolist.get(i)
      let InvName=topo.getInvName()
      let CtName=topo.getCtName()
      let Invld=topo.getInvId()
      let CtId=topo.getCtId()
      let InvBox=lvCIdentifyAlgor.getInvBoxById(Invld)
      let wasmInvlds=InvBox.getObjIds()
      let InvIds=this.getwasnIDs(wasmInvlds)
      let ContainerTransBox=lvCIdentifyAlgor.getContainerById(CtId)
      let wasmCtids=ContainerTransBox.getObjIds()
      let Ctids=this.getwasnIDs(wasmCtids)
      let box1=sg.getElementObjRect( dxfZsObj.drawingId ,new sg.ElementObj(wasmInvlds))//逆变器箱变包围盒

      let box2=sg.getElementObjRect( dxfZsObj.drawingId ,new sg.ElementObj(wasmCtids))
      
      let InverterBox=wasmBoundingBox(box1)  
      let ContainerBox=wasmBoundingBox(box2)

      arr.push({InvName,CtName,CtId,Invld,InvIds,Ctids,deviceName:obj.deviceName,InverterBox,ContainerBox,})//InverterBox,ContainerBox
      // console.log(arr,'arr');
      
    }
    let groupData=this.groupByCtId(arr,obj.boxName).map(item=>{
      if(!item.children){
        item.color='red'
      }else {
        !item.children.length && (item.color='red')
      }
      return item
    })

    return groupData
  }
  public getwasnIDs=(wasmids)=>{
    let size=wasmids.size()
    let arr:any=[]
    for(let i=0;i<size;i++){
      arr.push(wasmids.get(i))
    }
    return arr
  }

  public groupByCtId(data,deviceName) {
    const groupedMap = new Map();
    const specialItems:any = [];
    // 遍历数据并按 CtId 分组
    data.forEach(item => {
      
      if (item.CtId === -999) {
        // CtId = -999 的直接放入 specialItems
        let obj={
          ...item,
          ids:item.InvIds,
          startPoint:item.InverterBox.leftTopPoint,
          endPoint:item.InverterBox.rightBottomPoint
        }
        delete obj.InverterBox
        delete obj.ContainerBox
        // delete obj.CtId
        // delete obj.Invld
        delete obj.Ctids
        delete obj.InvIds
        specialItems.push(obj);
      } else {
        // 其他 CtId 按照原来的逻辑分组
        if (!groupedMap.has(item.CtId)) {
          groupedMap.set(item.CtId, {
            CtName: item.CtName,
            CtId: item.CtId,
            ids: item.Ctids,
            deviceName,
            isTerminal:true,
            children: [],
            startPoint:item.ContainerBox.leftTopPoint,
            endPoint:item.ContainerBox.rightBottomPoint
          });
        }
        let obj={
          ...item,
          ids:item.InvIds,
          startPoint:item.InverterBox.leftTopPoint,
          endPoint:item.InverterBox.rightBottomPoint
        }
        delete obj.InverterBox
        delete obj.ContainerBox
        // delete obj.CtId
        // delete obj.Invld
        delete obj.Ctids
        delete obj.InvIds

        groupedMap.get(item.CtId).children.push(obj);
      }
    });
  
    // 转换为数组形式，并将 specialItems 合并到结果中
    return [...Array.from(groupedMap.values()), ...specialItems];
  }

  //第二步
  public theSecondStep(organizedResults){
    let newOrganizedResults =cloneDeep(organizedResults)
    // let top= this.lvCIdentifyAlgor.getPathById(25, 10); //测试写死的桥架
    newOrganizedResults.forEach(item=>{
      item.children?.forEach(ite=>{
        let top= lsLvCIdentifyAlgor.getPathById(ite.Invld, item.CtId);
        let bridgelist=top.getCS()
        let newCableBridge=lsLvCIdentifyAlgor.getCB()
        let CableSegment=newCableBridge.getSegments(bridgelist)
        let size=CableSegment.size()
        let bridgeArr:any=[]
        size && bridgeArr.push({deviceCode:ite.InvName,deviceName:ite.deviceName})
        for(let i=0;i<size;i++){
          let segment=CableSegment.get(i)
          let id=segment.getId()
          let width=segment.getWidth()
          let height=segment.getHeight()
          let startPoint={x:segment.startPoint().x(),y:segment.startPoint().y()}
          let endPoint={x:segment.endPoint().x(),y:segment.endPoint().y()}
          let bridgeName='' //
          let length3d=segment.length3d()*dxfZsObj.drawingScale
          // console.log(length3d,'length3d')
          let startH=segment.startH()

          let endH=segment.endH()
          let constructType=segment.getCSType() 
          let cableLength:any=0
          // console.log(constructType,'constructType');
          
          if(constructType==1 ) { // 1：表示桥架，2表示：套管，3表示：直埋
            cableLength=length3d*(1+0.025)*dxfZsObj.drawingScale
            bridgeName= segment.isVertical()?'垂直桥架':'水平桥架' 
            // console.log(segment.isVertical(),'segment.isVertical()');
          }
          else if(constructType==2){
            cableLength=length3d*(1+0.025)*dxfZsObj.drawingScale
            bridgeName='直埋套管'
          }
          else {
            // cableLength=length3d*(1+0.10)*dxfZsObj.drawingScale
            cableLength=length3d*(1+this.buriedAmplify)*dxfZsObj.drawingScale
            bridgeName='电缆管沟'
          }
          
          bridgeArr.push({id,width,height,startPoint,endPoint,bridgeName,length3d,startH,endH,constructType,cableLength})
        }
        size && bridgeArr.push({deviceCode:item.CtName,deviceName:item.deviceName})
        !bridgeArr.length && console.log(ite,'没有桥架');
        ite.bridgeArr=bridgeArr
      })
    })
    return newOrganizedResults
  }



  public async cableHighlight(obj){
    let viewer=dxfview.GetViewer()
    let newobj={
      // endPoint: {x: 380081.1942480148, y: 3447368.182024619},
      // startPoint: {x: 380073.1095245297, y: 3447368.182024619},
      ...obj,
      viewer,
    }
    coordinateMovecamera(newobj)
  }

  //光伏电缆
  public async photovoltaicCable(obj, obj2) {
    let gp = dxfview.GetViewer().transaction.gpObj;
    let startList: any[] = [];
    let endList: any[] = [];
    if (obj.start.length) {
      let lsids:any=new Set()// 将结果合并到 临时数组 中
      for (const item of obj.start) {
          let ids = await obj2.identifyImgObj.legendTextpickupIds(item.legend);
          ids.forEach(id=>{lsids.add(id)})
      }
      let result = handletext([...lsids]);
      startList = startList.concat(result); 
    }
    
    if (obj.end.length) {
      let lsids:any=new Set()// 将结果合并到 临时数组 中
      for (const item of obj.end) {
          let ids = await obj2.identifyImgObj.legendTextpickupIds(item.legend);
          ids.forEach(id=>{lsids.add(id)})
      }
      let result = handletext([...lsids]);
      endList = endList.concat(result); // 将结果合并到 endList 中
    }
    console.log(endList, 'endList');
    
    let hlist=modellIST(obj.cableType)
    let newobj={
      hlist,
      ...obj.cableType,
    }
    let organizedData = this.organizeData(endList, startList,newobj); // 调用 organizeData 方法进行数据组织
    console.log(organizedData, 'organizedData');
    return organizedData
    
    function handletext(ids,name?) {
        let arr: any[] = [];
        ids.forEach(id => {
            let textObj = gp.getPBaseObjById(id);
            let text = textObj.getText();
            let wp = textObj.getInsertPoint();
            let point = { x: wp.x(), y: wp.y() };
            let obj:any={
              text, id:[id], point
            }
            name && (obj.name=name)
            arr.push(obj);
        });
        return arr;
    }
    function modellIST(obj){
      // console.log(obj);
      
      if(obj.calcType==1) return[]
      // V×V×N×2%/(0.00509×W)  V×V×N×2%/(0.00339×W)
      let H1=obj.peakVoltage*obj.peakVoltage*obj.componentCount*0.02/(0.00509*obj.peakPower)
      let H2=obj.peakVoltage*obj.peakVoltage*obj.componentCount*0.02/(0.00339*obj.peakPower)
      return [
        {norms:"H1Z2Z2-K-1500V",type:"1X4mm2",length:H1},
        {norms:'H1Z2Z2-K-1500V',type:'1X6mm2',length:H2},
        {norms:'H1Z2Z2-K-1500V',type:'1X8mm2',length:9999999999999},
      ]
    }
  }
  public organizeData(arrayA, arrayB,obj) {
    // 创建一个结果数组和一个新数组 arr
    const result:any = [];
    const arr:any = [];
    // 曼哈顿距离计算函数
    function calculateManhattanDistance(point1, point2) {
      // 曼哈顿距离计算公式，此处乘以绘图比例尺和放大系数来调整距离值
       return ( Math.abs(point1.x - point2.x) + Math.abs(point1.y - point2.y) )
    }
    function cableTypeFn(mergedDistance){
      if(obj.calcType==1) {
        return {norms:obj.norms,type:obj.type}
      }else {
        if(mergedDistance<obj.hlist[0].length) {
          return obj.hlist[0]
        }else if(mergedDistance<obj.hlist[1].length) {
          return obj.hlist[1]
        }else {
          return obj.hlist[2]
        }
      }
    }
    // 遍历数组 A
    arrayA.forEach(aItem => {
        // 筛选出数组 B 中 text 值包含 aItem.text 的元素
        // let children = arrayB.filter(bItem => bItem.text.includes(aItem.text));
        // 检查 aItem.text 是否为空字符串
        if (!aItem.text || aItem.text.trim() === "") {
          console.warn("aItem.text 为空字符串，已跳过该元素:", aItem);
          return; // 跳过当前循环
        }
        // 筛选出数组 B 中 text 值以 aItem.text 为前缀的元素
        let children = arrayB.filter(bItem => bItem.text.startsWith(aItem.text));
        // 处理重复的 text
        const textCounts = {};
        const groupedChildren = {};
        children.forEach(child => {
            if (!textCounts[child.text]) {
                textCounts[child.text] = 0;
                groupedChildren[child.text] = [];
            }
            textCounts[child.text]++;
            groupedChildren[child.text].push(child);
        });
        // 构造新的 children 数组
        const newChildren = [];
        for (const [text, group] of Object.entries(groupedChildren)) {
            if (group.length === 2) {
                // 合并两项为一项
                const mergedChild = { ...group[0] }; // 使用第一个作为合并后的对象
                // 合并 id 到数组中
                mergedChild.id = group.flatMap(item => item.id);
                // 计算两项之间的曼哈顿距离
                const distanceBetweenDuplicates = calculateManhattanDistance(
                    group[0].point,
                    group[1].point
                );

                // 计算与一级节点的距离
                const distanceToParent = calculateManhattanDistance(
                    group[0].point,
                    aItem.point
                );
                const distanceToParent2=calculateManhattanDistance(
                  group[1].point,
                  aItem.point
              );
                // 添加合并后的距离信息
                mergedChild.mergedDistance = distanceBetweenDuplicates + distanceToParent +distanceToParent2;
                mergedChild.distanceToParent = mergedChild.mergedDistance*dxfZsObj.drawingScale*(1+obj.amplificationFactor)+10
                // mergedChild.cableType = cableTypeFn(mergedChild.mergedDistance);
                let lsobj=cableTypeFn(mergedChild.distanceToParent);
                mergedChild.norms=lsobj.norms;
                mergedChild.type=lsobj.type;
                newChildren.push(mergedChild);
            } else if (group.length > 2) {
                // 超过 2 个相同的 text，添加到新数组 arr
                arr.push(...group);
            } else {
                // 只有一项，直接保留
                const child = group[0];
                child.distanceToParent = calculateManhattanDistance(child.point, aItem.point)*dxfZsObj.drawingScale*(1+obj.amplificationFactor)*2+10;
                // child.cableType = cableTypeFn(child.distanceToParent);
                let lsobj2=cableTypeFn(child.distanceToParent);
                child.norms=lsobj2.norms;
                child.type=lsobj2.type;
                newChildren.push(child);
            }
        }
        arr.length && console.log(arr,'超3个相同的text，添加到arr数组中');
        
        // 添加到结果中
        result.push({
            ...aItem,
            children: newChildren
        });
    });
    return result;
  }
}

const data = {
    setLegend: {
      combinerBox: {
        startDevice: "汇流箱",
        endDevice: "箱逆变",
        startLegend: "CM20250318000013",
        endLegend: "CM20250318000013",
        startRelativeHeight: 11,
        endRelativeHeight: 22,
        startCableLength: 33,
        endCableLength: 44,
      },
      inverter: {
        startDevice: "逆变器",
        endDevice: "箱变",
        startLegend: "CM20250318000013",
        endLegend: "CM20250318000013",
        startRelativeHeight: 11,
        endRelativeHeight: 22,
        startCableLength: 33,
        endCableLength: 44,
      },
    },
    laying: {
      bridge: [
        {
          legend: "",
          width: "",
          height: "",
          cableNumber: "",
          relativeHeight: "",
        },
        {
          legend: "",
          width: "",
          height: "",
          cableNumber: "",
          relativeHeight: "",
        },
      ],
      buried: [
        { legend: "", relativeDepth: "" },
        { legend: "", relativeDepth: "" },
      ],
    },
};