import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"
import { PubSub } from "emitter";

export class Text extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init() 
        PubSub.default.sub("confirmPop", this.draw.bind(this));//选择图元的图片
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        let p1=await this.lengthPick({
            tip:'指定文字高度',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p1)
        let p2=await this.lengthPick({
            tip:'指定文字高度',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p2)
        this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }else if(this.pnts.length===2){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        let plist=[this.pnts[1],v]
        this.mesh.geometry=new BufferGeometry().setFromPoints(plist);
    }

    draw(textBox:any){
        let {x,y}=globalDxf.wcsOff
        let gptext=new sg.SGObjText() 
        gptext.setText(textBox.value)
        let height= Math.sqrt((this.pnts[2].x-this.pnts[1].x) ** 2 + (this.pnts[2].y-this.pnts[1].y) ** 2 )
        gptext.setHeight(Number(height))
        gptext.setAngle(Number(textBox.rotationAngle))
        let p1=new sg.Point2d(this.pnts[0].x+x,this.pnts[0].y+y)
        gptext.setInsertPoint(p1)
        gptext.setAlignPoint(p1)
        // let [r,g,b]=this.color
        // gptext.setColor(new sg.SGColor(r,g,b,255))
        // gptext.setLineType(this.lineType)

        this.viewer.transaction.add(gptext)
        globalDxf.operation=GraphicType.None
        globalDxf.isdraw = false
        this.isSnap=false
        setTimeout(()=>{
            globalDxf.drawstate=DrawState.Pick
        })

    }
    
    complete(){
        let {x,y}=globalDxf.wcsOff
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        if(this.pnts.length<2) return
        PubSub.default.pub('textpop')
    }
}