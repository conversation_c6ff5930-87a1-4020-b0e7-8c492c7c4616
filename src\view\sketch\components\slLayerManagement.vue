<template>
    <div class="slLayerManagement">
        <el-dialog
            v-model="dialogVisible"
            title="图层管理"
            width="600"
        >
            <div>
                <el-table :data="layers" border style="width: 100%;" max-height="40vh">
                    <el-table-column prop="" label="显/隐" align="center" height="100" >
                        <template #header="scope">
                            <div style="display: flex;align-items: center;justify-content: center;">
                                <el-icon class="ico zpointer" size="20" @click.stop="btnALLhide">
                                    <View v-if="allIsVisible"/>
                                    <Hide v-else/>
                                </el-icon>
                                <span style="font-size: 14x;margin-left: 5px; user-select: none;">{{'显/隐'}}</span>
                            </div>
                            
                        </template>
                        <template #default="scope">
                            <div style="display: flex;align-items: center;justify-content: center;">
                                <el-icon class="ico zpointer" size="20" @click.stop="btnhide(scope?.row,scope?.$index)">
                                    <View v-if="scope?.row?.isVisible"/>
                                    <Hide v-else/>
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="layerColor" align="center" label="颜色" width="80" >
                        <template #default="scope">
                            <div style="display: flex;justify-content: center;">
                                <div class="colorbtn" :style="getLayerColorStyle(scope?.$index)"></div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="图层名称" align="center" height="100" />
                </el-table>
            </div>
            <!-- <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">Cancel</el-button>
                    <el-button type="primary" @click="dialogVisible = false">
                    Confirm
                    </el-button> 
                </div>
            </template> -->
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref,onMounted,nextTick,reactive } from 'vue'
import {measureObj,} from '../slMethod/measurementCategory'
import { ElLoading ,ElMessage } from "element-plus";
import { View,Hide} from '@element-plus/icons-vue'
import {dxflayers,setlayerVisible,getLayerColor,setlayerAllVisible} from 'dxf-viewer'

const layers=dxflayers.layers
const emits = defineEmits()
const props = defineProps({
    menuBarMethodObj:{
        type:Object
    },
})
const dialogVisible=ref(false)
const allIsVisible=ref(true)

const tableData=ref([{isVisible:true,layerColor:'pink'}])

onMounted(()=>{
    nextTick(()=>{
        console.log(dxflayers,'dxflayersdxflayersdxflayersdxflayers');
    })
})
const getLayerColorStyle=computed(()=>(idx)=>{
      let color=getLayerColor(idx)
      return {"background-color":color}
})
const openLayerManagementPop=()=>{
    dialogVisible.value=true
}
const btnALLhide=()=>{
    allIsVisible.value=!allIsVisible.value
    layers.forEach(item=>{
        item.isVisible=allIsVisible.value
    })
    setlayerAllVisible(allIsVisible.value)
    props.menuBarMethodObj.allslsceneVisible(allIsVisible.value)
}
const btnhide=(item,idx)=>{
    if(item.isSLidentifiedflag) {
        item.isVisible=!item.isVisible
        props.menuBarMethodObj.cstuc(item.name,item.isVisible)
    }else {
        setlayerVisible(idx)
    }
}

const changescale=()=>{
  emits('measureChangescale')
}
defineExpose({
    openLayerManagementPop,
})
</script>

<style lang="scss" scoped>
.slLayerManagement {
    :deep(.el-dialog__header) {
        text-align: center;
        background-color:#f2f2f2;
    }
    .ico {
        &:hover {
            background-color: red;
        }
    }
    .colorbtn{
      width:15px;
      height: 15px;
      border: 1px solid gray;
    }
}

</style>

