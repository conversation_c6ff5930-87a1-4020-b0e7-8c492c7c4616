# useProjectBuild 优化迁移指南

## 概述

本文档说明如何从旧版本的 `useProjectBuild` 迁移到优化后的新版本。

## 主要变更

### 1. 导入方式变更

**旧版本:**
```javascript
import useProjectBuild from './useProjectBuild'
import ExcelGenerator from './useExcel'
```

**新版本:**
```javascript
import useProjectBuild from './useProjectBuild'
import { generateExcel } from './useExcel' // 推荐使用便捷函数
```

### 2. 初始化方式变更

**旧版本:**
```javascript
const { initial } = useProjectBuild(olBoxRef)
await initial()
```

**新版本:**
```javascript
const { initialize } = useProjectBuild(olBoxRef, options)
await initialize()

// 或者使用兼容性接口
const { initial } = useProjectBuild(olBoxRef)
await initial() // 仍然可用
```

### 3. 状态访问方式变更

**旧版本:**
```javascript
const { 
  materialComponentTree,
  cadletaDetail 
} = useProjectBuild(olBoxRef)

// 直接访问 .value
console.log(materialComponentTree.value)
```

**新版本:**
```javascript
const { 
  materialComponentTree,
  cadletaDetail 
} = useProjectBuild(olBoxRef)

// 可以直接访问（已经是响应式的）
console.log(materialComponentTree)

// 或者使用计算属性（推荐）
console.log(materialComponentTree.value)
```

### 4. 错误处理改进

**旧版本:**
```javascript
// 没有统一的错误处理
const { initial } = useProjectBuild(olBoxRef)
try {
  await initial()
} catch (error) {
  console.error(error)
}
```

**新版本:**
```javascript
const { 
  initialize, 
  error, 
  loading 
} = useProjectBuild(olBoxRef)

// 方法1: 使用内置错误状态
await initialize()
if (error) {
  console.error('初始化失败:', error)
}

// 方法2: 传统try-catch
try {
  await initialize()
} catch (err) {
  console.error('初始化失败:', err.message)
}
```

### 5. 配置选项支持

**新版本新增:**
```javascript
const projectBuild = useProjectBuild(olBoxRef, {
  projectCode: 'PX20250724000001', // 自定义项目代码
  // 其他配置选项...
})
```

## 迁移步骤

### 步骤 1: 更新导入语句

```javascript
// 旧版本
import useProjectBuild from './useProjectBuild'
import ExcelGenerator from './useExcel'

// 新版本
import useProjectBuild from './useProjectBuild'
import { generateExcel } from './useExcel'
```

### 步骤 2: 更新初始化代码

```javascript
// 旧版本
const { initial, exportExcel } = useProjectBuild(olBoxRef)

onMounted(async () => {
  await initial()
})

// 新版本
const { 
  initialize, 
  exportExcel, 
  loading, 
  error 
} = useProjectBuild(olBoxRef)

onMounted(async () => {
  await initialize()
  
  // 可选：检查错误状态
  if (error) {
    console.error('初始化失败:', error)
  }
})
```

### 步骤 3: 更新Excel导出逻辑

```javascript
// 旧版本
const exportExcel = async () => {
  const generator = new ExcelGenerator()
  generator.generate(getExcelRowData())
}

// 新版本 - 已内置，直接调用
const { exportExcel } = useProjectBuild(olBoxRef)
await exportExcel()
```

### 步骤 4: 添加加载状态和错误处理

```javascript
// 新版本
const { 
  loading, 
  error, 
  initialize, 
  exportExcel 
} = useProjectBuild(olBoxRef)

// 在模板中使用
<template>
  <div v-loading="loading">
    <el-button 
      @click="initialize" 
      :disabled="loading"
    >
      {{ loading ? '加载中...' : '重新加载' }}
    </el-button>
    
    <el-button 
      @click="exportExcel" 
      :disabled="loading || !hasData"
    >
      导出Excel
    </el-button>
    
    <el-alert 
      v-if="error" 
      :title="error" 
      type="error" 
      show-icon 
    />
  </div>
</template>
```

## 新功能使用

### 1. 项目代码动态设置

```javascript
const { setProjectCode, initialize } = useProjectBuild(olBoxRef)

// 切换项目
const switchProject = async (newProjectCode) => {
  setProjectCode(newProjectCode)
  await initialize()
}
```

### 2. 数据重置

```javascript
const { reset, initialize } = useProjectBuild(olBoxRef)

// 重置并重新加载
const reloadData = async () => {
  reset()
  await initialize()
}
```

### 3. 数据验证

```javascript
import { validateProjectBuildData } from './useProjectBuild.example'

const projectBuild = useProjectBuild(olBoxRef)
await projectBuild.initialize()

// 验证数据
const validation = validateProjectBuildData(projectBuild)
if (!validation.isValid) {
  console.error('数据验证失败:', validation.details)
}
```

### 4. 性能监控

```javascript
import { useProjectBuildWithPerformance } from './useProjectBuild.example'

const { 
  initialize, 
  performanceMetrics 
} = useProjectBuildWithPerformance()

await initialize()
console.log('性能指标:', performanceMetrics.value)
```

## 兼容性说明

### 保持兼容的接口

以下接口保持向后兼容，无需修改：

- `initial()` - 映射到 `initialize()`
- `materialComponentTree` - 仍然可用
- `cadletaDetail` - 仍然可用
- `exportExcel()` - 功能增强但接口不变

### 已移除的接口

以下接口已被移除或重构：

- `loadcadletaDetail()` - 重命名为 `loadCadMetaDetail()`
- `handleFlatTree()` - 重命名为 `flattenTree()`
- `formatData()` - 重构为 `formatComponentData()`
- `getTotals()` - 重构为 `calculateTotals()`

## 注意事项

1. **调试代码清理**: 新版本移除了所有 `debugger` 和 `console.log` 语句
2. **错误处理**: 新版本提供了更完善的错误处理机制
3. **性能优化**: 减少了不必要的数据遍历和内存使用
4. **类型安全**: 增加了参数验证和类型检查
5. **配置灵活性**: 支持更多的配置选项

## 测试建议

迁移完成后，建议进行以下测试：

1. **功能测试**: 确保所有原有功能正常工作
2. **错误场景测试**: 测试网络错误、数据异常等场景
3. **性能测试**: 对比迁移前后的性能表现
4. **Excel导出测试**: 确保导出的Excel文件格式正确

## 获取帮助

如果在迁移过程中遇到问题，可以：

1. 查看 `useProjectBuild.example.js` 中的使用示例
2. 参考 `useProjectBuild.types.js` 中的类型定义
3. 检查控制台错误信息，新版本提供了更详细的错误提示
