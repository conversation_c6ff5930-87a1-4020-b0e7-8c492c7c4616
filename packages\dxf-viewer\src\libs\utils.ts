import { <PERSON>d<PERSON><PERSON><PERSON>, Vector2 } from "three"
import { Vector3 } from "three";
import { globalDxf } from "dxf-viewer/src"
import { pickupInfo,dxfZsObj,} from "dxf-viewer"
import * as THREE from "three"

/** Find intersection points of two segments in a parametric form.
 * @param {Vector2} a1 First segment start point.
 * @param {Vector2} a2 First segment end point.
 * @param {Vector2} b1 Second segment start point.
 * @param {Vector2} b2 Second segment end point.
 * @param {boolean} force Force intersection calculation even if intersection point is out of
 *  segment range.
 * @return {?number[3]} Parameters for the first and second segment in the intersection point
 *  (parameter value 0 corresponds to a start point, 1 - to an end point). Third number is segments
 *  direction vectors pseudo-cross-product. Null if there is no intersection.
 */
export function IntersectSegmentsParametric(a1, a2, b1, b2, force = false) {
    const a = a2.clone().sub(a1)
    const b = b2.clone().sub(b1)

    if (a.lengthSq() == 0 || b.lengthSq() == 0) {
        return null
    }

    const S = a.cross(b)
    if (Math.abs(S) <= Number.EPSILON) {
        /* Consider parallel. */
        return null
    }

    const c = b1.clone().sub(a1)

    const t = c.cross(b) / S
    if (!force && (t < 0 || t > 1)) {
        /* Intersection point is out the first segment endpoints. */
        return null
    }

    const u = c.cross(a) / S
    if (!force && (u < 0 || u > 1)) {
        /* Intersection point is out the second segment endpoints. */
        return null
    }

    return [t, u, S]
}

/**  Find intersection points of two segments.
 * @param {Vector2} a1 First segment start point.
 * @param {Vector2} a2 First segment end point.
 * @param {Vector2} b1 Second segment start point.
 * @param {Vector2} b2 Second segment end point.
 * @return {?Vector2} Intersection poi`nt coordinate, null if no intersection.
 */
export function IntersectSegments(a1, a2, b1, b2) {
    const params = IntersectSegmentsParametric(a1, a2, b1, b2)
    if (!params) {
        return null
    }
    return a2.clone().sub(a1).multiplyScalar(params[0]).add(a1)
}

export var ClearArray = (li: any[]) => {
    li.splice(0, li.length)
}

export const floatToUint = (f) => {
    return f >= 0 ? Math.floor(f) : Math.ceil(f) >>> 0;
}
export const combineBytesToUint32 = (byte1, byte2, byte3, byte4) => {
    const uint32Value =
        (byte1 << 24) |
        (byte2 << 16) |
        (byte3 << 8) |
        byte4;
    return uint32Value >>> 0; // 使用无符号右移确保结果为Uint32
}
export const splitBlockIdOld=(id)=>{//大小限制为2**16
    let blockId=id>>16;
    let entityId=id & 0x0000ffff;
    return {blockId,entityId}
}
export const splitBlockId=(id)=>{
    let n1=100000000
    let n2=10000
    let blockId=0,entityId=id
    if(id>n1){
        let compId=id-n1;
        blockId=Math.floor(compId/n2)
        entityId=compId%n2
    }
    return {
        blockId,
        entityId
    }
    
}
export const getBuffIds = (buf: Float32Array) => {
    let ids: Set<number> = new Set()
    for (let i = 0; i < buf.length/4; i++) {
        let rgba =buf.slice(i*4,i*4+4)
        let [r,g,b,a]=rgba
        if((r+g+b+a)==0)continue
        let i_rgba=rgba.map(e=>{return floatToUint(e * 255.0)})
        let [r1,g1,b1,a1]=i_rgba
        let id = combineBytesToUint32(a1, b1, g1, r1) // order is same as in fragment shader
        if (id > 0) {
            ids.add(id)
        }
    }
    return Array.from(ids)
}

export const validBuffId=(buf,idx)=>{
    let rgba =buf.slice(idx*4, idx*4+4)
    let [r,g,b,a]=rgba
    if((r+g+b+a)==0)return
    let i_rgba=rgba.map(e=>{return floatToUint(e * 255.0)})
    let [r1,g1,b1,a1]=i_rgba
    let id = combineBytesToUint32(a1, b1, g1, r1) // order is same as in fragment shader
    if (id > 0) {
        return id
    }
}
/**
 * 帧缓存颜色值rgba转id 
*/
export const rgba2id=(rgba)=>{
    let i_rgba=rgba.map(e=>{return floatToUint(e * 255.0)})
    let [r1,g1,b1,a1]=i_rgba
    let id = combineBytesToUint32(a1, b1, g1, r1) 
    return id
}
/**
 * 
 */
export const validIdsFromSet=(ins)=>{
    let outs:Set<number> = new Set();
    for(let rgba of ins){
        let id=rgba2id(rgba)
        if(id)outs.add(id)
    }
    return outs
}
var VecCompare=(v,v1,func=Math.min)=>{
    v.x=func(v.x,v1.x)
    v.y=func(v.y,v1.y)
    v.z&&(v.z=func(v.z,v1.z));
    return v
}
var VecMax=(v,v1)=>{
   return VecCompare(v,v1,Math.max)
}
var VecMin=(v,v1)=>{
    return VecCompare(v,v1)
}
export var Util={
    VecMax,
    VecMin
}

export var mathBigClamp=(v,threshold=10000)=>{
    return  v<0?Math.ceil(v/threshold)*threshold:Math.floor(v/threshold)*threshold
}

export var mathBigClampVec=(v,threshold=10000)=>{
    return {
        x:mathBigClamp(v.x,threshold),
        y:mathBigClamp(v.y,threshold)
    }
}

export const debounce=(func, wait)=> {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, args);
        }, wait);
    };
}

export const throttle=(func, wait)=> {
    let lastTime = 0;
    return function(...args) {
        const now = Date.now();
        if (now - lastTime >= wait) {
            func.apply(this, args);
            lastTime = now;
        }
    };
}


export const idmovecamera=(viewer,idlist)=>{//根据id移动镜头并高亮
    // let {x,y}=globalDxf.wcsOff
    let gp=viewer.transaction.gpObj
    const bluePrintCount = Number(pickupInfo.bluePrintCount)
    let bdx=new sg.Rect2d()
    console.log(idlist,'idlist');
    
   let arr=idlist.map(id=>{
        let newid
        if(id.toString().includes('A')) {
            newid=Number(id.slice(1)) + bluePrintCount
        }else {
            newid=Number(id)
        }
        // let newobj=gp.getPBaseObjById(newid)
        let newobj:any=null
        let {entityId,blockId}=splitBlockId(newid)
        if(blockId) {
            newobj=sg.copyInsertBaseObj(dxfZsObj.drawingId,newid)
        }else {
            newobj=gp.getPBaseObjById(newid)
        }
        let temprect = newobj.getBoundingBox()
        bdx.add(temprect)
        return newid
    })
    let midpt = bdx.getMiddlePoint();
    let lzb=bdx.getLeftTopPoint()//左上角
    let width=bdx.width()
    let heigth=bdx.heigth()
    let  leftTopPoint={x:lzb.x(),y:lzb.y()}

    viewer.FitView(leftTopPoint.x,leftTopPoint.x + width,leftTopPoint.y- heigth,leftTopPoint.y)
    console.log(viewer.camera.position.x,'viewer.camera.position.x');
    viewer.controls.target = new Vector3(viewer.camera.position.x, viewer.camera.position.y, 0)
    viewer.controls.update()
    viewer.camera.zoom = 1
    viewer.camera.updateProjectionMatrix()
    viewer.selectionScene.addObjsByIds(gp,arr ,{color:'',isEdit:true})
}
export const plistMovecamera=(obj)=>{//根据坐标移动镜头并高亮
    let {x,y}=globalDxf.wcsOff
    let viewer=obj.viewer
    viewer.selectionScene.dynmScene.clear()
    let vertices:any=[]
    obj.plist.forEach(item=>{
        let v1=new Vector3(item.x-x, item.y-y, 0)
        vertices.push(v1)
    })
    // 2. 创建包围盒并从所有点计算
    const boundingBox = new THREE.Box3().setFromPoints(vertices);
    let minP=boundingBox.min
    let maxP=boundingBox.max
    let centerP= boundingBox.getCenter(new THREE.Vector3())
    let size=boundingBox.getSize(new THREE.Vector3())
    let width=size.x
    let heigth=size.y
    const geometry = new THREE.BufferGeometry().setFromPoints(vertices);
    const material = new THREE.LineBasicMaterial({ color:'orange'});
    const mesh1 = new THREE.LineLoop(geometry, material); //LineLoop
    viewer.selectionScene.dynmScene.add(mesh1)
    const center = new THREE.Vector2(centerP.x, centerP.y)
    viewer.SetViewToObj(center,Math.max(width, heigth))
    viewer.Render() 
}

export const idSurroundingBox=(drawingId,ids)=>{//根据图纸ID和图元ID计算包围盒
    const vecint = new sg.vector_int
    ids.forEach(id=>vecint.push_back(Number(id)))
    let ElementObj=new sg.ElementObj(vecint)
    let bw=sg.getElementObjRect(drawingId,ElementObj)
    let czb=bw.getMiddlePoint()//中心点坐标
    const obj={
        centerPointXY:czb
    }
    return obj
}

export const gpCountLayer=(gp)=>{//根据gp获取所有图层名字
    let arr:any=[]
    let dxfbase=gp.getDxfBaseData()
    let laycount=dxfbase.layerCount()
    for(let i=0;i<laycount;i++){
        let lay=dxfbase.getLayer(i)
        let name=lay.getName()
        arr.push(name)
    }
    return [...new Set(arr)]
}