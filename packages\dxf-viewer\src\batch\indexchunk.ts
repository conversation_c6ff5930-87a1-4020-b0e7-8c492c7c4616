import { NativeType } from "../constant"
import { DynamicBuffer } from "../libs"
import { RenderBatch } from "./renderBatch"

export class IndexedChunk {
    public batch:RenderBatch
    public indices:DynamicBuffer
    public vertices:DynamicBuffer
    public entityIds:DynamicBuffer
    public entityColors:DynamicBuffer
    public lineStyleIds:DynamicBuffer
    public lineScales:DynamicBuffer
    constructor(batch,initialCapacity) {
        this.batch=batch
        // if (initialCapacity < 16) {
        //     initialCapacity = 16
        // }
        /* Average two indices per vertex. */
        this.indices = new DynamicBuffer(NativeType.UINT16, initialCapacity * 2)
        /* Two components per vertex. */
        this.vertices = new DynamicBuffer(NativeType.FLOAT32, initialCapacity * 2)
        /* One entity id per vertex*/
        this.entityIds = new DynamicBuffer(NativeType.UINT32, initialCapacity)
        this.lineStyleIds=new DynamicBuffer(NativeType.INT8,initialCapacity)
        this.lineScales=new DynamicBuffer(NativeType.FLOAT32,initialCapacity)
        this.entityColors=new DynamicBuffer(NativeType.UINT8,initialCapacity*4)
    }
}