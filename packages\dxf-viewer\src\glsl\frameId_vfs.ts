import { InstanceType } from "../constant"

export const vfs_frameId=(instanceType, pointSize)=>{
    const fullInstanceAttr = instanceType === InstanceType.FULL ?
            `
            /* First row. */
            in vec3 instanceTransform0;
            /* Second row. */
            in vec3 instanceTransform1;
            in uint instanceId;
            ` : ""
        const fullInstanceTransform = instanceType === InstanceType.FULL ?
            `
            pos.xy = mat2(instanceTransform0[0], instanceTransform1[0],
                          instanceTransform0[1], instanceTransform1[1]) * pos.xy +
                     vec2(instanceTransform0[2], instanceTransform1[2]);
            ` : ""

        const pointInstanceAttr = instanceType === InstanceType.POINT ?
            `
            in vec2 instanceTransform;
            ` : ""
        const pointInstanceTransform = instanceType === InstanceType.POINT ?
            `
            pos.xy += instanceTransform;
            ` : ""

        const pointSizeUniform = pointSize ? "uniform float pointSize;" : ""
        const pointSizeAssigment = pointSize ? "gl_PointSize = pointSize;" : ""
        // let entityIdAssigment = instanceType === InstanceType.FULL ?
        //     "idOut = (instanceId<<16U & 0xFFFFFFFFU) + entityId;" : "idOut = entityId;";
        let entityIdAssigment = instanceType === InstanceType.FULL ?
            "idOut =100000000U + instanceId*10000U + entityId;" : "idOut = entityId;";
        return {
            vertex: `
            precision highp float;
            precision highp int;
            in vec2 position;
            in uint entityId;
            ${fullInstanceAttr}
            flat out uint idOut;
            ${pointInstanceAttr}
            uniform mat4 modelViewMatrix;
            uniform mat4 projectionMatrix;
            ${pointSizeUniform}

            void main() {
                ${entityIdAssigment}
                vec4 pos = vec4(position, 0.0, 1.0);
                ${fullInstanceTransform}
                ${pointInstanceTransform}
                gl_Position = projectionMatrix * modelViewMatrix * pos;
                ${pointSizeAssigment}
            }
            `,
            fragment: `

            precision highp float;
            precision highp int;
            flat in uint idOut;
            uniform vec3 color;
            out vec4 fragId;

            void main() {
                uint r = idOut & 0xFFU;
                uint g = (idOut >> 8U) & 0xFFU;
                uint b = (idOut >> 16U) & 0xFFU;
                uint a = (idOut >> 24U) & 0xFFU;
                fragId = vec4(float(r), float(g), float(b), float(a)) / 255.0;
            }
            `
        }
}