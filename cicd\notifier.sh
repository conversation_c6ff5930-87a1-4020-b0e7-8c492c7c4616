#!/usr/bin/env bash
set -e

echo "最后的处理..."

echo $CI_JOB_STAGE
echo "$CI_JOB_STAGE"
jobStage=$CI_JOB_STAGE
echo ${jobStage}
echo jobStage

# 替换特殊字符
commitMessage=$(echo "${CI_COMMIT_MESSAGE}" | sed 's/\\/\\\\/g; s/"/\\"/g')

curl 'https://oapi.dingtalk.com/robot/send?access_token=74393caa99530531065c06458e9b480a37ed97b55d037b06548adf32aad02aa4' \
   -m 40 \
   -X POST \
   -H 'Content-Type: application/json' \
   -d '
   {
        "msgtype": "markdown",
        "markdown": {
            "text": "服务名称：['$CI_PROJECT_NAME'-'${ENV}']  \nStage： '${CI_JOB_STAGE}'  \n执行状态：'$CI_JOB_STATUS'  \n发布人员：'$GITLAB_USER_NAME'  \n链接地址：'$CI_JOB_URL'  \n提交内容：'"${commitMessage}"' \n",
            "title": "部署通知"
        }
   }'