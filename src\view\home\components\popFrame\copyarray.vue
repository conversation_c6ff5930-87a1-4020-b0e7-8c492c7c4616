<template>
    <div class="cad_dialogBody">
        <el-dialog :model-value="isVisible" title="复制" width="430"  class="textPop" :before-close="onCancel">
            <div class="body" style="margin: 10px;">
                <div class="row fb">
                    <RowItem title="行数" twid="50">
                        <el-input v-model="layout.nrow" style="width: 100px"/>
                    </RowItem>
                    <RowItem title="行间距" twid="50">
                        <el-input v-model="layout.x" style="width: 100px"/>
                    </RowItem>
                </div>
                <div style="height: 10px;"></div>
                <div class="row fb">
                    <RowItem title="列数" twid="50">
                        <el-input v-model="layout.ncol" style="width: 100px"/>
                    </RowItem>
                    <RowItem title="列间距" twid="50">
                        <el-input v-model="layout.y" style="width: 100px"/>
                    </RowItem>
                </div>
               
            </div>
            <template #footer>
                <div class="dialog-footer">
                <el-button type="primary" @click="onConfirm">确认</el-button>
                <el-button @click="onCancel">取消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>

</template>

<script lang="ts" setup>
import { ref,onMounted,nextTick,reactive } from 'vue'
import { PubSub } from 'emitter';

const emits = defineEmits(['onConfirm'])
const layout=reactive({
    nrow:4,
    ncol:4,
    x:100,
    y:100
})

const isVisible=ref(false)
const onConfirm=()=>{
    isVisible.value=false
    emits("onConfirm",layout)
    PubSub.default.pub('copyarray',layout)
}

const onCancel=()=>{
    isVisible.value=false
}

const show=()=>{
    isVisible.value=true
}

defineExpose({
    show
})

onMounted(()=>{
    
})
</script>

<style lang="scss" scoped>
.body {
    color: white;
}
</style>
  
  