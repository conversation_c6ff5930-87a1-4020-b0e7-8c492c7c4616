<template>
  <div class="header">
    <div class="header-logo">
      <mapBoxLogo></mapBoxLogo>
    </div>
    <!-- 控制按钮插槽 -->
    <div class="control-button">
      <slot name="controlButton"></slot>
    </div>
  </div>
</template>

<script setup>
import mapBoxLogo from "./mapBoxLogo.vue";
</script>

<style scoped lang="scss">
.header {
  width: 100%;
  background-color: #f7f7f7;
  border-bottom: 1px solid #e8e8e8;
  .header-logo{
    width: 100%;
  }
}
</style>
