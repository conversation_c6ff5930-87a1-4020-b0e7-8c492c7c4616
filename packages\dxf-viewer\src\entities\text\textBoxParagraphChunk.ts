import { TextBlock } from "./textBlock"

export class  TextBoxParagraphChunk{
    /**
     * @param {TextBox.Paragraph} paragraph
     * @param {number} fontSize
     * @param {?TextBox.Paragraph.Chunk} prevChunk
     */
    public paragraph
    public fontSize
    public prevChunk
    public lastChar
    public lastShape
    public leadingSpaces
    public spaceStartKerning
    public spaceEndKerning
    public block
    public position
    constructor(paragraph, fontSize, prevChunk) {
        this.paragraph = paragraph
        this.fontSize = fontSize
        this.prevChunk = prevChunk
        this.lastChar = null
        this.lastShape = null
        this.leadingSpaces = 0
        this.spaceStartKerning = null
        this.spaceEndKerning = null
        this.block = null
        this.position = null
    }

    PushSpace() {
        if (this.block) {
            throw new Error("Illegal operation")
        }
        this.leadingSpaces++
    }

    /**
     * @param char {string}
     * @param shape {CharShape}
     */
    PushChar(char, shape) {
        if (this.spaceStartKerning === null) {
            if (this.leadingSpaces === 0) {
                this.spaceStartKerning = 0
                this.spaceEndKerning = 0
            } else {
                if (this.prevChunk && this.prevChunk.lastShape &&
                    this.prevChunk.fontSize === this.fontSize &&
                    this.prevChunk.lastShape.font === this.paragraph.textBox.spaceShape.font) {

                    this.spaceStartKerning =
                        this.prevChunk.lastShape.font.GetKerning(this.prevChunk.lastChar, " ")
                } else {
                    this.spaceStartKerning = 0
                }
                if (shape.font === this.paragraph.textBox.spaceShape.font) {
                    this.spaceEndKerning = shape.font.GetKerning(" ", char)
                } else {
                    this.spaceEndKerning = 0
                }
            }
        }

        if (this.block === null) {
            this.block = new TextBlock(this.fontSize)
        }
        this.block.PushChar(char, shape)

        this.lastChar = char
        this.lastShape = shape
    }

    GetSpacingWidth() {
        return (this.leadingSpaces * this.paragraph.textBox.spaceShape.advance +
            this.spaceStartKerning + this.spaceEndKerning) * this.fontSize
    }

    GetWidth(withSpacing) {
        if (this.block === null) {
            return 0
        }
        let width = this.block.GetCurrentPosition()
        if (withSpacing) {
            width += this.GetSpacingWidth()
        }
        return width
    }
}