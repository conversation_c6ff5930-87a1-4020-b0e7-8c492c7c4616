<template>
    <div class="SideMenu">
        <div class="ButtonStyle" @click="btnLayerManagement">
            <el-icon class="ico" :size="20">
                <Grid  />
            </el-icon>
            <div>图层设置</div>
        </div>
        <div class="ButtonStyle" :style="{backgroundColor:pickupInfo.slcurrentstate===SlScopeType.Del?'#0089ff':''}" 
          @click="btnlengFn(SlScopeType.Del)">
            <el-icon class="ico" :size="20">
                <Delete  />
            </el-icon>
            <div>删除构件</div>
        </div>
        <div v-if="ceshiisshow">
          <!-- IDENTIFY -->
          <div class="ButtonStyle" :style="{backgroundColor:pickupInfo.slcurrentstate===SlScopeType.All?'#0089ff':''}" v-if="drawingMenuControl==='LENGTH'"
          @click="graphicRecognitionPop(SlScopeType.All)">
              <el-icon class="ico" :size="20">
                  <FullScreen  />
              </el-icon>
              <div>图元识别</div>
          </div>
          <div class="ButtonStyle" :style="{backgroundColor:pickupInfo.slcurrentstate===SlScopeType.Draw || pickupInfo.slcurrentstate===SlScopeType.AreaDraw?'#0089ff':''}"  v-if="drawingMenuControl==='LENGTH'"
          @click="btnlengFn(SlScopeType.Draw)">
              <el-icon class="ico" :size="20">
                  <Share  />
              </el-icon>
              <div>绘制路径</div>
          </div>
          <div class="ButtonStyle" :style="{backgroundColor:pickupInfo.slcurrentstate===SlScopeType.Pickup?'#0089ff':''}" v-if="drawingMenuControl==='LENGTH'"
          @click="btnlengFn(SlScopeType.ThreeDPickup)">
              <el-icon class="ico" :size="20" >
                  <TopLeft  />
              </el-icon>
              <div>拾取路径</div>
          </div>
          <div class="ButtonStyle" v-if="drawingMenuControl==='ATTACH'" 
          @click="btnattach">
              <el-icon class="ico" :size="20" >
                  <Connection  />
              </el-icon>
              <div>附属构建</div>
          </div>
          <div class="scale ButtonStyle" @click="btnscalePop">
              <el-icon class="ico" :size="20">
                  <ScaleToOriginal  />
              </el-icon>
              <div >比例尺</div>
          </div>
          <div class="ButtonStyle"  :style="{backgroundColor:pickupInfo.slcurrentstate===SlScopeType.AI?'#0089ff':''}"
            @click="btnlengFn(SlScopeType.AI)">
              <el-icon class="ico" :size="20" >
                  <DocumentCopy  />
              </el-icon>
              <div>文字复制</div>
          </div>
        </div>
        <!-- AreaDraw -->
        <div class="ButtonStyle" 
        @click="btnceshi">
            <el-icon class="ico" :size="20" >
                <Connection  />
            </el-icon>
            <div>显示全部</div>
        </div>

    </div>
    <SlLayerManagement ref="layerManagementPop" :menuBarMethodObj="menuBarMethodObj"></SlLayerManagement>
    

  </template>
  
  <script lang="ts" setup>
  import { ref,onMounted,nextTick,reactive } from 'vue'
  import { Position ,ScaleToOriginal,Top,Delete,FullScreen,Connection,
    Share,TopLeft,SetUp,DocumentCopy,Grid,Notebook,
  } from '@element-plus/icons-vue'

  import {graphicRecognitionObj,} from '../slMethod/identifyImg'
  import { drawingMenuControl,comparisonResultsGp} from '../slMethod/menuBarMethod'
  import {SlScopeType,pickupInfo} from 'dxf-viewer'
  import { PubSub } from 'emitter';
  import SlLayerManagement from './slLayerManagement.vue'


  const layerManagementPop=ref(null)


  const popswitch=ref(false)

  const checked1 = ref(false)
  const ceshiisshow=ref(false)
  const btntype=ref('')
 
  const emits = defineEmits(['attach','inherit','drawingComparison',])

  const props = defineProps({
    measurementCategoryObj:{
      type:Object
    },
    sLcontrolObj:{
        type:Object
    },
    menuBarMethodObj:{
        type:Object
    },
    refdxfpop:{
        type:Object
    }
  })
  const btnlengFn=(type)=>{
    pickupInfo.pickupIdlist=[]
    props.sLcontrolObj.setState(type)
  }
  const btnscalePop=()=>{
    props.measurementCategoryObj.openPop()
  }
  const btnattach=()=>{
    emits('attach')
  }
  const btninherit=()=>{
    emits('inherit')
  }


  const graphicRecognitionPop=(type)=>{
    graphicRecognitionObj.graphicRecognitionPop=true
    props.sLcontrolObj.setState(type)
  }

  const btnLayerManagement=()=>{
    layerManagementPop.value.openLayerManagementPop()
  }
  const btnDrawingComparison=()=>{
    emits('drawingComparison')
  }
  const btnDrawingComparisonRes=()=>{
    props.refdxfpop.switchPop()
    props.refdxfpop.render(comparisonResultsGp)
  }

  const btnceshi=()=>{
    PubSub.default.pub('fitview') 
  }

  const btnceshishow=()=>{
    ceshiisshow.value=!ceshiisshow.value
  }
  const btntextcopy=()=>{
    // AitextPopref.value.ceshi()
  }

  

  defineExpose({
    btnceshishow,
  })
  </script>
  
  <style lang="scss" scoped>

.SideMenu {
    display: flex;
    flex-direction: column;
    position: absolute;
    // background-color: red;
    color: #fff;
    z-index: 1;
    left: 10px;
    top: 60px;
    .ButtonStyle {
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }

}
  
//   .measure {
//     width: 100%;
//     background-color: #333333;
//     color: #fff;
//     display: flex;
//     .region {
//         padding: 5px 10px;
//         border-right:1px solid #fff;
//         display: flex;
//         flex-direction: column;
//         justify-content: space-between;
//         .ButtonStyle {
//             display: flex;
//             flex-direction: column;
//             justify-content: center;
//             align-items: center;
//             cursor: pointer;
//         }
//         .drawingoperation {
//             display: flex;
//             .scale {
//                 margin-right: 10px;
//             }
//             :deep(.drawingFn) {
//                 .el-checkbox:last-of-type {
//                     height: 25px;
//                 }
//             }
//         }
//         .length {
//             display: flex;
//             .length1 {
//                 margin: 0 10px;
//             }
//         }


//         .partitionName {
//             text-align: center;
//             margin-top: 10px;
//         }
//     }
//     .zpointer {
//         cursor: pointer;
//     }
//   }

  </style>
  
  