<template>
    <div class="canvasContainer" id="dxfviewercontainer">
        <div style="width: 100%;position: absolute;top: 0;left: 0;z-index: 99;" v-if="dataObj.isLoading">
            <el-progress color="#409EFF" class="infinite-scroll-progress" :duration="1" :stroke-width="8"
                :percentage="100" :indeterminate="true" />
        </div>
    </div>
</template>

<script setup>
import { DxfViewer,setupCursorStyle } from "dxf-viewer"
import * as three from "three"

const emits = defineEmits(['dxf-loaded', 'dxf-cleared', 'dxf-destroyed',
  'dxf-resized', 'dxf-pointerdown', 'dxf-pointerup',
  'dxf-viewChanged', 'dxf-message', 'GetViewer'])

const props = defineProps({
  dxfUrl: {
      type: String,
      default: null
  },
  fonts: {
      default: null
  },
  options: {
      default() {
          return {
              clearColor: new three.Color("black"),//black
              autoResize: true,
              colorCorrection: true,
              sceneOptions: {
                  wireframeMesh: true
              },
              preserveDrawingBuffer: true,
          }
      }
  }
})

const dataObj = reactive(
  {
      isLoading: false,
      progress: null,
      progressText: null,
      curProgressPhase: null,
      error: null,
  }
)
let dv

onMounted(() => {
  dv = new DxfViewer(document.getElementById('dxfviewercontainer'), props.options)
  setupCursorStyle("dxfviewercontainer")
  const Subscribe = eventName => {
      dv.Subscribe(eventName, e => emits("dxf-" + eventName, e))
  }
  for (const eventName of ["loaded", "cleared", "destroyed", "resized", "pointerdown",
      "pointerup", "viewChanged", "message"]) {
      Subscribe(eventName)
  }

})

const Load = async (url) => {
  dataObj.isLoading = true
  dataObj.error = null
  try {
      await dv.Load({
          url,
          fonts: props.fonts,
          progressCbk: _OnProgress.bind(this),
      })
  } catch (error) {
      dataObj.error = error.toString()
  } finally {
      dataObj.isLoading = false
      dataObj.progressText = null
      dataObj.progress = null
      dataObj.curProgressPhase = null
  }
}

/** @return {DxfViewer} */
const GetViewer = () => {
  return dv
}

const _OnProgress = (phase, size, totalSize) => {
  if (phase !== dataObj.curProgressPhase) {
      switch (phase) {
          case "font":
              dataObj.progressText = "Fetching fonts..."
              break
          case "fetch":
              dataObj.progressText = "Fetching file..."
              break
          case "parse":
              dataObj.progressText = "Parsing file..."
              break
          case "prepare":
              dataObj.progressText = "Preparing rendering data..."
              break
      }
      dataObj.curProgressPhase = phase
  }
  if (totalSize === null) {
      dataObj.progress = -1
  } else {
      dataObj.progress = size / totalSize
  }
}

const hideCAD=()=>{
    dv.visual=!dv.visual
    dv.Render()
}

onUnmounted(() => {
  dv.Destroy()
  dv = null
})

defineExpose({
  GetViewer,
  Load,
  hideCAD
})
</script>

<style lang="scss" scoped>
.canvasContainer {
    width: 100%;
    height: 100%;
    min-width: 100px;
    min-height: 100px;
    position: relative;
    z-index: 1000;
    background-color: transparent !important;
    :deep(.infinite-scroll-progress) {
        .el-progress__text {
            display: none;
            /* 不显示文字 */
       }

        .el-progress-bar__inner {
            transition: none;
            /* 移除过渡动画 */
            animation: scroll-left 2s linear infinite;
            /* 线性无限循环的滚动动画 */
        }

        @keyframes scroll-left {
            from {
                transform: translateX(-100%);
            }
            to {
                transform: translateX(0);
            }
        }
    }
}
</style>