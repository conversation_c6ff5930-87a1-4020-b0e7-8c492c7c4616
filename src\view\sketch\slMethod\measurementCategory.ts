import { PubSub } from 'emitter';
import {exportAppointDxfData,dxfZsObj,filterGroupsByIds,
    hasItBeenSelectedObj,SlScopeType,splitBlockId,pickupInfo,newAddLayer} from 'dxf-viewer'
import { getInterpNum,getSgBufInf } from "dxf-viewer/src/scene"
import {gpCountLayer} from "dxf-viewer/src/libs"


export const measureObj=reactive({
    measurePop:false,
    measureScaleText:'1',//比例尺
    measureLength:'',

    sizePop:false,
    sizetext:'',//实际尺寸对话框

})

export const distinguishObj=reactive({
    distinguishPop:false,
    entityFlag:true,
    layerFlag:true,
    colorFlag:true,
    linetypeFlag:true,
})

interface ContextMenuOptions {
    rightClickMenUpoP: boolean;
    buttonList: any[];
    position: {
        x: string | number;
        y: string | number;
    };
}
export const rightClickMenuObj:ContextMenuOptions=reactive({
    rightClickMenUpoP:false,
    buttonList:[],
    position:{
      x:'',
      y:'',
    }
})
export const AitextObj=reactive({
    AitextPopflag:false,
    value:''
})

let dxfview:any=null
export class MeasurementCategory {
    // public dxfview:any
    public layerInfo:any //导出的dxf字符串
    public fenceGroup:any  //id 和长度
    public AItext:string=''
    public deleteInfo:any
    public sLcontrolObj:any

    constructor(obj){
        dxfview=obj.dxfview
        this.sLcontrolObj=obj.sLcontrolObj
        rightClickMenuObj.buttonList=[
            {text:'确认',fn:()=>{
              this.rightClickConfirm()
            },color:'#409eff'},
            {text:'取消',fn:()=>{
               this.rightClickcancel()
            },color:'#eebe77'}
        ]
    }

    public openPop(){
        measureObj.measurePop=true
    }
    public closePop(){
        measureObj.measurePop=false
    }

    public closesizePop(){
        measureObj.sizetext=''
        measureObj.sizePop=false
    }
    public opensizePop(l){
        measureObj.measureLength=l || ''
        measureObj.sizetext=l || ''
        measureObj.sizePop=true
    }

    public  async rightClickConfirm(){
       switch (pickupInfo.slcurrentstate) {
            case SlScopeType.Scale://比例尺
              break;
            case SlScopeType.Draw://绘制
                this.drawConfirm()
              break;
            case SlScopeType.AreaDraw://绘制
                this.drawConfirm()
              break;
            case SlScopeType.ThreeDDraw://绘制
                this.ThreeDDrawConfirm()
              break;
            case SlScopeType.Pickup://拾取
                this.pickupConfirm()
              break;
            case SlScopeType.AreaPickup://面积拾取
                this.pickupConfirm()
              break;
            case SlScopeType.ThreeDPickup://3D拾取
                this.ThreeDpickupConfirm()
              break;
            case SlScopeType.PeiDistinguish://图元识别
                this.pickupConfirm()
              break;
            case SlScopeType.AI:
                this.textRecognition()
              break;
            case SlScopeType.Del:
                await this.sldel()
                break;
            default:
        }
    }
    public leftClickConfirm(){
        switch (pickupInfo.slcurrentstate) {
            case SlScopeType.AI: //文字识别
                 this.textRecognition()
                break;
            default:
        }
    }
    public rightClickcancel(){
        
        switch ( pickupInfo.slcurrentstate ) {
            case SlScopeType.Scale://比例尺
              break;
            case SlScopeType.Draw://绘制
               this.drawCancel()
              break;
            case SlScopeType.Pickup://拾取
               this.pickupCancel()
              break;
            case SlScopeType.PeiDistinguish://图元识别
              this.pickupCancel()
              break;
            case SlScopeType.Del://删除
              this.pickupCancel()
             break;
            case SlScopeType.AI:
                this.pickupCancel()
                break;
            default:
        }
        rightClickMenuObj.rightClickMenUpoP=false
        
    }

    public sldel=async()=>{
        const nbluePrintCount = Number(pickupInfo.bluePrintCount)
        let ele:any=[]
        let lsentityId:any=[]
        let gro:any=[]
        ele=hasItBeenSelectedObj(pickupInfo.hideSelectedIdObj.elements,pickupInfo.pickupIdlist)
        lsentityId = pickupInfo.pickupIdlist.filter(item => !ele.includes(item));
        // gro=filterByIds(pickupInfo.hideSelectedIdObj.groups,lsentityId)
        gro=filterGroupsByIds(pickupInfo.hideSelectedIdObj.groups,lsentityId)
        pickupInfo.otherSelectedID={
            elements:ele,
            groups:gro,
        }
        const viewer = dxfview.GetViewer()
        const transaction = viewer.transaction

    //    let delFenceIds=pickupInfo.pickupIdlist.filter(id=>id>nbluePrintCount)
        let delFenceIds=pickupInfo.pickupIdlist.filter(id=>pickupInfo.customID.includes(id))
        let newIds:any=[]
        if(delFenceIds.length) {
                await transaction.del(delFenceIds)
                this.exportDxf()
                newIds= delFenceIds.map(id=>{
                    let newid = id - Number(pickupInfo.bluePrintCount)
                    return 'A' + newid
                })
        }
        this.deleteInfo={
        otherSelectedIdObj:pickupInfo.otherSelectedID,
        layerObj:{
            element:newIds,
            layerInfo:delFenceIds.length?this.layerInfo:'',
        }
       }
       pickupInfo.selectedID=pickupInfo.selectedID.filter(id=>!pickupInfo.pickupIdlist.includes(id))
       viewer.drawingSelected()
       viewer.selectionScene.clear()
       this.exportDxf()
    //    viewer.Render()
    //    rightClickMenuObj.rightClickMenUpoP = false
    }
    public pickupConfirm=()=>{
        if(!pickupInfo.pickupIdlist.length) return

        let arr:any=[]
        let gparr:any=[]
        let trans= dxfview.GetViewer().transaction
        let gpwasm= dxfview.GetViewer().transaction.gpObj
        newAddLayer(gpwasm,pickupInfo.structureTypeName)
        let strtype=pickupInfo.slcurrentstate==SlScopeType.AreaPickup?'area'
                    :pickupInfo.slcurrentstate==SlScopeType.Pickup?'length'
                    :'null'
        pickupInfo.pickupIdlist.forEach(id=>{
            let {entityId,blockId}=splitBlockId(id)
            let newobj:any=null
            let objinfo={
                type:strtype,
            }
            let sgCurve:any =null
            if(blockId) {
                newobj=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
            }else {
                newobj=gpwasm.getPBaseObjById(id).copyNewOne()
            }
            newobj.setColor(new sg.SGColor(236, 128, 141,255))
            const type = newobj.getObjType()
            if(type === 'SGObjCurve'){
                sgCurve = sg.SGObjTool.convertToCurve(newobj).getCurve()
            }else if(type === 'SGObjComBinCurve'){
                sgCurve=sg.SGObjTool.convertToComBinCurve(newobj).getCurve()
            }
            // area = area * Number(measureObj.measureScaleText)* Number(measureObj.measureScaleText)
            
            let fenid=newobj.getDrawObjectId()
            pickupInfo.selectedID.push(Number(fenid))
            const newid =fenid - Number(pickupInfo.bluePrintCount)
            objinfo.id='A' + newid
            if(strtype=='area') {
                objinfo[strtype]=sgCurve.area()* Number(measureObj.measureScaleText)* Number(measureObj.measureScaleText)
            }else {
                objinfo[strtype]=sgCurve.length()* Number(measureObj.measureScaleText)
            }
            arr.push(objinfo)
            
            newobj.setLayer(pickupInfo.structureTypeName)
            gparr.push(newobj)
        })
        pickupInfo.pickupIdlist = []
        dxfview.GetViewer().selectionScene.clear()
        trans.add(gparr)
        this.fenceGroup=arr
        // rightClickMenuObj.rightClickMenUpoP=false
        // this.exportDxf()
    }

    public drawConfirm=()=>{
        let strtype=pickupInfo.slcurrentstate==SlScopeType.AreaDraw?'area'
        :pickupInfo.slcurrentstate==SlScopeType.Draw?'length'
        :'null'
        if(pickupInfo.fenceDrawlist.length){
            let arr:any=[]
            pickupInfo.fenceDrawlist.forEach(item=>{
                console.log(item,'item.area');
                let objinfo={
                    type:strtype,
                }
                pickupInfo.selectedID.push(Number(item.id))
                console.log(item.id,'item.id');
                
                const newid = item.id- Number(pickupInfo.bluePrintCount)
                console.log(newid,'newid');
                
                objinfo.id='A' + newid
                if(strtype=='area') {
                    objinfo[strtype]=item.area* Number(measureObj.measureScaleText)* Number(measureObj.measureScaleText)
                }else {
                    objinfo[strtype]=item.length* Number(measureObj.measureScaleText)
                }
                arr.push(objinfo)
            })
            this.fenceGroup=arr
            // pickupInfo.pickupIdlist = []
            pickupInfo.fenceDrawlist=[]
        }
        this.exportDxf()
    }
    public ThreeDDrawConfirm=()=>{//3d绘制
        if(pickupInfo.fenceDrawlist.length){
            let arr:any=[]
            pickupInfo.fenceDrawlist.forEach(item=>{
                console.log(item,'item.area');
                let objinfo={
                    extremepoint:[],
                }
                item.extremepoint.forEach(ite=>{
                    objinfo.extremepoint.push([ite.x,ite.y])
                })
                pickupInfo.selectedID.push(Number(item.id))
                const newid = item.id- Number(pickupInfo.bluePrintCount)
                objinfo.id='A' + newid
                arr.push(objinfo)
            })
            this.fenceGroup=arr
            // pickupInfo.pickupIdlist = []
            pickupInfo.fenceDrawlist=[]  
        }
    }
    public ThreeDpickupConfirm=()=>{//3d拾取
        if(!pickupInfo.pickupIdlist.length) return
        let arr:any=[]
        let gparr:any=[]
        let trans= dxfview.GetViewer().transaction
        let gpwasm= dxfview.GetViewer().transaction.gpObj
        newAddLayer(gpwasm,pickupInfo.structureTypeName)
        pickupInfo.pickupIdlist.forEach(id=>{
            let {entityId,blockId}=splitBlockId(id)
            let newobj:any=null
            let objinfo={
                extremepoint:[]
            }
            if(blockId) {
                newobj=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
            }else {
                newobj=gpwasm.getPBaseObjById(id).copyNewOne()
            }
            newobj.setColor(new sg.SGColor(236, 128, 141,255))
            newobj.getRenderPoints(100)
            let {vertices} = getSgBufInf()
            let extremepoint:any = [];
            for (let i = 0; i < vertices.length; i += 2) {
                let x = vertices[i];
                let y = vertices[i + 1];
                extremepoint.push([x,y]);
            }
            let fenid=newobj.getDrawObjectId()
            pickupInfo.selectedID.push(Number(fenid))
            const newid =fenid - Number(pickupInfo.bluePrintCount)
            objinfo.id='A' + newid
            objinfo.extremepoint=extremepoint
            arr.push(objinfo)
            newobj.setLayer(pickupInfo.structureTypeName)
            gparr.push(newobj)
        })
        pickupInfo.pickupIdlist = []
        dxfview.GetViewer().selectionScene.clear()
        trans.add(gparr)
        this.fenceGroup=arr
    }
    

    public pickupCancel=()=>{//图元识别 拾取状态下 右键取消
        pickupInfo.pickupIdlist=[]
        dxfview.GetViewer().selectionScene.clear()
        dxfview.GetViewer().Render()
        // pickupInfo.slcurrentstate=SlScopeType.Empty
        this.sLcontrolObj.setState(SlScopeType.Empty)
    }
    public drawCancel=()=>{
        PubSub.default.pub('slidentifyclear')
        rightClickMenuObj.rightClickMenUpoP=false
        // pickupInfo.slcurrentstate=SlScopeType.Empty
        this.sLcontrolObj.setState(SlScopeType.Empty)
    }

    public textRecognition=()=>{
        let gpwasm= dxfview.GetViewer().transaction.gpObj
        let arr:any=[]
        pickupInfo.pickupIdlist.forEach(id=>{
            let int = gpwasm.getObjType(id)
            if(int==1) {
                let gp=gpwasm.getPBaseObjById(id)
                const type =gp.getObjType()
                if(type === 'SGObjText') {
                    let text=sg.SGObjTool.convertToText(gp)
                    let value=text.getText()
                    arr.push(value)
                }
                
            }

        })
        this.AItext=arr
    }
    public exportDxf=()=>{
       let arr=gpCountLayer(dxfview.GetViewer().transaction.gpObj)
       let lsarr=arr.filter(name=>!pickupInfo.baseMapLayers.includes(name))
       const vecint = new sg.vector_string
       lsarr.forEach(layname=>{
         vecint.push_back(layname)
       })
       let sgcolor= new sg.SGColor(245, 154, 35,255)
       let str=exportAppointDxfData(dxfview.GetViewer().transaction.gpObj,pickupInfo.bluePrintCount,sgcolor,vecint)
       this.layerInfo=str
       pickupInfo.exportDxfText=str
    }

}