import mapboxgl from 'mapbox-gl';
import { MyEvent } from './myevent';
import { Vector2 } from 'three';
import { tips } from './global';
import { MathLib } from './mathlib';
import { ButtonStatus } from './global';
mapboxgl.accessToken = 'pk.eyJ1IjoiamVmZnN0ZXJuIiwiYSI6IlAzRFFiN0EifQ.mNWvayrLEw9wULuq0sopyA'; // 替换为你的 token

const mapBoxToken = 'pk.eyJ1IjoiamVmZnN0ZXJuIiwiYSI6IlAzRFFiN0EifQ.mNWvayrLEw9wULuq0sopyA'

import Map from 'ol/Map'
import View from 'ol/View'
import TileLayer from 'ol/layer/Tile'
import OSM from 'ol/source/OSM'
import VectorSource from 'ol/source/Vector'
import { bbox as bboxStrategy } from 'ol/loadingstrategy'
import GeoJSON from 'ol/format/GeoJSON'
import VectorLayer from 'ol/layer/Vector'
import XYZ from 'ol/source/XYZ'
import * as proj from 'ol/proj'
// 引入需要的交互
import DragPan from 'ol/interaction/DragPan';
import MouseWheelZoom from 'ol/interaction/MouseWheelZoom';
import { getWorldPositionOnZPlane } from "dxf-viewer/src/controls/coordinateSystem";
import { Style, Fill, Stroke } from 'ol/style';

import proj4 from 'proj4'
import { register } from 'ol/proj/proj4'
import { get as getProjection } from 'ol/proj'

import BaseLayer from 'ol/layer/Base'

// 引入并定义 Gauss-Krüger 投影 EPSG:2345   EPSG:31467 （德国常用 Gauss-Krüger 第3带）
// proj4.defs('EPSG:2345', '+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=500000 +y_0=0 +ellps=krass +units=m +no_defs');


// // proj4.defs(
// //     'EPSG:31467', 
// //     '+proj=tmerc +lat_0=0 +lon_0=9 +k=1.0 +x_0=3500000 +y_0=0 +ellps=bessel +datum=potsdam +units=m +no_defs'
// // );
// register(proj4);
if (!proj4.defs('EPSG:2345')) {
    proj4.defs('EPSG:2345', '+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=500000 +y_0=0 +ellps=krass +units=m +no_defs');
    register(proj4);
}
const projection: any = getProjection('EPSG:2345')
console.log('Projection registered:', !!projection); // 应该输出 true


export class OlViewerSync extends MyEvent {//注册MyEvent用于绑定事件
    public mapContainer
    public map
    public cadApp
    public cadContainer
    public get cadview() {//
        return this.cadApp.canvas
    }
    public _onload
    public mapCenter

    public get screenCen(): Vector2 {
        const size = this.map.getSize(); // 返回 [width, height]
        return new Vector2(size[0] / 2, size[1] / 2);
    }

    public _cen//初始经纬度
    public _st: Vector2//
    public _en: Vector2
    public _drag: boolean = false
    // 图层
    // private _customLayers: BaseLayer[] = [];

    constructor(mapContainer, cadApp) {
        super()
        this.mapContainer = mapContainer
        this.cadApp = cadApp
        this.cadContainer = cadApp.domContainer
        this._st = new Vector2()
        this._en = new Vector2()
        this.initMap()
    }
    public override dispose() {
        // this._customLayers.forEach(layer => this.map.removeLayer(layer));
        this.freezeMap()
        this.map.remove()
    }

    public initMap() {
        this.mapCenter = [117.20861541288662, 31.836393226525608]
        let lscenter = proj.transform(this.mapCenter, 'EPSG:4326', 'EPSG:2345')
        const amapKey = '4afe573e31144e25dc0b93593e952d1d';
        this.map = new Map({
            target: this.mapContainer,
            layers: [
                new TileLayer({
                    source: new XYZ({
                        // url: `https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token=${mapBoxToken}`,
                        // attributions: '© <a href="https://www.mapbox.com/about/maps/">Mapbox</a> © <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
                        url: `https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}&key=${amapKey}`,
                        attributions: '© 高德地图',
                        tileSize: 256,
                        crossOrigin: 'anonymous',
                        tilePixelRatio: window.devicePixelRatio || 1
                    })
                }),
            ],
            view: new View({
                // center: proj.fromLonLat(this.mapCenter),
                // projection: 'EPSG:3857',
                zoom: 15,
                center: lscenter,
                projection: projection,
                constrainResolution:true,
                smoothResolutionConstraint: false
            }),
            interactions: [
                new DragPan(),
                new MouseWheelZoom(),
            ]
        })

    }
    public freezeCAD() {//冻结cad
        this.cadContainer.style.pointerEvents = 'none'
    }
    public freezeMap() {
        console.log(this.cadview)
        this.cadContainer.style.pointerEvents = 'all'
        this.cadview.removeEventListener('pointerdown', this._onmousedown)
        this.cadview.removeEventListener('pointerup', this._onmouseup)
        this.cadview.removeEventListener('pointermove', this._onmousemove)
        this.cadApp.removeEventListener('wheel', this._onwheel)
    }
    public syncView() {
        console.log(this.cadview)
        this.cadContainer.style.pointerEvents = 'all'
        this.cadview.addEventListener('pointerdown', this._onmousedown)
        this.cadview.addEventListener('pointerup', this._onmouseup)
        this.cadview.addEventListener('pointermove', this._onmousemove)
        this.cadApp.addEventListener('wheel', this._onwheel)
    }
    public loadShp(opt) {
        let { lng, lat, url, projId } = opt
        fetch(url)
            .then(res => res.json())
            .then(data => {
                console.log('Raw GeoJSON:', data);
                delete data.crs;
                console.log(data, 'datadatadatadata');

                const format = new GeoJSON({
                    dataProjection: 'EPSG:4326',     // 原始坐标系
                    featureProjection: 'EPSG:2345'   // 目标投影
                });

                const features = format.readFeatures(data);
                console.log('Parsed Features:', features); // 应该不再是空数组

                if (features.length === 0) {
                    console.warn('⚠️ No features were parsed!');
                    return;
                }

                const vectorLayer = new VectorLayer({
                    source: new VectorSource({
                        features: features
                    }),
                    style: function (feature) {
                        const id = feature.get('id');
                        let fill = '';
                        if (id >= 0 && id <= 100) {
                            fill = 'rgba(0, 0, 255, 0.5)';
                        } else if (id > 100 && id <= 400) {
                            fill = 'rgba(255, 0, 0, 0.5)';
                        } else if (id > 400) {
                            fill = 'rgba(0, 255, 0, 0.5)';
                        }
                        return new Style({
                            fill: new Fill({ color: fill }),
                            stroke: new Stroke({ color: '#885588', width: 2 })
                        });
                    }
                });

                // this._customLayers.push(vectorLayer);
                this.map.addLayer(vectorLayer);
            });
        // const vectorSource = new VectorSource({
        //     // format: new GeoJSON(),
        //     format: new GeoJSON({
        //         dataProjection: 'EPSG:4326',   // GeoJSON 原始坐标系统（WGS84）3857  4326
        //         featureProjection: 'EPSG:2345' // 地图视图使用的投影
        //     }),
        //     url: function(extent) {
        //         return url // 替换为你的GeoJSON地址
        //     },
        //     // strategy: bboxStrategy,
        // })
        // const vectorLayer = new VectorLayer({
        //     zIndex: 100,
        //     source: vectorSource,
        //     style: function(feature) {
        //         const id = feature.get('id');  // 假设GeoJSON中每个feature都有一个'id'字段

        //         let fill = '';
        //         if (id >= 0 && id <= 100) {
        //             fill = 'rgba(0, 0, 255, 0.5)'; // 蓝色填充
        //         } else if (id > 100 && id <= 400) {
        //             fill = 'rgba(255, 0, 0, 0.5)'; // 红色填充
        //         } else if (id > 400) {
        //             fill = 'rgba(0, 255, 0, 0.5)'; // 绿色填充
        //         }
        //         return new Style({
        //             fill: new Fill({
        //                 color: fill
        //             }),
        //             stroke: new Stroke({
        //                 // color: '#000000',
        //                 // width: 1.5
        //                 color: '#885588', // 使用你原本的紫色
        //                 width: 2,
        //             })

        //         });
        //     }
        // })
        // this.map.addLayer(vectorLayer);

    }
    public loadTiff(opt) {
        let { lng, lat, url, projId } = opt
        // 创建 XYZ 瓦片图层
        const tiffLayer = new TileLayer({
            source: new XYZ({
                url,
                tileSize: 256,
            }),
            opacity: 1.0, // 类似 raster-opacity
            visible: true,
        });
        this.map.addLayer(tiffLayer);
        // const centerInWebMercator = proj.transform([lng, lat], 'EPSG:4326', 'EPSG:2345')
        const centerInWebMercator = proj.transform([lng, lat], 'EPSG:4326', 'EPSG:2345')
        // 获取当前地图视图
        const view = this.map.getView();
        // 方法一：直接跳转
        view.setCenter(centerInWebMercator);

    }
    public override onMouseMove(e: MouseEvent): void {
        if (this._drag) {

            this._en = new Vector2(e.offsetX, e.offsetY)
            let delta = new Vector2().subVectors(this._st, this._en)
            let lnglat = this.getLngLat(delta)
            // this._cen=lnglat
            // this.map.jumpTo({
            //     center:lnglat
            // })
            const center = proj.fromLonLat(lnglat);
            this.map.getView().setCenter(center);
            this._st = this._en
        }
        tips.mouse = { x: e.offsetX, y: e.offsetY }
        // tips.lnglat=this.map.unproject(tips.mouse)
        // 获取鼠标当前位置的地理坐标（EPSG:4326）
        const coordinate = this.map.getCoordinateFromPixel([e.offsetX, e.offsetY]);
        tips.lnglat = {
            lng: proj.toLonLat(coordinate)[0],
            lat: proj.toLonLat(coordinate)[1]
        }

    }
    public override onMouseDown(e: MouseEvent): void {
        console.log(e)
        if (e.button == ButtonStatus.Middle) {
            this.panStart(e)
        }
    }
    public override onMouseUp(e: MouseEvent): void {
        console.log(e)
        this.panEnd(e)
    }

    public panStart(event: MouseEvent) {
        this._drag = true
        this._cen = this.getLngLat()
        this._st = new Vector2(event.offsetX, event.offsetY)
    }
    public panEnd(event: MouseEvent) {
        this._drag = false
    }
    public override onWheel(e: any): void {
        // //mapbox写法
        // let deltaScale=1/e.event['_deltaScale']
        // let zoom=MathLib.Log2(2**this.map.getZoom()*deltaScale)
        // tips.zoom=zoom
        // let cur=new Vector2(e.event.offsetX,e.event.offsetY)
        // let lnglat=this.map.unproject(cur)
        // let delt=new Vector2().subVectors(cur,this.screenCen)
        // this.map.jumpTo({zoom,center:lnglat})
        // let newpos=new Vector2().subVectors(this.screenCen,delt)
        // lnglat=this.map.unproject(newpos)
        // this.map.jumpTo({center:lnglat})

        // OpenLayers 写法
        const map = this.map; // OpenLayers 地图实例
        const view = map.getView();
        let deltaScale = 1 / e.event['_deltaScale']
        let zoom = MathLib.Log2(2 ** view.getZoom() * deltaScale)
        tips.zoom = zoom
        let cur = new Vector2(e.event.offsetX, e.event.offsetY)
        // 鼠标当前屏幕坐标（像素位置）
        const mousePixel = [cur.x, cur.y];
        // 将鼠标位置转为地图地理坐标（经纬度）
        const mouseCoordinate = map.getCoordinateFromPixel(mousePixel);
        let delt = new Vector2().subVectors(cur, this.screenCen)
        view.setCenter(mouseCoordinate);
        view.setZoom(zoom);
        let newpos = new Vector2().subVectors(this.screenCen, delt)
        const mousePixel2 = [newpos.x, newpos.y];
        const mouseCoordinate2 = map.getCoordinateFromPixel(mousePixel2);
        view.setCenter(mouseCoordinate2);
    }

    public onLoad() {
        this.lngGrid()
    }

    public getLngLat(delta = undefined) {
        let cen = this.screenCen.clone(); // 假设 screenCen 是一个 Vector2 类型
        if (delta) {
            cen.add(delta); // 加上偏移量
        }
        // 这里返回的是屏幕像素坐标对应的地图坐标（EPSG:3857）
        const coordinate = this.map.getCoordinateFromPixel([cen.x, cen.y]);
        // 如果你想得到 [lng, lat] 格式：
        const lngLat = proj.toLonLat(coordinate);

        return lngLat; // 返回 [lng, lat]
    }
    public lngGrid() {
        let cen = this.mapCenter
        const coordinates = [
            [180, 90],
            [180, -90]
        ];
        // 3. 在地图上添加线条
        this.map.on('load', function () {
            this.addSource('line', {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    geometry: {
                        type: 'LineString',
                        coordinates: coordinates,
                    },
                },
            });
            this.addLayer({
                id: 'line-layer',
                type: 'line',
                source: 'line',
                paint: {
                    'line-color': '#FF0000', // 线的颜色
                    'line-width': 4, // 线宽
                },
            });
            this.addSource('circle-source', {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: [{
                        type: 'Feature',
                        geometry: {
                            type: 'Point',
                            coordinates: cen  // 圆心坐标
                        }
                    }]
                }
            });
            this.addLayer({
                id: 'circle-layer',
                type: 'circle',
                source: 'circle-source',
                paint: {
                    'circle-radius': 5, // 圆的半径
                    'circle-color': '#FF0000', // 圆的颜色
                    'circle-opacity': 0.6 // 圆的透明度
                }
            });
        })

    }
    public cadMoveView(position, scale) {
        this.cadApp.olsetCadView(position, scale)
    }
    public getCadScale() {
        let scale = this.cadApp.camera.zoom
        return scale
    }
    public getCadCenter() {
        const centerX = this.cadApp.canvasWidth / 2;
        const centerY = this.cadApp.canvasHeight / 2;
        let center = getWorldPositionOnZPlane({ x: centerX, y: centerY }, this.cadApp.camera, this.cadApp.canvas)
        return center
    }
}