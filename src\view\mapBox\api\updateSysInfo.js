import instance from '@/http/index'

// 获取API基础URL，从axios.js导入
import { API_URLS } from '../utils/axios.js';

/* 
* @description 添加-修改项目关联的属性
* @param {object} data - 请求参数
* @param {string} data.projectCode - 项目code
* @param {string} [data.id] - 属性id
* @param {Array<object>} [data.attributeList] - 项目属性
* @param {string} data.attributeList[].attributeKey - 属性key
* @param {string} data.attributeList[].attributeValue - 属性value
*/
export function apiUpdateProjectAttribute(data) {
  return instance({
    url: API_URLS.VITE_BASE_MAP_BOX_URL + "/projectAttribute/add",
    method: 'POST',
    data: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * @description 删除项目关联的属性
 * @param {string} projectCode - 项目code
 * @param {string[]} keys - 要删除的属性id数组
 */
export function apiDeleteProjectAttribute(projectCode, keys) {
  return instance({
    url: `${API_URLS.VITE_BASE_MAP_BOX_URL}/projectAttribute/delete/${projectCode}`,
    method: 'DELETE',
    data: keys,
  });
}

/**
 * @description 查询项目关联的属性列表
 * @param {string} projectCode - 项目code
 */
export function apiGetProjectAttributes(projectCode) {
  return instance({
    url: `${API_URLS.VITE_BASE_MAP_BOX_URL}/projectAttribute/list/${projectCode}`,
    method: 'GET',
  });
}
