
import ExcelJS from 'exceljs'
import { saveAs } from "file-saver"; // 用于保存压缩后的文件

class ExcelGenerator {
  constructor() {
    this.workbook = new ExcelJS.Workbook();
    this.sheet = this.workbook.addWorksheet('工程量清单');
    this.mergeList = [];
    this.boldText = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  }

  setColumnWidths() {
    this.sheet.columns = [
      { width: 10 },
      { width: 30 },
      { width: 30 },
      { width: 40 },
      { width: 15 },
      { width: 15 }
      { width: 30 }
    ];
  }

  setBorder(cell) {
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
  }
  /**
   * 设置当前工作表所有单元格的背景色
   * @param {string} color - 颜色代码，如 'FFFF00'（黄色）
   */
  setBgColor(color) {
    this.sheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: color }
        };
      });
    });
  }

  addTitle() {
    this.sheet.mergeCells('A1:G1');
    const cell = this.sheet.getCell('A1');
    cell.value = '光伏区安装分部分项工程量清单';
    cell.alignment = { vertical: 'middle', horizontal: 'center' };
    cell.font = { bold: true, size: 14 };
    this.setBgColor('9e9f9f');
  }

  addHeaderRow() {
    const headerRow = this.sheet.addRow(['序号', '项目名称', '型号规格', '工程量计算规则', '数量', '计量单位', '形象进度']);
    headerRow.height = 30;
    headerRow.eachCell((cell) => {
      cell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
      cell.font = { bold: true };
      this.setBorder(cell);
    });
  }

  addContentRows(rows) {
    rows.forEach((rowData, index) => {
      const row = this.sheet.addRow(rowData);
      const isBoldRow = this.boldText.includes(rowData[0]);
      const excelRowIndex = index + 3; // offset: title + header
      if (isBoldRow) {
        this.mergeList.push(`B${excelRowIndex}:G${excelRowIndex}`);
      }

      row.eachCell((cell) => {
        cell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
        if (isBoldRow) {
          cell.font = { bold: true };
          cell.alignment = { vertical: 'middle', wrapText: true };
        }
        this.setBorder(cell);
      });
    });

    this.mergeList.forEach(range => {
      this.sheet.mergeCells(range);
    });
  }

  async export(filename = '光伏区工程量清单.xlsx') {
    const buffer = await this.workbook.xlsx.writeBuffer();
    saveAs(new Blob([buffer]), filename);
  }

  async generate(rows) {
    this.setColumnWidths();
    this.addTitle();
    this.addHeaderRow();
    this.addContentRows(rows);
    await this.export();
  }
}

export default ExcelGenerator;