
import ExcelJS from 'exceljs'
import { saveAs } from "file-saver"

/**
 * Excel生成器类 - 用于生成光伏工程量清单Excel文件
 * @class ExcelGenerator
 */
class ExcelGenerator {
  // 常量定义
  static CONSTANTS = {
    WORKSHEET_NAME: '工程量清单',
    DEFAULT_FILENAME: '光伏区工程量清单.xlsx',
    TITLE: '光伏区安装分部分项工程量清单',
    HEADER_HEIGHT: 30,
    TITLE_FONT_SIZE: 14,
    TITLE_BG_COLOR: '9e9f9f',
    BORDER_STYLE: 'thin',
    TITLE_ROW_INDEX: 1,
    HEADER_ROW_INDEX: 2,
    CONTENT_START_ROW: 3,
    COLUMN_WIDTHS: [10, 30, 30, 40, 15, 15, 30],
    HEADERS: ['序号', '项目名称', '型号规格', '工程量计算规则', '数量', '计量单位', '形象进度'],
    BOLD_INDICATORS: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
  }

  constructor(options = {}) {
    try {
      this.workbook = new ExcelJS.Workbook()
      this.sheet = this.workbook.addWorksheet(
        options.worksheetName || ExcelGenerator.CONSTANTS.WORKSHEET_NAME
      )
      this.mergeList = []
      this.options = {
        title: options.title || ExcelGenerator.CONSTANTS.TITLE,
        headers: options.headers || ExcelGenerator.CONSTANTS.HEADERS,
        columnWidths: options.columnWidths || ExcelGenerator.CONSTANTS.COLUMN_WIDTHS,
        ...options
      }
    } catch (error) {
      throw new Error(`Excel生成器初始化失败: ${error.message}`)
    }
  }

  /**
   * 设置列宽
   * @private
   */
  setColumnWidths() {
    try {
      this.sheet.columns = this.options.columnWidths.map(width => ({ width }))
    } catch (error) {
      console.warn('设置列宽失败:', error.message)
      // 使用默认列宽
      this.sheet.columns = ExcelGenerator.CONSTANTS.COLUMN_WIDTHS.map(width => ({ width }))
    }
  }

  /**
   * 设置单元格边框
   * @private
   * @param {Object} cell - Excel单元格对象
   */
  setBorder(cell) {
    if (!cell) return

    cell.border = {
      top: { style: ExcelGenerator.CONSTANTS.BORDER_STYLE },
      left: { style: ExcelGenerator.CONSTANTS.BORDER_STYLE },
      bottom: { style: ExcelGenerator.CONSTANTS.BORDER_STYLE },
      right: { style: ExcelGenerator.CONSTANTS.BORDER_STYLE }
    }
  }

  /**
   * 设置单元格背景色
   * @private
   * @param {Object} cell - Excel单元格对象
   * @param {string} color - 颜色代码
   */
  setCellBackground(cell, color) {
    if (!cell || !color) return

    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: color }
    }
  }

  /**
   * 设置单元格通用样式
   * @private
   * @param {Object} cell - Excel单元格对象
   * @param {Object} options - 样式选项
   */
  setCellStyle(cell, options = {}) {
    if (!cell) return

    const {
      bold = false,
      fontSize = 11,
      alignment = { vertical: 'middle', horizontal: 'center', wrapText: true },
      backgroundColor = null
    } = options

    cell.alignment = alignment
    cell.font = { bold, size: fontSize }

    if (backgroundColor) {
      this.setCellBackground(cell, backgroundColor)
    }

    this.setBorder(cell)
  }

  /**
   * 添加标题行
   * @private
   */
  addTitle() {
    try {
      const titleRange = `A${ExcelGenerator.CONSTANTS.TITLE_ROW_INDEX}:G${ExcelGenerator.CONSTANTS.TITLE_ROW_INDEX}`
      this.sheet.mergeCells(titleRange)

      const titleCell = this.sheet.getCell(`A${ExcelGenerator.CONSTANTS.TITLE_ROW_INDEX}`)
      titleCell.value = this.options.title

      this.setCellStyle(titleCell, {
        bold: true,
        fontSize: ExcelGenerator.CONSTANTS.TITLE_FONT_SIZE,
        backgroundColor: ExcelGenerator.CONSTANTS.TITLE_BG_COLOR
      })
    } catch (error) {
      throw new Error(`添加标题失败: ${error.message}`)
    }
  }

  /**
   * 添加表头行
   * @private
   */
  addHeaderRow() {
    try {
      const headerRow = this.sheet.addRow(this.options.headers)
      headerRow.height = ExcelGenerator.CONSTANTS.HEADER_HEIGHT

      headerRow.eachCell((cell) => {
        this.setCellStyle(cell, { bold: true })
      })
    } catch (error) {
      throw new Error(`添加表头失败: ${error.message}`)
    }
  }

  /**
   * 检查是否为粗体行
   * @private
   * @param {any} indicator - 行标识符
   * @returns {boolean}
   */
  isBoldRow(indicator) {
    return ExcelGenerator.CONSTANTS.BOLD_INDICATORS.includes(indicator)
  }

  /**
   * 添加内容行
   * @private
   * @param {Array} rows - 数据行数组
   */
  addContentRows(rows) {
    if (!Array.isArray(rows) || rows.length === 0) {
      console.warn('没有数据行需要添加')
      return
    }

    try {
      rows.forEach((rowData, index) => {
        if (!Array.isArray(rowData)) {
          console.warn(`第${index + 1}行数据格式错误，跳过`)
          return
        }

        const row = this.sheet.addRow(rowData)
        const isBold = this.isBoldRow(rowData[0])
        const excelRowIndex = index + ExcelGenerator.CONSTANTS.CONTENT_START_ROW

        // 如果是粗体行，记录需要合并的单元格范围
        if (isBold) {
          this.mergeList.push(`B${excelRowIndex}:G${excelRowIndex}`)
        }

        // 设置行样式
        row.eachCell((cell) => {
          this.setCellStyle(cell, { bold: isBold })
        })
      })

      // 批量合并单元格
      this.mergeList.forEach(range => {
        try {
          this.sheet.mergeCells(range)
        } catch (error) {
          console.warn(`合并单元格失败 ${range}:`, error.message)
        }
      })
    } catch (error) {
      throw new Error(`添加内容行失败: ${error.message}`)
    }
  }

  /**
   * 导出Excel文件
   * @param {string} filename - 文件名
   * @returns {Promise<void>}
   */
  async export(filename = ExcelGenerator.CONSTANTS.DEFAULT_FILENAME) {
    try {
      if (!filename || typeof filename !== 'string') {
        filename = ExcelGenerator.CONSTANTS.DEFAULT_FILENAME
      }

      const buffer = await this.workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      saveAs(blob, filename)
    } catch (error) {
      throw new Error(`导出Excel文件失败: ${error.message}`)
    }
  }

  /**
   * 生成完整的Excel文件
   * @param {Array} rows - 数据行数组
   * @param {string} filename - 可选的文件名
   * @returns {Promise<void>}
   */
  async generate(rows, filename) {
    try {
      // 参数验证
      if (!Array.isArray(rows)) {
        throw new Error('数据行必须是数组格式')
      }

      // 按顺序执行生成步骤
      this.setColumnWidths()
      this.addTitle()
      this.addHeaderRow()
      this.addContentRows(rows)

      await this.export(filename)
    } catch (error) {
      throw new Error(`生成Excel文件失败: ${error.message}`)
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    try {
      this.mergeList = []
      this.workbook = null
      this.sheet = null
    } catch (error) {
      console.warn('清理资源时出现错误:', error.message)
    }
  }

  /**
   * 验证数据格式
   * @param {Array} rows - 数据行数组
   * @returns {boolean} 验证结果
   */
  static validateData(rows) {
    if (!Array.isArray(rows)) {
      return false
    }

    return rows.every((row, index) => {
      if (!Array.isArray(row)) {
        console.warn(`第${index + 1}行不是数组格式`)
        return false
      }

      if (row.length !== ExcelGenerator.CONSTANTS.HEADERS.length) {
        console.warn(`第${index + 1}行列数不匹配，期望${ExcelGenerator.CONSTANTS.HEADERS.length}列，实际${row.length}列`)
        return false
      }

      return true
    })
  }
}

/**
 * 创建Excel生成器的工厂函数
 * @param {Object} options - 配置选项
 * @returns {ExcelGenerator} Excel生成器实例
 */
export function createExcelGenerator(options = {}) {
  return new ExcelGenerator(options)
}

/**
 * 快速生成Excel文件的便捷函数
 * @param {Array} rows - 数据行数组
 * @param {Object} options - 配置选项
 * @returns {Promise<void>}
 */
export async function generateExcel(rows, options = {}) {
  // 数据验证
  if (!ExcelGenerator.validateData(rows)) {
    throw new Error('数据格式验证失败')
  }

  const generator = createExcelGenerator(options)

  try {
    await generator.generate(rows, options.filename)
  } finally {
    // 确保资源被清理
    generator.dispose()
  }
}

export default ExcelGenerator