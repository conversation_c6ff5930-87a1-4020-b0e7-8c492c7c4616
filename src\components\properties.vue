<template>
    <div class="entityProperties">
      <canvas ref="canvas" id="canvass" width="600px" height="600px"  class="mycanvas"></canvas>
    </div>
  </template>
  
  <script setup>
  // import CanvasFn from '../utils/canvasFn'
  import {ref,onMounted} from 'vue'
  let canvas=ref(null)
  let canvasDom=null
  let ctx=null
  onMounted(() => {
    // console.log(canvas.value.width);
    canvasDom=canvas.value
    ctx=canvasDom.getContext('2d')
    ctx.translate(canvasDom.width/2,canvasDom.height/2)
    ctx.scale(1,-1)
    // draw()
  })
  
  // const draw=()=>{
  //   drawLine(ctx,[-canvasDom.width / 2,0],[canvasDom.width / 2,0],'#333')
  //   drawLine(ctx,[0,-canvasDom.height / 2],[0,canvasDom.height / 2],'#333')
  // }
  
  
  </script>
  
  <style lang="scss" scoped>
  .entityProperties {
    position:absolute;
    width: 200px ;
    right: 0px ;
    border: 1px solid blue;
  }
  </style>
  
  