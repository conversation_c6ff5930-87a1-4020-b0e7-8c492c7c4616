<template>
  <div>
    <t-card class="list-card-container card" :bordered="false">
      <t-form
        ref="formRef"
        :data="searchData"
        :label-width="80"
        layout="inline"
      >
        <div class="project-name-wrap">
          <t-form-item label="项目名称" name="projectName">
            <t-input
              v-model="searchData.projectName"
              class="form-item-content"
              type="search"
              :style="{ width: '200px' }"
            />
          </t-form-item>
          <div class="block-wrap">
            <div class="block-left">
              <el-button
                @click="searchFetchData()"
                size="default"
                color="#ff923f"
                style="color: #fff; margin-right: 10px"
                >搜索</el-button
              >
              <el-button size="default" color="#e8e8e8" @click="onReset()"
                >清空</el-button
              >
            </div>
            <div class="block-right">
              <el-button
                color="#ff923f"
                size="default"
                style="color: #fff"
                @click="onAddOrEdit"
                >新建项目</el-button
              >
            </div>
          </div>
        </div>
      </t-form>
    </t-card>

    <t-card :bordered="false" class="card">
      <template v-if="pagination.total > 0 && !dataLoading">
        <div class="list-card-items">
          <t-row class="img-wrap">
            <t-col v-for="item in imgList as Array<any>" :key="item.code">
              <t-image
                :src="item && item.thumbnailUrl ? item.thumbnailUrl : errorImg"
                :lazy="true"
                class="img-item"
                @click="goDetail(item)"
              >
              </t-image>
              <div class="img-txt">
                <t-tag
                  shape="mark"
                  theme="warning"
                  style="
                    border-radius: 3px;
                    background: transparent;
                    color: #fff;
                  "
                >
                  {{ item.name }}
                </t-tag>
              </div>
            </t-col>
          </t-row>
        </div>

        <div class="list-card-pagination">
          <t-pagination
            v-model="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            @page-size-change="onPageSizeChange"
            @current-change="onCurrentChange"
          />
        </div>
      </template>

      <div v-else-if="dataLoading" class="list-card-loading">
        <t-loading size="large" text="加载数据中..." />
      </div>
    </t-card>

    <AddDialog ref="AddDialogRef" @refresh="searchFetchData()"></AddDialog>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";

// @ts-ignore
import errorImg from "../images/error.jpeg";
import { useProjectStore } from "../store";
import AddDialog from "./addProjectDialog.vue";
import { apiGetProjectList, apiUpdateProjectFile } from "../api/listPageApi";
import { MessagePlugin } from "tdesign-vue-next";
const AddDialogRef = ref();
const usePeoject = useProjectStore();
const router = useRouter();

const formRef = ref(null);
const searchData = ref({
  projectName: "",
  page: 1,
  pageSize: 10,
});
const pagination = ref({ current: 1, pageSize: 10, total: 0 });

const imgList = ref([]);
const dataLoading = ref(true);

const onPageSizeChange = (size) => {
  pagination.value.pageSize = size;
  pagination.value.current = 1;
  searchFetchData();
};
const onCurrentChange = (current) => {
  pagination.value.current = current;
  searchFetchData();
};

// 项目详情
const goDetail = (row) => {
  // 设置项目基础信息
  usePeoject.setProjectInfo({
    projectCode: row.code,
    projectName: row.name,
    projectCity: row.city,
    cadUrl: row.cadUrl || undefined,
    tiffUrl: row.tiffUrl || undefined,
    recognitionStatus: row.recognitionStatus || undefined,
  });

  // 检查项目状态
  if (!row.cadUrl || !row.tiffUrl) {
    // 当CAD图纸或TIFF图像不存在时，打开AddDialog弹窗进行上传
    AddDialogRef.value.openDialog({
      isEdit: true,
      code: row.code,
      name: row.name,
      city: row.city|| null,
      type: row.type,
      address: row.address || "",
      mark: row.mark || "",
      latitude: row.latitude || null,
      longitude: row.longitude || null,
      cadUrl: row.cadUrl || "",
      tiffUrl: row.tiffUrl || "",
    });

    // 显示提示消息
    if (!row.cadUrl && !row.tiffUrl) {
      MessagePlugin.warning({
        content: `项目${row.name}的CAD图纸和TIFF图像尚未上传，请上传`,
        duration: 2000,
      });
    } else if (!row.tiffUrl) {
      MessagePlugin.warning({
        content: `项目${row.name}的TIFF图像尚未上传，请上传`,
        duration: 2000,
      });
    } else if (!row.cadUrl) {
      MessagePlugin.warning({
        content: `项目${row.name}的CAD图纸尚未上传，请上传`,
        duration: 2000,
      });
    }
    return;
  }

  if (row.recognitionStatus !== usePeoject.statusEnum.SUCCESS) {
    if (row.recognitionStatus === usePeoject.statusEnum.WORKING) {
      MessagePlugin.warning({
        content: `项目${row.name}的AI图像识别正在处理中，请稍后再试`,
        duration: 2000,
      });
    } else if (row.recognitionStatus === usePeoject.statusEnum.FAILED) {
      MessagePlugin.error({
        content: `项目${row.name}的AI图像识别失败，请重新处理`,
        duration: 2000,
      });
    } else {
      MessagePlugin.warning({
        content: `项目${row.name}的AI图像识别尚未处理完成，请稍后再试`,
        duration: 2000,
      });
    }
    return;
  }

  // 所有条件满足，跳转到详情页
  MessagePlugin.success({ content: `已切换至${row.name}`, duration: 2000 });
  router.push({
    path: "/viewMapBoxDetail",
    query: { id: row.code },
  });
};

// 更新项目文件信息
const updateProjectFile = async (projectId, fileType, filePath) => {
  try {
    const updateData: any = {};
    if (fileType === "cad") {
      updateData.cadUrl = filePath;
    } else if (fileType === "tiff") {
      updateData.tiffUrl = filePath;
    }

    if (Object.keys(updateData).length > 0) {
      const res = await apiUpdateProjectFile(projectId, updateData);
      if (res.api_status && res.code === 200) {
        MessagePlugin.success({
          content: `${fileType.toUpperCase()}文件信息已更新`,
          duration: 2000,
        });
        // 刷新项目列表
        searchFetchData();
        return true;
      } else {
        MessagePlugin.error({
          content: `更新${fileType.toUpperCase()}文件信息失败`,
          duration: 2000,
        });
      }
    }
  } catch (error) {
    MessagePlugin.error({
      content: `更新项目文件信息时发生错误`,
      duration: 2000,
    });
    console.error("更新项目文件信息错误:", error);
  }
  return false;
};
// 清空搜索
const onReset = () => {
  if (formRef.value) {
    // @ts-ignore
    formRef.value.reset();
  }
  searchFetchData();
};

const onAddOrEdit = () => {
  // 弹窗展示
  AddDialogRef.value.openDialog();
};

const searchFetchData = () => {
  const params = {
    pageNum: pagination.value.current,
    pageSize: pagination.value.pageSize,
    projectName: searchData.value.projectName,
  };
  dataLoading.value = false;

  // 查询项目列表
  apiGetProjectList(params)
    .then((res) => {
      if (res.data && res.data.list) {
        imgList.value = res.data.list;
        pagination.value.total = Number(res.data.total || 0); // 总条数
      } else {
        imgList.value = [];
        pagination.value = { current: 1, pageSize: 10, total: 0 };
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

onMounted(() => {
  searchFetchData();
});

// 暴露方法
defineExpose({
  searchFetchData,
  updateProjectFile,
});
</script>

<style lang="scss" scoped>
.list-card {
  height: 100%;

  &-operation {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--td-comp-margin-xxl);

    .search-input {
      width: 360px;
    }
  }

  &-item {
    padding: 0;

    :deep(.t-card__header) {
      padding: 0;
    }

    :deep(.t-card__body) {
      padding: 0;
      // margin-top: var(--td-comp-margin-xxl);
      // margin-bottom: var(--td-comp-margin-xxl);
    }

    :deep(.t-card__footer) {
      padding: 0;
    }
  }

  &-pagination {
    padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingTB-xl);
  }

  &-loading {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.list-card-container {
  margin-bottom: 10px;
  .project-name-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .block-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      .block-left {
        display: flex;
        align-items: center;
      }
      .block-right {
        display: flex;
        align-items: center;
      }
    }
  }
}

.list-card-items {
  .img-item {
    width: calc(calc(100vw / 5) - 34px);
    height: calc(100vh / 4);
    border-radius: 8px;
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      box-shadow: 10px 10px 15px rgba(0, 0, 0, 0.3);
      transform: scale(1.02);
      transform-origin: center center;
    }
  }

  .img-txt {
    position: absolute;
    right: 0px;
    left: 0px;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    width: 100%;
    overflow: hidden;
    z-index: 1;
  }
}
.img-wrap {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
}
:deep(.t-card__body) {
  padding: 16px;
}
:deep(.t-pagination__number.t-is-current) {
  background-color: #ff923f;
  color: #fff;
  border: solid 1px #ff923f;
}
:deep(.t-pagination__number:hover) {
  color: #ff923f;
  border: solid 1px #ff923f;
}
</style>
