<template>
  <div class="data-management">
    <Header>
      <template #controlButton>
        <div class="tab-panel-wrap">
          <div class="tab-panel-wrap-left">
            <div class="tab-panel-wrap-left-item">
              <el-icon :size="20" color="#ff923f"><House /></el-icon>
            </div>
          </div>
          <div class="tab-panel-wrap-right">
            <div class="tab-panel-wrap-right-item" v-show="false">
              <!-- 隐藏--TODO: 更新项目名称 -->
              <span>项目名称：</span>
              <span>{{ projectName }}</span>
            </div>
          </div>
        </div>
      </template>
    </Header>
    <main class="content-wrap">
      <DataList ref="dataListRef" />
    </main>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore
import { ref, onMounted, onBeforeUnmount } from "vue";
import { House } from "@element-plus/icons-vue";
// @ts-ignore
import Header from "@/view/mapBox/components/mapBoxHeader.vue";
import DataList from "../components/dataList.vue";
import { mapBoxConfig } from "../config/index";

const projectName = ref("111");
const dataListRef = ref(null);
let pollingTimer: NodeJS.Timeout | null = null;

// 开始轮询
const startPolling = () => {
  // 清除存在的定时器
  stopPolling();

  // 创建新的定时器
  pollingTimer = setInterval(() => {
    if (dataListRef.value) {
      dataListRef.value.searchFetchData();
    }
  }, mapBoxConfig.pollingTime) as NodeJS.Timeout;
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
  }
};

onMounted(() => {
  startPolling();
});

onBeforeUnmount(() => {
  stopPolling();
});
</script>

<style scoped lang="scss">
.data-management {
  display: flex;
  flex-direction: column;
  height: 100%;
  .tab-panel-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 36px;
    padding: 0 20px;
    background-color: #f3f3f3;
    .tab-panel-wrap-left {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 36px;
      line-height: 36px;
      .tab-panel-wrap-left-item {
        padding: 0 16px;
        height: 36px;
        line-height: 36px;
        background-color: #fff;
        border: solid 1px #e8e8e8;
        cursor: pointer;
        &:active {
          background-color: #e0e0e0;
        }
      }
    }
    .tab-panel-wrap-right {
      display: flex;
      align-items: center;
      justify-content: center;
      .tab-panel-wrap-right-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        color: #000;
        .tab-panel-wrap-right-item-text {
          margin-left: 10px;
        }
      }
    }
  }

  .content-wrap {
    flex: 1;
    padding: 20px;
    background-color: #eee;
    overflow-x: hidden;
    overflow-y: scroll;
  }
}
</style>
