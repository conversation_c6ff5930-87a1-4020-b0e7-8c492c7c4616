<template>
    <div class="attributeBox">
        <div class="title">
            <div>特性</div>
            <div @click="btnClose" style="cursor: pointer;">
                <el-icon :size="20"><Close /></el-icon>
            </div>
        </div>
        <div>
            <el-select v-model="ty" placeholder="类型"  style="width: 80px;margin-right: 10px" >
              <el-option
                v-for="item in options"
                :key="item.name"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select v-model="currentTy" placeholder="类型" value-key="name" style="width: 260px">
              <el-option
                v-for="item in lineTypelist"
                :label="item.name +`(${item.cunt})`"
                :value="item"
              >
              </el-option>
            </el-select>
        </div>
        <div class="box scroll">
            <el-collapse v-model="activeNames" >
                <el-collapse-item v-for="(section, sectionIndex) in sections" :key="sectionIndex" :title="section.title" :name="sectionIndex.toString()"
                >
                    <div class="property-list">
                        <div v-for="(item, index) in section.properties" :key="index" style="display: flex;">

                            <div class="label">{{ item.label }}</div>
                            <div v-if="item.type==1" class="value">{{ item.value }}</div>
                            <div v-else class="value">
                                <el-select v-model="item.value" style="width: 250px;margin-right: 10px" >
                                    <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                        </div>
                    </div>
                </el-collapse-item>
            </el-collapse>
            <el-collapse>
                <el-collapse-item  title="XData">
                    <div class="property-list">
                        <div v-for="(item, index) in xData" :key="index" style="display: flex;">
                            <div class="label">{{ item.label }}</div>
                            <div  class="value">{{ item.value }}</div>
                        </div>
                        <div @click="btnXDataPop">新增</div>
                    </div>
                </el-collapse-item>
            </el-collapse>
        </div>
    </div>
    <XDataPop ref="refXDataPop"></XDataPop>
  </template>
  
  <script setup>
  import { PubSub } from 'emitter';
  import { mitts, mittEvents } from 'sgdraw/mittBus'
  import {Close} from '@element-plus/icons-vue' 
  import {sections,xData,attributeBarFalg,lineTypelist,currentTy} from 'dxf-viewer'
  import XDataPop from './popFrame/XDataPop.vue'

  const options=ref([
    {label:'全部',value:'all'},
    {label:'CAD图形',value:'cad'},
    {label:'批注图形',value:'annotations'},
  ])
  const ty=ref('all')
  const ty2=ref('')
  const selectTy=ref([])
  const refXDataPop=ref(null)
  const activeNames=ref(['0', '1', '2'])
  // const sections=ref([
  //       {
  //         title: '基础',
  //         properties: [
  //           { label: '类型', value: 'CAD' ,type:'1'},
  //           { label: '名称', value: '多段线',type:'1' },
  //           { label: '颜色', value: '随层',type:'2' },
  //           { label: '图层', value: '图框',type:'2'  },
  //           { label: '线型', value: 'ByLayer',type:'2'  },
  //           { label: '线型比例', value: '6',type:'1'  },
  //           { label: '渲染顺序', value: '4287',type:'1'  },
  //           { label: '可见', value: '是',type:'2'  },
  //           { label: '线宽', value: '随层' ,type:'2' },
  //           { label: '法向坐标', value: '(0,0,1)' },
  //           { label: '其他信息', value: '(id:11473280,handle:13bb)' }
  //         ]
  //       },
  //       {
  //         title: '曲线参数',
  //         properties: [
  //           { label: '面积', value: '291893.079' },
  //           { label: '长度', value: '2193.828' }
  //         ]
  //       },
  //       {
  //         title: '几何图形',
  //         properties: [
  //           { label: '顶点', value: '1',type:'2' },
  //           { label: '顶点X', value: '1000' ,type:'1'},
  //           { label: '顶点Y', value: '1000' ,type:'1'},
  //           { label: '顶点Z', value: '0' ,type:'1'},
  //           { label: '顶点Z', value: '0' ,type:'2'}
  //         ]
  //       }
  // ])



  const btnClose=()=>{
    attributeBarFalg.value=false
  }
  const btnXDataPop=()=>{
    refXDataPop.value.open()
  }

  </script>
  
  <style lang="scss" scoped>
  .attributeBox {
    flex: 0 0 auto;
    background-color: #3a4352;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap-reverse;
  
    border-top: 1px solid #4f4f4f;
    
    .title {
        color: #fff;
        background-color: #212832;
        width: 100%;
        font-size: 16px;
        height: 48px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
  

    .box {
        overflow-y: auto;
        // height: 80%;
        height: calc(100% - 100px);
        el-collapse {
            color: #fff;
        }
        :deep(.el-collapse-item){
            background-color: #3a4352 !important;
            color: #fff;
            .el-collapse-item__header {
                background-color: #212832 !important;
                color: #fff;
                // border-bottom:none;
            }
            .el-collapse-item__wrap {
                background-color: #3a4352 !important; 
                color: #fff;
                border-bottom:none;
            }
        }
        .property-list {
            color: #fff;
        }
        .label {
            width: 100px;
            border: 1.5px solid black;
        }
        .value {
            width: 100%;
            border: 1.5px solid black;
        }
    }
  }
  </style>