import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"

export class Polyline extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    // public color:string='white'
    public els:any=[]
    public draw:boolean=true

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        console.log(this.color,'this.color');
        
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init()
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        while (this.draw) {
            let p1=await this.lengthPick({
                tip:'指定下一个点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
        }
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    public override onRButtonDown( event:PointerEvent ) : boolean{
        this.draw=false
        this.complete()        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
    }
    
    complete(){
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        if(this.pnts.length<2) return
        const wasmVPlist=this.setWasmVPlist(this.pnts)
        let wasmBulge = new sg.vector_double()
        let  pl=new sg.PolyLine2d(wasmVPlist, wasmBulge, false)
        let  plobj=new sg.SGObjComBinCurve()
        plobj.setpCurve(pl)
        let [r,g,b]=this.color
        plobj.setColor(new sg.SGColor(r,g,b,255))
        plobj.setLineType(this.lineType)
        this.els.push(plobj)
        this.viewer.transaction.add(this.els)
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
    }
    private setWasmVPlist(vList){
        let {x,y}=globalDxf.wcsOff
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x+x, item.y+y))
        })
        return wasmVPlist
    }
}


// import * as THREE from "three"
// import {ElementStyle} from './ElementStyle'

// export class Polyline2 extends ElementStyle {
//     public vPlist:THREE.Vector3[]=[]
//     public Polyline:any
//     public count:number=0
//     public iscomplete:boolean=false

//     // public wasmVPlist:any
//     public wasmBulge:any
//     public gp:any

//     constructor() {
//         // 调用父类的构造函数以初始化继承的属性
//         super();
//         // 定义线的两个端点
//         const geometry = new THREE.BufferGeometry().setFromPoints([]);
//         // 创建线的材质
//         const material = new THREE.LineBasicMaterial({ color:this.color});
//         // 使用几何体和材质创建线
//         this.Polyline = new THREE.Line(geometry, material);

//         // this.wasmVPlist= new sg.vector_Point2d()
//         this.wasmBulge = new sg.vector_double()
//         // this.polyline=new sg.PolyLine2d(this.wasmVPlist, this.wasmBulge, false)
//     }
//     setV(v:THREE.Vector3){
//         this.count++
//         this.vPlist.push(v)
//         return true
//     }
//     getmove(v:THREE.Vector3){
//         if(!this.vPlist.length) return
//         this.Polyline.geometry=new THREE.BufferGeometry().setFromPoints([...this.vPlist,v]);
//         this.Polyline.material= new THREE.LineBasicMaterial({ color:this.color});
//     }
    
//     complete(){
//         if(this.vPlist.length<2) return
//         this.iscomplete=true
//         this.Polyline.geometry=new THREE.BufferGeometry().setFromPoints(this.vPlist);
//         this.Polyline.material= new THREE.LineBasicMaterial({ color:this.color});

//         const wasmVPlist=this.setWasmVPlist(this.vPlist)
//         let  pl=new sg.PolyLine2d(wasmVPlist, this.wasmBulge, false)
//         this.gp=new sg.SGObjComBinCurve()
//         this.gp.setpCurve(pl)
//     }
//     getisitCompleted(){
//         return this.iscomplete
//     }

//     private setWasmVPlist(vList){
//         const wasmVPlist= new sg.vector_Point2d()
//         vList.forEach(item=>{
//             wasmVPlist.push_back(new sg.Point2d(item.x, item.y))
//         })
//         return wasmVPlist
//     }
// }

