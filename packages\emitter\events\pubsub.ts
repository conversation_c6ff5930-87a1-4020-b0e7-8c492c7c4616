export interface IDisposable {
    dispose(): void;
}
export interface PubSubEventMap {
    executeCommand: (commandName: any) => void;
    test:()=>void;
    connectToCanvasDom:(canvasElement:HTMLCanvasElement)=>void;
    modelToImage: () => void;
    modelToAttribute:()=> void;
    // wasm:()=>void;
    wasmundo:()=>void;
    wasmredo:()=>void;
    fitview:()=>void;
    sl_measure_scale:(l:string | number)=>void;
    showlayer:(layer:any)=>void
    freezelayer:(layer:any)=>void
    locklayer:(layer:any)=>void
    layercolorchanged:(layer:any)=>void
    layernamechanged:(layer:any)=>void
    selectionIdsChanged:(e:any)=>void
    // sl_measure_del:()=>void
    KeyboardKey:(e:any)=>void
    copyarray:()=>void
    mirror:()=>void
    testlod:()=>void
    canceltask:()=>void
    cmdMessage:()=>void
    cmdInput:()=>void
    textpop:()=>void
    confirmPop:(obj:any)=>void

    slfencedraw:()=>void
    slidentifyclear:()=>void
}

export class PubSub implements IDisposable {
    static readonly default: PubSub = new PubSub();

    private _events: Map<any, Set<(...args: any[]) => void>>;

    constructor() {
        this._events = new Map(); 
    }

    dispose() {
        this._events.forEach((v, k) => {
            v.clear();
        });
        this._events.clear();
    }

    sub<T extends PubSubEventMap, K extends keyof T>(
        event: K,
        callback: T[K] extends (...args: any[]) => any ? T[K] : never,
    ) {
        if (!this._events.has(event)) {
            this._events.set(event, new Set<(...args: any[]) => void>());
        }
        this._events.get(event)!.add(callback);
    }

    pub<T extends PubSubEventMap, K extends keyof T>(
        event: K,
        ...args: Parameters<T[K] extends (...args: any[]) => any ? T[K] : never>
    ) {
        this._events.get(event)?.forEach((x) => {
            x(...args);
        });
    }

    remove<T extends PubSubEventMap, K extends keyof T>(
        event: K,
        callback: T[K] extends (...args: any[]) => any ? T[K] : never,
    ) {
        let callbacks = this._events.get(event);
        if (callbacks?.has(callback)) {
            callbacks.delete(callback);
        }
    }

    removeAll<K extends keyof PubSubEventMap>(event: K) {
        this._events.get(event)?.clear();
    }
}
