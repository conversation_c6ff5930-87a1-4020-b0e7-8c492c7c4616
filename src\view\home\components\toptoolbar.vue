<template>
  <div class="layerManage">
    <div style="width: 20px;height: 20px;margin-right: 10px;" @click="showBlockGallery">
      <svg-icon iconClass="tuceng" w=20 h=20 style="padding-top:0px;"></svg-icon>
    </div>
    <div class="icon zpointer mr10">
      <svg-icon iconClass="wndcmd" w=30 h=30 style="padding-top:0px" @click="onWndCmd"></svg-icon>
    </div>
    <layersSelection></layersSelection>
    <colorsSelection></colorsSelection>
    <linestyleSelection></linestyleSelection>
    <div class="icon zpointer mr10" v-for="(item, index) in toolbarTopBtns" @click='onClick(item)' :key="index">
      <el-tooltip placement="bottom" :content="item.tip">
        <svg-icon :iconClass="item.icon" w=30 h=30 style="padding-top:0px"></svg-icon>
      </el-tooltip>
    </div>
    <div class="icon zpointer mr10">
      <svg-icon iconClass="kuaibuju" w=30 h=30 style="padding-top:0px" @click="createBlock"></svg-icon>
    </div>

    <div class="icon zpointer mr10">
      <svg-icon iconClass="kuaibuju" w=30 h=30 style="padding-top:0px" @click="testlod"></svg-icon>
    </div>
  </div>
  <BlockPop></BlockPop>
</template>

<script setup>

import { toolbarTopBtns } from '@/store/toolbartips'

import layersSelection from './popFrame/choiceLayers.vue'
import colorsSelection from './popFrame/choiceColors.vue'
import linestyleSelection from './popFrame/choiceLinestyle.vue'

import BlockPop from './popFrame/blockPop.vue'

import { mitts, btnFnObj } from 'sgdraw/mittBus'
import { PubSub } from 'emitter';
import { blockBox, viewlayout } from 'dxf-viewer'
import { pickupInfo, globalDxf ,attributeBarFalg} from 'dxf-viewer'


const emits = defineEmits([''])
const props = defineProps({
  ctx: {
    type: Object,
    default: null
  },
})

const onClick = (itm) => {
  if (itm.name === 'layermanage') {
    mitts.emit(btnFnObj.showLayerManagePop)
  } else if (itm.name === 'undo') {
    PubSub.default.pub('wasmundo')
  } else if (itm.name === 'redo') {
    PubSub.default.pub('wasmredo')
  } else if (itm.name === 'fitview') {
    PubSub.default.pub('fitview')
  }else if(itm.name === 'objfeatures') {
    attributeBarFalg.value=true
  }
}
let i = 0
const testlod = () => {
  i = i + 1
  console.log(i)
  PubSub.default.pub('testlod')
}
const createBlock = () => {
  if (!pickupInfo.pickupIdlist.length) return
  blockBox.blockPop = true
}

const showBlockGallery = () => {
  viewlayout.visBlockGallery = true
}
const onWndCmd = () => {
  viewlayout.visCodeEditor = true
}


defineExpose({

})

</script>

<style lang="scss" scoped>
.layerManage {
  display: flex;
  align-items: center;
  background-color: #3a4352;
  padding: 5px 10px;

  :deep(.el-select__selection) {
    visibility: hidden;
  }

  .icon {
    &:hover {
      background-color: #616975;
    }
  }
}

:deep(.el-popper) {
  border: none;
}

:deep(.el-scrollbar) {
  background-color: #212832;

  .is-hovering {
    background-color: #787878;
  }
}

:deep(.el-select__wrapper) {
  background-color: #212832;
  border: none;
  box-shadow: none;
}
</style>