#!/usr/bin/env bash
##Usage: build.sh
set -e -x
env

SERVICE_NAME='isolar_build_progress_front'
echo $SERVICE_NAME

npm cache clean --force
npm config ls -l
npm config set registry https://registry.npmmirror.com/
npm install
npm run build:$ENV

img=sungrowplant-registry.cn-hangzhou.cr.aliyuncs.com/sg-public/rd-shanghai/${SERVICE_NAME}
if [  "$ENV"  == 'production' ]; then
  img_tag=${ENV}_$(date +'%Y%m%d_%H%M%S')_${CI_COMMIT_SHORT_SHA}
else
  img_tag=${ENV}
fi

docker login --username sg20180808 --password $DOCKER_REGISTRY_PWD sungrowplant-registry.cn-hangzhou.cr.aliyuncs.com
docker build -t ${img}:${img_tag} -f cicd/Dockerfile .
docker push ${img}:${img_tag}
docker rmi ${img}:${img_tag}
echo "IMG_TAG=${img_tag}" > ${CI_PROJECT_NAME}_${CI_JOB_ID}_${CI_COMMIT_SHORT_SHA}.env
echo "IMG=${img}" >> ${CI_PROJECT_NAME}_${CI_JOB_ID}_${CI_COMMIT_SHORT_SHA}.env


