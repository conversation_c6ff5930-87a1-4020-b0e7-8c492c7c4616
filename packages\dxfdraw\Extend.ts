import * as THREE from "three";

import { KeyboardKey } from "dxf-viewer/src/controls";
import { DrawState, globalDxf, pickupInfo, poptip } from "dxf-viewer";
import { singal } from "emitter";
import { DrawHelp } from "./drawHelp";
export class Extend extends DrawHelp {
  public MIds: any;
  public extendObjId: any;
  public state: any; //0选择边界对象   1选择被延长的对象
  
  constructor(viewer, ids) {
    super(viewer);
    this.MIds = ids;
    globalDxf.drawstate = DrawState.Pick;
    this.isSnap = false;
    this.state = 0;
    this.init();
  }
  public async init() {
    await this.pick0();
    await this.pick1();
    await this.pick3({ tip: "是否保留" });
    pickupInfo.pickupIdlist=[];
    poptip.dispose();
  }
  public async pick0() {
    await poptip.tip({ tip: "请选择延长边界" });
    if (this.MIds.length == 0) {
      await this.select();
    } else {
      await this.setState(1);
    }
  }
  public async setState(v) {
    this.state = v;
  }
  public async pick1() {
    await poptip.tip({ tip: "请选择延长对象" });
    await this.select();
  }
  public async pick3(opt){
    poptip.tip(opt)
    return new Promise((resolve)=>{
        this.onCancel()
        resolve([])  
       
    })
}
  public override onRButtonDown(event: PointerEvent): boolean {
    this.endSelect();
    return true;
  }
  //键盘按下
  public override onKeyDown(event: KeyboardEvent): boolean {
    if (event.key == KeyboardKey.Escape) {
      //取消
      this.onCancel();
    } else if (event.key == KeyboardKey.Space) {
      this.endSelect();
    }
    return true;
  }

  //最后一步添加图元
  private addObjects(): void {
    let createObjs: any[] = [];
    let delIds: any[] = [];

    let borderCurve = this.cadCtx.getPBaseObjById(this.MIds[0]);
    console.log(this.MIds);
    this.extendObjId.forEach((id) => {
      let obj = this.cadCtx.getPBaseObjById(id);

      let newOne = obj.copyNewOne();
      let entityType = newOne.getObjType();
      if (entityType === "SGObjCurve") {
        let extendCurve = sg.SGObjTool.convertToCurve(newOne);
        let newCurveObj = sg.SGCurveObjTool.extend(extendCurve, 0, borderCurve);
        let newCurveObj2 = sg.SGCurveObjTool.extend(
          extendCurve,
          1,
          borderCurve
        );
        console.log(newCurveObj);
        console.log(newCurveObj2);
        if (newCurveObj) {
          createObjs.push(newCurveObj);
          delIds.push(id);
        } else if (newCurveObj2) {
          createObjs.push(newCurveObj2);
          delIds.push(id);
        }
      } else if (entityType === "SGObjComBinCurve") {
        let polylineNew = sg.SGObjTool.convertToComBinCurve(newOne);
        let newCurveObj = sg.SGCurveObjTool.extendComBinCurve(
          polylineNew,
          0,
          borderCurve
        );
        let newCurveObj2 = sg.SGCurveObjTool.extendComBinCurve(
          polylineNew,
          1,
          borderCurve
        );
        console.log(newCurveObj);
        console.log(newCurveObj2);
        if (newCurveObj) {
          createObjs.push(newCurveObj);
          delIds.push(id);
        } else if (newCurveObj2) {
          createObjs.push(newCurveObj2);
          delIds.push(id);
        }
      }
    });
    this.viewer.transaction.del(delIds);
    this.viewer.transaction.add(createObjs);
  }

  private endSelect() {
    //按空格键，确定
    if (this.state == 0) {
      //pick完成
      if (pickupInfo.pickupIdlist.length == 0) {
        console.log("未选取到对象,请重新选择");
      } else {
        this.state = 1;
        this.MIds = pickupInfo.pickupIdlist;
        singal.selectObj.dispatch();
        return;
      }
    } else if (this.state == 1) {
      if (pickupInfo.pickupIdlist.length == 0) {
        console.log("未选取到对象,请重新选择");
      } else {
        this.extendObjId = pickupInfo.pickupIdlist;
        
        this.addObjects();
        singal.selectObj.dispatch();
        return;
      }
    } else {
      this.onCancel();
    }
  }

  //绘制临时图元
  // private drawTempGraphic(secpoint): void {
  //   this.clearTempGeometry();
  //   let geometry1 = new THREE.BufferGeometry().setFromPoints([
  //     this.firstPoint,
  //     secpoint,
  //   ]);
  //   let material1 = new THREE.LineBasicMaterial({ color: "red" });
  //   let line1 = new THREE.Line(geometry1, material1);
  //   this.tempGeometry.push(line1);

  //   let borderCurve = new sg.SGObjCurve(
  //     this.threePointToSgPoint(this.firstPoint),
  //     this.threePointToSgPoint(secpoint)
  //   );
  //   this.MIds.forEach((id) => {
  //     let obj = this.cadCtx.getPBaseObjById(id);
  //     let newOne = obj.copyNewOne();
  //     let entityType = newOne.getObjType();
  //     if (entityType === "SGObjCurve") {
  //       let extendCurve = sg.SGObjTool.convertToCurve(newOne);
  //       let newCurveObj = sg.SGCurveObjTool.extend(extendCurve, 1, borderCurve);
  //       let newCurveObj2 = sg.SGCurveObjTool.extend(
  //         extendCurve,
  //         0,
  //         borderCurve
  //       );
  //       if (newCurveObj) {
  //         let points = newCurveObj.getCurve().getInterpolatePoints(50);
  //         let threeJsPoints: THREE.Vector3[] = [];
  //         for (let i = 0; i < points.size(); i++) {
  //           threeJsPoints.push(
  //             new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
  //           );
  //         }
  //         let geometry2 = new THREE.BufferGeometry().setFromPoints(
  //           threeJsPoints
  //         );
  //         let material2 = new THREE.LineBasicMaterial({ color: "green" });
  //         let line2 = new THREE.Line(geometry2, material2);
  //         this.tempGeometry.push(line2);
  //       } else if (newCurveObj2) {
  //         let points = newCurveObj2.getCurve().getInterpolatePoints(50);
  //         let threeJsPoints: THREE.Vector3[] = [];
  //         for (let i = 0; i < points.size(); i++) {
  //           threeJsPoints.push(
  //             new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
  //           );
  //         }
  //         let geometry2 = new THREE.BufferGeometry().setFromPoints(
  //           threeJsPoints
  //         );
  //         let material2 = new THREE.LineBasicMaterial({ color: "green" });
  //         let line2 = new THREE.Line(geometry2, material2);
  //         this.tempGeometry.push(line2);
  //       }
  //     } else if (entityType === "SGObjComBinCurve") {
  //       let polylineNew = sg.SGObjTool.convertToComBinCurve(newOne);
  //       let newCurveObj = sg.SGCurveObjTool.extendComBinCurve(
  //         polylineNew,
  //         0,
  //         borderCurve
  //       );
  //       let newCurveObj2 = sg.SGCurveObjTool.extendComBinCurve(
  //         polylineNew,
  //         1,
  //         borderCurve
  //       );
  //       console.log(newCurveObj);
  //       console.log(newCurveObj2);
  //       if (newCurveObj) {
  //         let points = newCurveObj.getCurve().getInterpolatePoints(50);
  //         let threeJsPoints: THREE.Vector3[] = [];
  //         for (let i = 0; i < points.size(); i++) {
  //           threeJsPoints.push(
  //             new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
  //           );
  //         }
  //         let geometry2 = new THREE.BufferGeometry().setFromPoints(
  //           threeJsPoints
  //         );
  //         let material2 = new THREE.LineBasicMaterial({ color: "green" });
  //         let line2 = new THREE.Line(geometry2, material2);
  //         this.tempGeometry.push(line2);
  //       } else if (newCurveObj2) {
  //         let points = newCurveObj2.getCurve().getInterpolatePoints(50);
  //         let threeJsPoints: THREE.Vector3[] = [];
  //         for (let i = 0; i < points.size(); i++) {
  //           threeJsPoints.push(
  //             new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
  //           );
  //         }
  //         let geometry2 = new THREE.BufferGeometry().setFromPoints(
  //           threeJsPoints
  //         );
  //         let material2 = new THREE.LineBasicMaterial({ color: "green" });
  //         let line2 = new THREE.Line(geometry2, material2);
  //         this.tempGeometry.push(line2);
  //       }
  //       // let anycurve2d = polylineNew.getCurve();
  //       // let curveCount = anycurve2d.curveCount();
  //       // points = anycurve2d.getInterpolatePoints(10 * curveCount);
  //     }
  //   });
  //   this.showTempGeometry();
  // }
}
