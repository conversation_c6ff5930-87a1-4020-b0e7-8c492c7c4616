import mapboxgl from 'mapbox-gl';
import { MyEvent } from './myevent';
import { Vector2 } from 'three';
import { tips } from './global';
import { MathLib } from './mathlib';
import { ButtonStatus } from './global';
mapboxgl.accessToken = 'pk.eyJ1IjoiamVmZnN0ZXJuIiwiYSI6IlAzRFFiN0EifQ.mNWvayrLEw9wULuq0sopyA'; // 替换为你的 token
export class MapboxViewerSync extends MyEvent {//注册MyEvent用于绑定事件
    public mapContainer
    public map
    public cadApp
    public cadContainer
    public get cadview(){//
        return this.cadApp.canvas
    }
    public _onload
    public mapCenter

    public get screenCen(){
        let t=this.map.transform
        return new Vector2(t.width/2,t.height/2)
    }
    
    public _cen//初始经纬度
    public _st:Vector2//
    public _en:Vector2
    public _drag:boolean=false

    constructor(mapContainer,cadApp) {
        super()
        this.mapContainer = mapContainer
        this.cadApp = cadApp
        this.cadContainer = cadApp.domContainer
        this._st=new Vector2()
        this._en=new Vector2()
        this.initMap()
    }
    public override dispose(){
        this.freezeMap()
        this.map.remove()
    }

    public initMap() {
        this.mapCenter=[117.20861541288662,31.836393226525608]
        this.map = new mapboxgl.Map({
            container: this.mapContainer,// mapContainer.value, // 指定地图容器
            style: 'mapbox://styles/mapbox/streets-v11', // 地图样式
            center: this.mapCenter, // 初始中心经纬度
            zoom: 15, // 初始缩放级别
            doubleClickZoom:false,
			// scrollZoom:false,
			dragRotate: false, // 禁用拖动旋转
			touchZoomRotate:false,//
			touchPitch:false,
            attributionControl: false 
        });
    }
    public freezeCAD(){//冻结cad
        this.cadContainer.style.pointerEvents='none'
    }
    public freezeMap(){
        console.log(this.cadview)
        this.cadContainer.style.pointerEvents='all'
        this.cadview.removeEventListener('pointerdown', this._onmousedown)
        this.cadview.removeEventListener('pointerup', this._onmouseup)
        this.cadview.removeEventListener('pointermove', this._onmousemove)
        this.cadApp.removeEventListener('wheel', this._onwheel)
    }
    public syncView(){
        console.log(this.cadview)
        this.cadContainer.style.pointerEvents='all'
        this.cadview.addEventListener('pointerdown', this._onmousedown)
        this.cadview.addEventListener('pointerup', this._onmouseup)
        this.cadview.addEventListener('pointermove',this._onmousemove)
        this.cadApp.addEventListener('wheel',this._onwheel)
    }
    public loadShp(opt){
        let {lng,lat,url,projId}=opt
        this.map.addSource('shapefile-data', {
            type: 'geojson',
            data: url // 这里使用你转换后的 GeoJSON 文件路径
        });
    
        this.map.addLayer({
            id: 'shapefile-layer',
            type: 'fill', // 你可以根据数据的类型使用不同的图层类型，比如 'line'、'circle' 等
            source: 'shapefile-data',
            paint: {
              'fill-color': '#885588', // 填充颜色
              'fill-opacity': 1 // 填充透明度
            }
        });
    }
    public loadTiff(opt){
        let {lng,lat,url,projId}=opt
        this.map.addLayer({
            'id': 'tiff_'+projId,
            'type': 'raster',
            'source': {
              'type': 'raster',
              'tiles': [
                url
              ],
              'tileSize': 256
            },
            'paint': {
              'raster-opacity': 1 // 设置透明度，使底图能叠加
            }
        })
        this.map.jumpTo({center:{lng,lat}})
    }
    public override onMouseMove(e:  MouseEvent): void {
        if(this._drag){
            
            this._en=new Vector2(e.offsetX,e.offsetY)
            let delta=new Vector2().subVectors(this._st,this._en)
            let lnglat=this.getLngLat(delta)
            this._cen=lnglat
            this.map.jumpTo({
                center:lnglat
            })
            this._st=this._en
        }
        tips.mouse={x:e.offsetX,y:e.offsetY}
        tips.lnglat=this.map.unproject(tips.mouse)
    }
    public override onMouseDown(e:  MouseEvent): void {
        console.log(e)
        if (e.button == ButtonStatus.Middle) {
            this.panStart(e)
        } 
    }
    public override onMouseUp(e: MouseEvent): void {
        console.log(e)
        this.panEnd(e)
    }

    public panStart(event: MouseEvent){
        this._drag=true
        this._cen=this.getLngLat()
        this._st=new Vector2(event.offsetX,event.offsetY)
    }
    public panEnd(event:MouseEvent){
        this._drag=false
    }
    public override onWheel(e: any): void {
        // let scale=this.cadApp.mapContainer.scale.x
        let deltaScale=1/e.event['_deltaScale']
        let zoom=MathLib.Log2(2**this.map.getZoom()*deltaScale)
        // tips.scale=scale
        tips.zoom=zoom
        let cur=new Vector2(e.event.offsetX,e.event.offsetY)
        let lnglat=this.map.unproject(cur)

        let delt=new Vector2().subVectors(cur,this.screenCen)
        console.log(this.screenCen,'this.screenCen1111');
        this.map.jumpTo({zoom,center:lnglat})
        let newpos=new Vector2().subVectors(this.screenCen,delt)
        console.log(this.screenCen,'this.screenCen222222');

        
        lnglat=this.map.unproject(newpos)
        console.log(lnglat,'lnglat');
        this.map.jumpTo({center:lnglat})

    }

    public onLoad() {
        this.lngGrid()
    }

    public getLngLat(delta=undefined){
        let cen=this.screenCen  
        if(delta){
            cen.add(delta)
        }
        let lnglat=this.map.unproject(cen)
        return lnglat
    }
    public lngGrid() {
        let cen=this.mapCenter
        const coordinates = [
            [180, 90],
            [180, -90]
        ];
        // 3. 在地图上添加线条
        this.map.on('load',function(){
            this.addSource('line', {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    geometry: {
                        type: 'LineString',
                        coordinates: coordinates,
                    },
                },
            });
            this.addLayer({
                id: 'line-layer',
                type: 'line',
                source: 'line',
                paint: {
                    'line-color': '#FF0000', // 线的颜色
                    'line-width': 4, // 线宽
                },
            });
            this.addSource('circle-source', {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: [{
                        type: 'Feature',
                        geometry: {
                            type: 'Point',
                            coordinates:cen  // 圆心坐标
                        }
                    }]
                }
            });
            this.addLayer({
                id: 'circle-layer',
                type: 'circle',
                source: 'circle-source',
                paint: {
                    'circle-radius': 5, // 圆的半径
                    'circle-color': '#FF0000', // 圆的颜色
                    'circle-opacity': 0.6 // 圆的透明度
                }
            });
        })
        
    }
}