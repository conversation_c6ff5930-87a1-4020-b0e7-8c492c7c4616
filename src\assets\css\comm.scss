.box30 {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;

  border-radius: 3px;
  border: 1px solid rgba(129, 113, 113, 0.623);
}

.fb {
  display: flex;
  justify-content: space-between;
}

.fc {
  display: flex;
  ;
  justify-content: center;
}

.zpointer {
  cursor: pointer;
}

.icon {
  &:hover {
    background-color: #616975;
  }
}

.scroll {
  &::-webkit-scrollbar-track-piece {
    background-color: #fff;
  }

  // 滚动条的宽度
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  // 滚动条的设置
  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    background-clip: padding-box;
    min-height: 14px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #bbb;
  }
}

.split10 {
  height: 10px;
}

.box10 {
  width: 10px;
  height: 10px;
}

.box12 {
  width: 12px;
  height: 12px;
}

.box16 {
  width: 16px;
  height: 16px;
}

.box20 {
  width: 20px;
  height: 20px;
}

.box24 {
  width: 24px;
  height: 24px;
}


.h20 {
  line-height: 20px;
  height: 20px;
}

.f15 {
  font-size: 15px
}

.f14 {
  font-size: 14px;
}

.f13 {
  font-size: 13px;
}

.ml5 {
  margin-left: 5px;
}

.ml10 {
  margin-left: 10px;
}

.mr5 {
  margin-right: 5px;
}

.mr10 {
  margin-right: 10px;
}

.mb1 {
  margin-bottom: 1px;
}

.w400 {
  width: 300px;
}

.red {
  color: red;
}