import { PubSub } from "emitter"
import { ILayerOpt } from "./interface"
import { sgcolor2rgba, sgcolor2Rgbs } from "./sgFunc"
import { ClearArray } from "../libs"
import { pickupInfo } from "./indentify"
import { Transaction } from "./transaction"
import { weakprops } from "../xData"
import { ColorMod } from "./constant"
import { WasmObjType } from "../constant"

export const dxflayers=reactive({
    curLayerIdx:0,
    curColor:'',
    curLineStyle:"",
    layers:[{
        name:"",
        displayName:"",
        isVisible:true,
        isFreeze:true,
        isLock:true,
        color:'',
        linestyle:'',
        rgb:[1,1,1]
    }],
    colors:[],
    linestyles:[],
})
export const curLayer=computed(()=>{
    return dxflayers.layers[dxflayers.curLayerIdx]
})
export const setCurLayer=(idx)=>{
    dxflayers.curLayerIdx=idx
}
export const getLayerColor=(idx?)=>{
    if(idx==undefined)idx=dxflayers.curLayerIdx
    let layer:ILayerOpt=dxflayers.layers[idx]
    return layer.color
}
export const setlayerVisible=(idx?)=>{//设置图层是否显示
    if(idx==undefined)idx=dxflayers.curLayerIdx
    let curlayer:ILayerOpt=dxflayers.layers[idx]
    curlayer.isVisible=!curlayer.isVisible  
    PubSub.default.pub('showlayer',curlayer)
}
export const setlayerAllVisible=(isVisible)=>{//设置全部图层是否显示
    PubSub.default.pub('showlayer',{type:'all',isVisible})
}

export const setlayerFreeze=(idx?)=>{
    if(idx==undefined)idx=dxflayers.curLayerIdx
    let curlayer:ILayerOpt=dxflayers.layers[idx]
    curlayer.isFreeze=!curlayer.isFreeze  
    PubSub.default.pub('freezelayer',curlayer)
}

export const setlayerLock=(idx?)=>{
    if(idx==undefined)idx=dxflayers.curLayerIdx
    let curlayer:ILayerOpt=dxflayers.layers[idx]
    curlayer.isLock=!curlayer.isLock  
    PubSub.default.pub('locklayer',curlayer)
}
export const initDxfLayerData=(gp)=>{
    let dxfbase=gp.getDxfBaseData()
    const layer = new sg.SGLayer()
    // layer.setName("场区围栏")
    // layer.setColor(new sg.SGColor(255,255,0,255))
    // dxfbase.addLayer(layer)
    layer.delete()
    let laycount=dxfbase.layerCount()
    let layers:ILayerOpt[]=[]
    for(let i=0;i<laycount;i++){
        let lay=dxfbase.getLayer(i)
        
        // console.log(lay.getName(),'lay',i);
        
        layers.push({
            name:lay.getName(),
            displayName:lay.getName(),
            isVisible:true,
            isFreeze:true,
            isLock:true,
            color:sgcolor2rgba(lay.getColor()),
            rgb:sgcolor2Rgbs(lay.getColor()),
            linestyle:''
        })
    }
    ClearArray(dxflayers.layers)
    dxflayers.layers.push(...layers)
}
export const newAddLayer = (gp,layerName='围栏') => {
    const dxfbase=gp.getDxfBaseData()
    const layer = new sg.SGLayer()
    layer.setName(layerName)
    dxfbase.addLayer(layer)
}

export const changeSelectedColor=(e)=>{
    pickupInfo.pickupIdlist.forEach(id=>{
        let {obj,properties,xdata}=weakprops.getProp(id)
        let [r,g,b]=e.color
        obj.setColor(new sg.SGColor(r,g,b,255))
    })
    Transaction.instance.gpRender()
}

    
watch(()=>pickupInfo.pickupIdlist,()=>{
    if(!pickupInfo.isSLflag) return
    let sgBDG=Transaction.sgBDG
    pickupInfo.pickupIdlist.forEach(id=>{
        if(!weakprops.hasId(id)){
            let objType=sgBDG.getObjType(id)
            if(objType==WasmObjType.Entity){
                let obj=sgBDG.getPBaseObjById(id)
                let color=obj.getColor()
                let [r,g,b]=sgcolor2rgba(color)
                let colorMod=ColorMod.Fixed
                // colorMod=color.getMode()
                let props={
                    obj,
                    properties:{
                        color:[r,g,b],
                        colorMod
                    },
                    xdata:{

                    }
                }
                weakprops.addProp(id,props)
            }else if(objType==WasmObjType.Block){
                let block=sgBDG.getInsertBlockById(id)
                console.log('todo 块颜色需要处理')
            }
        }
    })
})