@import './comm.scss';
html,
body,
#app{
    margin: 0px;
    overflow: hidden;
}
.col,
.row{
    display: flex;
}
.col{
    flex-direction: column;
}
.fa{
    justify-content: space-around;
}
.fb{
    justify-content: space-between;
}
.fu{
    width: 100%;
    height: 100%;
}

// .el-tree{
//     // background: none;
//     background-color: rgba(77,77,77,0.45);
//     border-radius: 14px;
//     color: #fff;
//     // color: rgba(77,77,77);
//     font-weight: 500;
//     --el-tree-node-hover-bg-color:rgba(77,77,77,0.45);
//     // --el-tree-node-hover-bg-color:#fff;
// }
// .el-tree-node__content{
//     height: 26px;
// }
.el-popper {
    min-width: 60px !important;
}

/*纯CSS实现『斑马纹理投影文字』 
https://cloud.tencent.com/developer/article/1982532
*/
.zebra__shadow__text {
    font-size: 100px; /* 文字大点，效果更明显 */
    font-weight: bold; /* 文字粗点，这样效果更明显 */
    color: transparent; /* 设置文字填充颜色为透明，这样可以把背景显示出来 */
    -webkit-text-fill-color: transparent; /* 使用一个非标准的方法覆盖文字填充颜色，以防color被其他代码覆盖 */
    text-shadow: 6px -6px #000, 4px -4px #fff; /* 设置顶层黑色投影和中层白色投影 */
    background-image: linear-gradient(135deg, #fff 0%, #fff 25%, #000 25%, #000 50%, #fff 50%, #fff 75%, #000 75%, #000 100%);   /* 使用 #fff 和 #000 画出条纹效果，并倾斜135度 */
    background-size: 6px 6px; /* 背景图大小，控制斑马纹的粗细 */
    background-repeat: repeat; /* 不断重复渲染背景图 */
    -webkit-background-clip: text; /* 将背景渲染到文本中（兼容性写法） */
    background-clip: text; /* 将背景渲染到文本中 */
}
.stats{
    position: absolute; 
    top: 0px; 
    cursor: pointer;
    opacity: 0.9; 
    z-index: 10000; 
    right: 0px; 
    display: none;
}