<template>
    <el-dialog
      :model-value="measureObj.measurePop"
      title="比例尺设置"
      width="300"
      :before-close="btnclose"
    >
        <div class="boxbtn1">
           <!-- <div>1.设置比例尺</div> -->
           <div style="margin-top: 5px;">
            <el-input v-model="input" disabled style="width: 40px"/>
            :
            <el-input v-model="measureObj.measureScaleText" @blur="changescale" style="width: 70px"/>
            <el-button style="margin-left: 25px;" @click="btnMeasurescale" :icon="ScaleToOriginal">A:B</el-button>
          </div>
        </div>
        
      <template #footer>
        <div class="dialog-footer">
          <!-- <el-button type="primary" @click="btnsizePopseve">确认</el-button> -->
          <el-button @click="btnclose">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      :model-value="measureObj.sizePop"
      title="输入实际尺寸"
      width="300"
      :before-close="btnclosesizePop"
    >
      <div>输入两点之间的实际尺寸来设置标注比例:</div>
      <div style="margin-top: 10px;">
        <el-input v-model="measureObj.sizetext" style="width: 250px"/>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="btnsizePopseve">确认</el-button>
          <!-- <el-button @click="btnclosesizePop">关闭</el-button> -->
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script lang="ts" setup>
  import { ref,onMounted,nextTick,reactive } from 'vue'
  import {measureObj,} from '../slMethod/measurementCategory'
  import { ElLoading ,ElMessage } from "element-plus";
  import {ScaleToOriginal,} from '@element-plus/icons-vue'
    import {dxflayers,SlScopeType,pickupInfo} from 'dxf-viewer'


  const emits = defineEmits(['sizePopseve','measureScale','measureChangescale'
  ,'measureFn'])

  const props = defineProps({
    measureScaleText:{
        type:String,
        default:''
    },
    measurementCategoryObj:{
      type:Object
    },
    sLcontrolObj:{
        type:Object
    },
  })

  const input=ref('1')

  onMounted(()=>{
  })

  
  const btnMeasurescale=()=>{
    // props.measurementCategoryObj.operationType('scale')

    measureObj.measurePop=false
    props.sLcontrolObj.setState(SlScopeType.Scale)
  }
  const btnclose=()=>{
    props.measurementCategoryObj.closePop()
    props.sLcontrolObj.setState(SlScopeType.Empty)
  }

  const btnclosesizePop=()=>{
    props.measurementCategoryObj.closesizePop()
  }

  const btnsizePopseve=()=>{
    if(!measureObj.sizetext) return ElMessage({ message: '请输入实际尺寸', type: 'warning',})
    emits('sizePopseve')
  }

  const changescale=()=>{
    emits('measureChangescale')
  }


  defineExpose({

  })
  </script>
  
  <style lang="scss" scoped>
  
  .measure {
    width: 300px;
    height: 188px;
    background-color: #fff;
    position: absolute;
    top: 50px;
    left: 10px;
    z-index: 9;
    .title {
      display: flex;
      justify-content: space-between;
      padding:3px 10px;
      font-size: 18px;
      border-bottom: 1px solid #333;
      .close {
        font-weight: 500;
        padding: 0px 8px 5px 8px;
        cursor: pointer;
        &:hover{
          background-color: #57aaff;
        }
      }
    }
    .boxbtn1 {
      padding:10px;
    }
    .boxbtn2 {
      padding: 10px;
      padding-top: 0;
    }
  }

  </style>
  
  