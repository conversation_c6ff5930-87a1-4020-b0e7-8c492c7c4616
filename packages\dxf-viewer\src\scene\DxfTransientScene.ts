import { DxfParseScene } from "./DxfParseScene"
import * as THREE from "three"

function convertPoint2dToVector2(point2d) {
    return new THREE.Vector2(point2d.x(), point2d.y());
}

const gripPointSize = 12;
const gripPointColor = 0xffff00;
const gripLienColor = 0xffff00;

export class DxfTransientScene extends DxfParseScene {
    // entity related
    private dxf;
    private gp;
    private entityIds : number;
    //private entityPoints: THREE.Vector2[];
    private entityPoints;
    private cloneDrawObj;
    private entityName: string;
    private gripLinkedIndices : number [];
    // scene realated
    private scene: THREE.Scene;
    private point: THREE.Points;
    private scenePoints: THREE.Vector2[];

    constructor(viewer) {
        super(viewer);
        this.scene = new THREE.Scene();
        let pointGeom = new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0, 0, 0)]);
        let pointMat = new THREE.PointsMaterial({
            size: gripPointSize,
            color: gripPointColor,
            //depthTest: false
        });

        this.point = new THREE.Points(pointGeom, pointMat);
        this.point.visible = false;
        this.scene.add(this.point);

        this.entityPoints = [];
        this.entityName = 'entity';

        const lineGeom = new THREE.BufferGeometry();
        let lineMat = new THREE.LineBasicMaterial({
            color: gripLienColor
        });
        let line = new THREE.Line(lineGeom, lineMat);
        line.name = this.entityName;
        line.visible = false
        this.scene.add(line)

        this.dxf = null;
        this.entityIds = 0;
        this.gripLinkedIndices = [];
        this.scenePoints = [];
    }

    async setTransientEntityById(gp, id) {
        this.entityPoints = [];
        this.scenePoints = [];

        //this.dxf = dxf;
        this.gp = gp;
        this.entityIds = id;

        this.updateEntityPoints();
        // TODO: this.updateScenePoints();

        this.initTransientScene();
    }

    updateEntityPoints() {
        //let gp = this.gp;
        let drawObj = this.gp.getPBaseObjById(this.entityIds)
        if (!drawObj) {
            return
        }
        let entityType = drawObj.getObjType();
        if (entityType === 'SGObjCurve') {//单条线 
            let curve = sg.SGObjTool.convertToCurve(drawObj);
            // let curve2d = curve.getCurve()
            // let curveType: string = curve2d.getCurveType();
            const selectPoints = curve.selectPoints();
           
            const n = selectPoints.size()
            for(let i = 0; i< n; i++){
                const typedPoint = selectPoints.get(i)
                const pt = typedPoint.getPoint()
                const type = typedPoint.getString()
                this.entityPoints.push({
                    'type':type,
                    'point':convertPoint2dToVector2(pt)
                })
            }
        } else if (entityType === 'SGObjComBinCurve') {
            let polylineObj = sg.SGObjTool.convertToComBinCurve(drawObj);
            const selectPoints = polylineObj.selectPoints();
            const n = selectPoints.size()
            for(let i = 0; i< n; i++){
                const typedPoint = selectPoints.get(i)
                const pt = typedPoint.getPoint()
                const type = typedPoint.getString()
                this.entityPoints.push({
                    'type':type,
                    'point':convertPoint2dToVector2(pt)
                })
            }
        }else {
            console.warn("grips not supported for : " + entityType)
        }
    }

    updateScenePoints() {
        //let gp = this.dxf.getDrawGroup()
        let drawObj = this.gp.getPBaseObjById(this.entityIds)

        if (!drawObj) {
            return
        }
        let entityType = drawObj.getObjType();
        if (entityType === 'SGObjCurve') {//单条线 
            let curve = sg.SGObjTool.convertToCurve(drawObj);
            let curve2d = curve.getCurve()
            let curveType: string = curve2d.getCurveType();
            if (curveType === 'LineSegment2d') {
                let line2d = sg.Geometry2dUtils.convertToLineSegment2d(curve2d)
                this.scenePoints.push(convertPoint2dToVector2(line2d.startPoint()));
                this.scenePoints.push(convertPoint2dToVector2(line2d.endPoint()));
            }
        } else if (entityType === 'SGObjComBinCurve') {
            let polylineObj = sg.SGObjTool.convertToComBinCurve(drawObj);
            let anycurve2d = polylineObj.getCurve();
            const cunt = anycurve2d.curveCount();
            const nInterp = 16;
            for (let i = 0; i < cunt; i++) {
                let lItm = anycurve2d.getBoundCurve(i)
                const curveType = lItm.getCurveType()
                if (curveType === 'LineSegment2d') {
                    this.scenePoints.push(convertPoint2dToVector2(lItm.startPoint()));
                    this.scenePoints.push(convertPoint2dToVector2(lItm.endPoint()));
                } else if (curveType === 'Arc2d') {
                    let dfxEntity = sg.Geometry2dUtils.convertToArc2d(lItm)
                    let plist = dfxEntity.getInterpolatePoints(nInterp);
                    for (let j = 0; j < nInterp; j++) {
                        this.scenePoints.push(convertPoint2dToVector2(plist.get(j)));
                    }
                } else {
                    console.warn("polyline error: " + curveType)
                    continue
                }
            }
        }else {
            console.warn("grips not supported for : " + entityType)
        }
    }

    initTransientScene() {
        let obj = this.scene.getObjectByName(this.entityName)
        this.updateGeometryVertices(obj, this.entityPoints);
    }

    public getScene() {
        return this.scene;
    }

    public clear() {
        this.scene.clear();
    }

    public showPoint() {
        if (!this.point.visible) {
            this.point.visible = true;
            this.point.geometry.getAttribute('position').needsUpdate = true;
        }
    }

    public cleanTransientScene() {
        this.point.visible = false;        
        this.scene.getObjectByName(this.entityName).visible = false;

        // write new position back to entity
        //let gp = this.dxf.getDrawGroup()
        let drawObj = this.gp.getPBaseObjById(this.entityIds)
        if (!drawObj) {
            console.log('not found')
            return
        }
        if (!this.cloneDrawObj) {
            console.log('not found')
            return
        }
        let entityType = drawObj.getObjType();
        let index = 0;
        if (entityType === 'SGObjCurve') {//单条线 
            let curve = sg.SGObjTool.convertToCurve(drawObj);
            let curveNew = sg.SGObjTool.convertToCurve(this.cloneDrawObj);
            curve.setpCurve(curveNew.getCurve())
           
            // let curve2d = curve.getCurve()
            // let curveType: string = curve2d.getCurveType();
            // let newCurve;
            // if (curveType === 'LineSegment2d') {
            //     newCurve = new sg.LineSegment2d(
            //         new sg.Point2d(this.entityPoints[index].x, this.entityPoints[index].y),
            //         new sg.Point2d(this.entityPoints[index+1].x, this.entityPoints[index+1].y)
            //     );
            //     index += 2;
            // }else if(curveType === 'Circle2d'){
            //     //curve.set
            // }
            // else {
            //     console.warn("cannot update grip point for : " + curveType)
            // }
            // curve.setpCurve(newCurve);
        } else if (entityType === 'SGObjComBinCurve') {
            let polylineObj = sg.SGObjTool.convertToComBinCurve(drawObj);
            let polylineNew = sg.SGObjTool.convertToComBinCurve(this.cloneDrawObj);
            let anycurve2d = polylineNew.getCurve();
           
           // this.entityPoints.map(value=>vecVertex.push_back(new sg.Point2d(value.x, value.y)))
            let polyline = sg.Geometry2dUtils.convertToPolyLine2d(anycurve2d);
            let vecVertex = polyline.getVertex();
            const bulge = polyline.getBulge();
            const isClosed = polyline.isClosed();
            polylineObj.setCurveVertex(vecVertex, bulge, isClosed);
        }
        this.cloneDrawObj=null
    }

    public validGripPointsIndices(point, threshold) {
        this.gripLinkedIndices = this.entityPoints
            .filter((value, index)=> new THREE.Vector3(value['point'].x, value['point'].y, 0).distanceTo(point) < threshold)
            .map(value => this.entityPoints.indexOf(value))
        return this.gripLinkedIndices;
    }

    public updateTransientScene(point) {
        // set grips
        
        this.point.visible = true;
        this.point.geometry.setFromPoints([point]);
        this.point.geometry.getAttribute('position').needsUpdate = true;

        // set entities
        let drawObj = this.gp.getPBaseObjById(this.entityIds)
       
        if (!this.cloneDrawObj) {
            this.cloneDrawObj=drawObj.copyNewOne();
        }
        let newPoints=[];
        let points;
        let entityType = this.cloneDrawObj.getObjType();
        if (entityType === 'SGObjCurve') {//单条线 
            let curve = sg.SGObjTool.convertToCurve(this.cloneDrawObj);
            let curve2d = curve.getCurve()
            let curveType: string = curve2d.getCurveType();
            if(curveType==='LineSegment2d'){
                this.gripLinkedIndices.map(index=>{
                    if(this.entityPoints[index].type==='startPoint'){
                        curve.setSegmentStartPoint(new sg.Point2d(point.x,point.y))
                    }else if(this.entityPoints[index].type==='endPoint'){
                        curve.setSegmentEndPoint(new sg.Point2d(point.x,point.y))
                    }else{
                        curve.setSegmentCenterPoint(new sg.Point2d(point.x,point.y))
                    }
                })
                curve2d = curve.getCurve()
                points=curve2d.getInterpolatePoints(2)
                
            }else if(curveType==='Circle2d'){
                this.gripLinkedIndices.map(index=>{
                    if(this.entityPoints[index].type==='center'){
                        curve.setCircleCenter(new sg.Point2d(point.x,point.y))
                    }
                    else{
                        curve.setCirclePoint(new sg.Point2d(point.x,point.y))
                    }
                })
                curve2d = curve.getCurve()
                let r=curve2d.getRadius();
                points=curve2d.getInterpolatePoints(50*(r+1))
            }
        }else if (entityType === 'SGObjComBinCurve'){
            let polylineNew = sg.SGObjTool.convertToComBinCurve(this.cloneDrawObj);
            polylineNew.setCurveSingleVertex(new sg.Point2d(point.x,point.y),this.gripLinkedIndices[0])
            let anycurve2d = polylineNew.getCurve();
            let curveCount=anycurve2d.curveCount()
            points=anycurve2d.getInterpolatePoints(10*curveCount)
        }
        let n=points.size();
        for (let i=0;i<n;i++) {
            let value=points.get(i);
            newPoints.push(new THREE.Vector2(value.x(),value.y()))
        }
        //this.gripLinkedIndices.map(index=>this.entityPoints[index] = new THREE.Vector2(point.x, point.y))
        // TODO to set entity points, selection points and scene points accordingly
        //this.gripLinkedIndices.map(index=>this.scenePoints[index] = new THREE.Vector2(point.x, point.y))
        let obj = this.scene.getObjectByName(this.entityName)
        this.updateGeometryVertices(obj, newPoints);
        //this.updateGeometryVertices(obj, this.entityPoints);
        obj.visible = true;        
    }

    updateGeometryVertices(object, newPositions) {
        if (object.geometry && object.geometry.isBufferGeometry) {
            object.geometry.setFromPoints(newPositions)
            object.geometry.attributes.position.needsUpdate = true;
        } else {
            console.error('Object does not have a BufferGeometry.');
        }
    }
    public moveGrips(off){
        this.entityPoints.map(e=>{
            e.point.x=off.x+e.point.x
            e.point.y=off.y+e.point.y
        })
        let newPoints=this.entityPoints.map(e=>{return e.point})
        let obj = this.scene.getObjectByName(this.entityName)
        this.updateGeometryVertices(obj, newPoints);
        obj.visible = true;
    }
    public endMoveGrips({x,y}){
        let drawObj = this.gp.getPBaseObjById(this.entityIds)
        let trans=new sg.Matrix3()
        trans.move(x,y)
        drawObj.transform(trans)
    }
}