import { globalDxf } from "../global"

export var getEntityRgba = (cc) => {
    let colorObj = cc.getColor()
    let red = colorObj.getR()
    let green = colorObj.getG()
    let blue = colorObj.getB()
    let alpha = colorObj.getA()
    let dcolor = `rgba(${red}, ${green}, ${blue}, ${alpha})`
    return dcolor
}
export var getSGColorRgba = (sgColor) => {
    let red = sgColor.getR()
    let green = sgColor.getG()
    let blue = sgColor.getB()
    let alpha = sgColor.getA()
    let dcolor = `rgba(${red}, ${green}, ${blue}, ${alpha})`
    return dcolor
}
export var getSGColorRGB=(sgColor)=>{
    let r = sgColor.getR()
    let g = sgColor.getG()
    let b = sgColor.getB()
    return [r,g,b]
}
export var getEntityInfo=(entity)=>{
    let color = getEntityRgba(entity)
    let lineType=entity.getLineType()
    let lWidth = entity.getLineWidth() * 4
    let id=entity.getDrawObjectId()
    let layer = entity.getLayer()
    return {color,lineType,lWidth,id,layer}
}
export var getEntityInfo2=(entity)=>{
    let lineType=entity.getLineType()
    let lineScale=entity.getLineTypeScale()
    let layer = entity.getLayer()
    let color=entity.getColor()
    let colorMod=color.getMode()
    
    return {lineType,layer,colorMod:colorMod.value,lineScale}
}
export var getSgBuf=()=>{
    let buf= sg.getShareBuffer();
    // let len=sg.getShareBufferLen();
    sg.freeShareBuffer();
    return buf.slice(18)
}
export var getSgBufTxt=()=>{
    let buf= sg.getShareBuffer();
    sg.freeShareBuffer();
    let [id,r,g,b,a,lWidth,lscale,]=buf.slice(4,11)
    let color=`rgba(${r},${g},${b},${a})`
    return {
        id,
        color,
        lWidth,
        lscale,
        
    }
}
export var getSgBufInf=()=>{
    let buf= sg.getShareBuffer();
    sg.freeShareBuffer();
    let [id,r,g,b,a,lWidth,lscale]=buf.slice(4,11)
    let color=`rgba(${r},${g},${b},${a})`
    let vertices=buf.slice(18)
    return {
        id,
        color,
        lWidth,
        lscale,
        vertices,
        rgb:[r,g,b]
    }
}
export var getInterpNum=(a)=>{
    if(!globalDxf.isLodOn) return 24
    let nInterp = 5;
    let id:number=a.getDrawObjectId()
    if(globalDxf.idsInScreen.includes(id)){
        let box=a.getBoundingBox()
        let tess=box.width()/globalDxf.view.wid*100
        nInterp=Math.min(Math.max(Math.round(tess),nInterp),64)
    }
    return nInterp
}