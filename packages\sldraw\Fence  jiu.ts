import { DrawHelp } from "../dxfdraw/drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState,pickupInfo } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial,Group, Vector3,Line as ThLine } from "three"
import { PubSub } from 'emitter';

export class <PERSON>ce extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    public Fence:THREE.Group = new Group()//临时对象组
    public color:string='yellow'
    public els:any=[]
    public fencelist:any=[]
    public draw:boolean=true

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        const material = new LineBasicMaterial({ color:this.color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.Fence.add(this.mesh)
        this.viewer.transientSceneWasm.scene.add(this.Fence)

        PubSub.default.sub("slfencedraw", this.contro.bind(this));
        PubSub.default.sub("slidentifyclear", this.clear.bind(this));
        pickupInfo.pickupIdlist=[]
        this.init()
    }
    public async init(){
        
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        while (this.draw) {
            let p1=await this.lengthPick({
                tip:'指定下一个点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
        }
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    public override onRButtonDown( event:PointerEvent ) : boolean{
       
        if(this.pnts.length) {
            this.generatePolyline()
            this.pnts=[]
            const geometry = new BufferGeometry().setFromPoints([]);
            // 创建线的材质
            const material = new LineBasicMaterial({ color:this.color});
            // 使用几何体和材质创建线
            this.mesh = new ThLine(geometry, material);
            this.Fence.add(this.mesh)
        }else {
            // this.complete() 
            this.draw=false
            pickupInfo.pickupIdlist=pickupInfo.fenceDrawlist.map(item=>item.id)
        }
               
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }
    public override onKeyUp(event: KeyboardEvent): boolean {
        if (event.key === 'Escape') {
            console.log('Escape key was pressed!');
            if(this.pnts.length) {
                this.generatePolyline()
                this.pnts=[]
                const geometry = new BufferGeometry().setFromPoints([]);
                // 创建线的材质
                const material = new LineBasicMaterial({ color:this.color});
                // 使用几何体和材质创建线
                this.mesh = new ThLine(geometry, material);
                this.Fence.add(this.mesh)
            }else {
                this.clear()
            }
        }
        

        return true
    }

    private generatePolyline(){
        if(this.pnts.length<2) return 

        this.mesh.geometry.setFromPoints(this.pnts);

        const wasmVPlist=this.setWasmVPlist(this.pnts)
        let wasmBulge = new sg.vector_double()
        const pl=new sg.PolyLine2d(wasmVPlist, wasmBulge, false)
        const length=pl.length()
        const gp=new sg.SGObjComBinCurve()
        gp.setpCurve(pl)
        gp.setLayer("场区围栏")
        gp.setColor(new sg.SGColor(255,255,0,255))
        const id = gp.getDrawObjectId()
        console.log(id,'id');
        
        pickupInfo.fenceDrawlist.push({
            id,
            length,
        })
        this.els.push(gp)
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
    }
    
    public contro(){
        for(let c of this.Fence.children){
            this.Fence.remove(c)
        }
        this.viewer.transientSceneWasm.scene.remove(this.Fence)
        poptip.dispose()
        if(!this.els.length) return
        this.viewer.transaction.add(this.els)
        this.pnts=[]
        this.els=[]
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false

    }
    public clear(){
        for(let c of this.Fence.children){
            this.Fence.remove(c)
        }
        this.viewer.transientSceneWasm.scene.remove(this.Fence)
        this.pnts=[]
        poptip.dispose()
        pickupInfo.fenceDrawlist=[]
        pickupInfo.pickupIdlist=[]
        this.els=[]
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
    }
    private setWasmVPlist(vList){
        let {x,y}=globalDxf.wcsOff
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x+x, item.y+y))
        })
        return wasmVPlist
    }
}

