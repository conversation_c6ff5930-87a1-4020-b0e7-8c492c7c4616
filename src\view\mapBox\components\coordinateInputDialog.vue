<template>
  <el-dialog
    :model-value="visible"
    title="请输入经纬度"
    width="30%"
    @update:model-value="$emit('update:visible', $event)"
    @close="$emit('close')"
  >
    <el-form :model="form" label-width="80px">
      <el-form-item label="经度">
        <el-input v-model.number="form.lng" placeholder="请输入经度"></el-input>
      </el-form-item>
      <el-form-item label="纬度">
        <el-input v-model.number="form.lat" placeholder="请输入纬度"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  visible: <PERSON><PERSON><PERSON>,
});

const emit = defineEmits(['update:visible', 'close', 'confirm']);

const form = ref({
  lng: null,
  lat: null,
});

const handleConfirm = () => {
  emit('confirm', { ...form.value });
};

watch(() => props.visible, (newVal) => {
  if (!newVal) {
    form.value = { lng: null, lat: null };
  }
});
</script> 