import * as THREE from "three";

import { CadMessageReply } from "./CadMessageReply";
import { KeyboardKey } from "dxf-viewer/src/controls";
import { DrawState, globalDxf, pickupInfo, poptip } from "dxf-viewer";
import { singal } from "emitter";
import { DrawHelp } from "./drawHelp";
export class Trim extends DrawHelp {
  public MIds: any;
  public trimObjID: any;
  public state: any; //0选择边界对象   1选择被剪切对象 2删除

  constructor(viewer, ids) {
    super(viewer);
    this.MIds = ids;
    globalDxf.drawstate = DrawState.Pick;
    this.isSnap = false;
    this.state = 0;
    this.init();
  }
  public async init() {
    await this.pick0();
    await this.pick1();
    await this.pick2();
    await this.pick3({ tip: "是否保留" });
    pickupInfo.pickupIdlist = [];
    poptip.dispose();
  }
  public async pick0() {
    await poptip.tip({ tip: "请选择边界对象" });
    console.log("duixiang", this.MIds.length);
    if (this.MIds.length == 0) {
      await this.select();
    } else {
      await this.setState(1);
    }
  }
  public async setState(v) {
    this.state = v;
  }
  public async pick1() {
    await poptip.tip({ tip: "请选择被剪切对象" });
    await this.select();
  }
  public async pick2() {
    await poptip.tip({ tip: "请选择删除的部分" });
    await this.select();
  }
  public async pick3(opt) {
    poptip.tip(opt);
    return new Promise((resolve) => {
      this.onCancel();
      resolve([]);
    });
  }
  //消息返回false表示可以往下路由，返回true表示在这个节点截止不再往下路由
  // 左键按下
  public override onRButtonDown(event: PointerEvent): boolean {
    this.endSelect();
    return true;
  }
  //键盘按下
  public override onKeyDown(event: KeyboardEvent): boolean {
    if (event.key == KeyboardKey.Escape) {
      //取消
      this.onCancel();
    } else if (event.key == KeyboardKey.Space) {
      this.endSelect();
    }
    return true;
  }
  //最后一步添加图元
  private addObjects(): void {
    let createObjs: any[] = [];
    let delIds: any[] = [];

    let borderCurve = this.cadCtx.getPBaseObjById(this.MIds[0]);
    this.trimObjID.forEach((id) => {
      let obj = this.cadCtx.getPBaseObjById(id);
      let newOne = obj.copyNewOne();
      let entityType = newOne.getObjType();
      if (entityType === "SGObjCurve") {
        let trimedcurve = sg.SGObjTool.convertToCurve(newOne);
        let newCurveObjVec = sg.SGCurveObjTool.trim(trimedcurve, borderCurve);
        if (newCurveObjVec.size() > 0) {
          delIds.push(id);
        }
        for (let i = 0; i < newCurveObjVec.size(); i++) {
          createObjs.push(newCurveObjVec.get(i));
        }
      } else if (entityType === "SGObjComBinCurve") {
        let polylineNew = sg.SGObjTool.convertToComBinCurve(newOne);
        let newCurveObjVec = sg.SGCurveObjTool.trimComBinCurve(
          trimedcurve,
          polylineNew
        );
        if (newCurveObjVec.size() > 0) {
          delIds.push(id);
        }
        for (let i = 0; i < newCurveObjVec.size(); i++) {
          createObjs.push(newCurveObjVec.get(i));
        }
      }
    });
    this.viewer.transaction.del(delIds);
    this.viewer.transaction.add(createObjs);
  }

  private endSelect() {
    //按空格键，确定
    if (this.state == 0) {
      //pick完成
      if (pickupInfo.pickupIdlist.length == 0) {
        console.log("未选取到对象,请重新选择");
      } else {
        this.state = 1;
        this.MIds = pickupInfo.pickupIdlist;
        singal.selectObj.dispatch();
        return;
      }
    } else if (this.state == 1) {
      if (pickupInfo.pickupIdlist.length == 0) {
        console.log("未选取到对象,请重新选择");
      } else {
        this.trimObjID = pickupInfo.pickupIdlist;
        this.state = 2;
        this.addObjects();
        pickupInfo.pickupIdlist = [];
        singal.selectObj.dispatch();
        return;
      }
    } else if (this.state == 2) {
      if (pickupInfo.pickupIdlist.length == 0) {
        console.log("未选取到对象,请重新选择");
      } else {
        console.log(pickupInfo.pickupIdlist[0]);
        let delArray = pickupInfo.pickupIdlist.filter((id) => {
          for (let mid of this.MIds) {
            if (mid == id) {
              return false;
            }
          }
          return true;
        });
        this.viewer.transaction.del(delArray);
        pickupInfo.pickupIdlist = [];
        singal.selectObj.dispatch();
      }
    } else {
      this.onCancel();
    }
  }
  //绘制临时图元
  // private drawTempGraphic(secpoint):void{
  //     this.clearTempGeometry()
  //     let geometry1 =new THREE.BufferGeometry().setFromPoints([this.firstPoint,secpoint]);
  //     let material1 = new THREE.LineBasicMaterial({ color:'red'});
  //     let line1 = new THREE.Line(geometry1, material1);
  //     this.tempGeometry.push(line1)

  //     // let trimedcurve=new sg.SGObjCurve(this.threePointToSgPoint(this.firstPoint),this.threePointToSgPoint(secpoint));
  //     // this.MIds.forEach((id)=>{
  //     //     let obj = this.cadCtx.getPBaseObjById(id)
  //     //     let newOne = obj.copyNewOne()
  //     //     let borderCurve=sg.SGObjTool.convertToCurve(newOne);
  //     //     let newCurveObjVec=sg.SGCurveObjTool.trim(borderCurve,trimedcurve);
  //     //     console.log(newCurveObjVec.size());
  //     //     for(let i=0;i<newCurveObjVec.size();i++){
  //     //         console.log(newCurveObjVec.get(i));
  //     //         let points=newCurveObjVec.get(i).getCurve().getInterpolatePoints(50);
  //     //         let threeJsPoints:THREE.Vector3[]=[];
  //     //         for(let i=0;i<points.size();i++){
  //     //             threeJsPoints.push(new THREE.Vector3(points.get(i).x(),points.get(i).y(),0))
  //     //         }
  //     //         let geometry2 = new THREE.BufferGeometry().setFromPoints(threeJsPoints);
  //     //         let material2 = new THREE.LineBasicMaterial({ color:'green'});
  //     //         let line2 = new THREE.Line(geometry2, material2);
  //     //         this.tempGeometry.push(line2)
  //     //     }

  //     // })
  //     this.showTempGeometry()
  // }
}
