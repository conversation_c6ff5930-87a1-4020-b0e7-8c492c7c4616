/**
 * axios封装
 * 请求拦截、响应拦截、错误统一处理 { AxiosInstance }
 */
import axios, { AxiosInstance } from "axios";

var tip = console.log;
var toLogin = function () {
};

// 创建axios实例
// @ts-ignore
var instance: AxiosInstance = axios.create({
  timeout: 60000,
  baseURL: import.meta.env.VITE_MAP_BOX_UPLOAD_URL,
});
// 设置post请求头
instance.defaults.headers.post["Content-Type"] =
  "application/x-www-form-urlencoded;charset=utf-8";
instance.defaults.headers.get["Content-Type"] =
  "application/x-www-form-urlencoded";
// axios.defaults.baseURL ='http://*************:8080'
// axios.defaults.baseURL =

/**
 * 请求拦截器
 * 每次请求前，如果存在token则在请求头中携带token
 */

instance.interceptors.request.use(
  (config) => {
    // 登录流程控制中，根据本地是否存在token判断用户的登录情况
    // 但是即使token存在，也有可能token是过期的，所以在每次的请求头中携带token
    // 后台根据携带的token判断用户的登录情况，并返回给我们对应的状态码
    // 而后我们可以在响应拦截器中，根据状态码进行一些统一的操作。
    //   config.data = qs.stringify(config.data) // 序列化,比如表单数据

    // if (localStorage.accessToken && config.url !='/v1/login') { //判断token是否存在
    //     config.headers.Authorization ='Bearer '+ localStorage.accessToken;  //将token设置成请求头
    // }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  // 请求成功
  (res): any => {
    if (res.status === 200) {
      // 0000--未找到用户信息(这个主要是服务器重启了);100--用户未登录;101--在别处登录;102--token失效
      // if(res.data.errorCode == 100 || res.data.errorCode == 101 || res.data.errorCode == 102 || res.data.errorCode == '0000'){//未登录
      if (
        [100, 101, 102, 103, 104, 105, 401].includes(parseInt(res.data.code))
      ) {
        //未登录

        tip(res.data.message);
        // localStorage.accessToken=''
        toLogin();
        return Promise.reject(res.data);
      } else {
        return Promise.resolve(res.data);
      }
    }
  },
  // 请求失败
  (error) => {
    console.log(error, "https");

    // errorHandler(error)
  }
);
export default instance;
