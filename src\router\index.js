import {
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from "vue-router";
// import home from "../view/home/<USER>";
// import exportCenter from "@/view/exportCenter/index.vue";
// import dxfview from "@/view/dxfview/index.vue";
// import sketch from "@/view/sketch/index.vue";
// import sketchtest from "@/view/sketchtest/index.vue";
// import JSZip from "@/view/JSZip/index.vue";

// import ol from "@/view/ol/index.vue";
// import olmap from "@/view/mapBox/indexOL.vue";


import MapBoxListPage from "@/view/mapBox/listPage/index.vue";
// import MapBoxDetailPage from "@/view/mapBox/detailPage/index.vue";

const router = createRouter({
  // mode: 'history',
  history: createWebHashHistory(),
  routes: [
    {
      path: "/",
      name: "home",
      cn: "主页",
      // redirect: '/mapBox', // or component: MapBoxListPage, //TODO: 切记这个路由别提交到远程分支
      component: MapBoxListPage,
      // component: home,
    },
    {
      path: "/exportCenter",
      name: "exportCenter",
      cn: "导出中心",
      component: import("@/view/exportCenter/index.vue"),
    },
    {
      path: "/dxfview",
      name: "dxfview",
      cn: "渲染",
      component: import("@/view/dxfview/index.vue"),
    },
    {
      //打包测试
      path: "/sketchtest",
      name: "sketchtest",
      cn: "渲染",
      component: import("@/view/sketchtest/index.vue"),
    },
    {
      //本地测试
      path: "/sketch",
      name: "sketch",
      cn: "渲染",
      component: import("@/view/sketch/index.vue"),
    },
    {
      //压缩工具
      path: "/JSZip",
      name: "JSZip",
      cn: "渲染",
      component: import("@/view/JSZip/index.vue"),
    },
    {
      //压缩工具
      path: "/mapBox",
      name: "mapBox",
      cn: "地图",
      component: import("@/view/mapBox/listPage/index.vue"),
    },
    {
      path: "/viewMapBoxDetail",
      name: "viewMapBoxDetail",
      cn: "地图详情页",
      component: import("@/view/mapBox/detailPage/index.vue"),
    },
    {
      //压缩工具
      path: "/ol",
      name: "ol",
      cn: "地图",
      component: import("@/view/ol/index.vue"),
    },
    {
      //压缩工具
      path: "/olmap",
      name: "olmap",
      cn: "地图",
      component: import("@/view/mapBox/indexOL.vue"),
    },
  ],
});

export default router;
