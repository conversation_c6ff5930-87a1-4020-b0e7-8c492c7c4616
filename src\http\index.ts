/**
 * axios封装
 * 请求拦截、响应拦截、错误统一处理 { AxiosInstance }
 */
import axios, { AxiosRequestConfig, AxiosRequestHeaders, Method ,AxiosInstance} from 'axios';
// import router from "../router/index";
import { ElMessage } from 'element-plus'

//
var tip=console.log
var toLogin=function(){
  // router.push('/login')
}

// 异常拦截处理器
const errorHandler = (error:any) => {
    console.log('异常处理', error)
    let message = ''
    if (error && error.response) {
      switch (error.response.status) {
        case 302:
          message = '接口重定向！'
          break
        case 400:
          message = '参数不正确！'
          break
        case 401:
          // localStorage.accessToken='' //清除token

          message = '您未登录，或者登录已经超时，请先登录！'
          break
        case 403:
          message = '您没有权限操作！'
          break
        case 404:
          message = `请求地址出错: ${error.response.config.url}`
          break
        case 408:
          message = '请求超时！'
          break
        case 409:
          message = '系统已存在相同数据！'
          break
        case 500:
          message = '服务器内部错误！'
          break
        case 501:
          message = '服务未实现！'
          break
        case 502:
          message = '网关错误！'
          break
        case 503:
          message = '服务不可用！'
          break
        case 504:
          message = '服务暂时无法访问，请稍后再试！'
          break
        case 505:
          message = 'HTTP 版本不受支持！'
          break
        default:
          message = '异常问题，请联系管理员！'
          break
      }
    }
    ElMessage({ message: message,  type: 'error'})
    return Promise.reject(message)
}

// 创建axios实例
// @ts-ignore
// var instance :AxiosInstance = axios.create({timeout: 60000,baseURL:import.meta.env.VITE_BASE_URL});
var instance :AxiosInstance = axios.create({timeout: 60000});
// 设置post请求头
instance.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=utf-8';
instance.defaults.headers.get['Content-Type'] = 'application/x-www-form-urlencoded';
// axios.defaults.baseURL ='http://101.37.172.90:8080'
// axios.defaults.baseURL =



/**
 * 请求拦截器
 * 每次请求前，如果存在token则在请求头中携带token
 */

instance.interceptors.request.use(
    config => {
        // 登录流程控制中，根据本地是否存在token判断用户的登录情况
        // 但是即使token存在，也有可能token是过期的，所以在每次的请求头中携带token
        // 后台根据携带的token判断用户的登录情况，并返回给我们对应的状态码
        // 而后我们可以在响应拦截器中，根据状态码进行一些统一的操作。
         //   config.data = qs.stringify(config.data) // 序列化,比如表单数据

        // if (localStorage.accessToken && config.url !='/v1/login') { //判断token是否存在
        //     config.headers.Authorization ='Bearer '+ localStorage.accessToken;  //将token设置成请求头
        // }

        return config;
    },
    error => {return Promise.reject(error)}
)

// 响应拦截器
instance.interceptors.response.use(
    // 请求成功
    (res):any => {
        if(res.status === 200){
            // 0000--未找到用户信息(这个主要是服务器重启了);100--用户未登录;101--在别处登录;102--token失效
            // if(res.data.errorCode == 100 || res.data.errorCode == 101 || res.data.errorCode == 102 || res.data.errorCode == '0000'){//未登录
            if([100,101,102,103,104,105,401].includes(parseInt(res.data.code))){//未登录

                tip(res.data.message);
                // localStorage.accessToken=''
                toLogin();
                return Promise.reject(res.data);
            }else {
                return Promise.resolve(res.data);
            }
        }
    },
    // 请求失败
    error => {
      console.log(error,'https');

        // errorHandler(error)
    }
);
export default instance;