import * as THREE from "three"

import { KeyboardKey } from "dxf-viewer/src/controls"
import { DrawState, globalDxf, pickupInfo,poptip } from "dxf-viewer"
import { singal } from "emitter"
import { DrawHelp } from "./drawHelp"
export class Mirror extends DrawHelp {
    public MIds : any
    public rect : any
    public isPickObj : any//是否需要pickobj
    public isSelectFirstPt : any//是否已经点下第一点
    public firstPoint : any//记录第一点坐标
    
    constructor(viewer,ids) {
        super(viewer)
        this.MIds = ids;
        this.isPickObj = false;
        this.isSelectFirstPt = false;
        this.init()
    }
    public async init(){
        await this.pick0({tip:'请选择对象'});
        this.calBBox(); 
        let a=await this.pick1({tip:'绘制第一个点'});
        let b=await this.pick2({tip:'绘制第二个点'}); 
        let c=await this.pick3({tip:'是否保留'});
        poptip.dispose()
    }
    public async pick0(opt){
        poptip.tip({tip:'请选择对象'})
        if(this.MIds.length == 0){
            this.isPickObj = true;
            await this.select()
        }else{
            await 1
        }
    }

    public async pick1(opt){//选择第一个点
        poptip.tip(opt)
        return new Promise((resolve)=>{
            var func=(event)=>{
                this.isSelectFirstPt = true;
                this.firstPoint = this.getEventPoint(event)
                singal.pickPoint.remove(func)
                resolve([])
            }
            singal.pickPoint.add(func);
        })
    }
    public async pick2(opt){
        poptip.tip(opt)
        return new Promise((resolve)=>{
            var func=(event)=>{
                this.addObjects(this.getEventPoint(event))
                singal.pickPoint.remove(func)
                resolve([])
            }
            singal.pickPoint.add(func)
        })
    }
    public async pick3(opt){
        poptip.tip(opt)
        return new Promise((resolve)=>{
            this.onCancel()
            resolve([])  
           
        })
    }

    public calBBox(){
        let box =this.rect= new sg.Rect2d()
        this.MIds.forEach((v)=>{
			let pObj = this.cadCtx.getPBaseObjById(v)
			let tempBox = pObj.getBoundingBox()
			box.add(tempBox)
		})
       
    }
    //消息返回false表示可以往下路由，返回true表示在这个节点截止不再往下路由
    // 左键按下
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    public override onRButtonDown( event:PointerEvent ) : boolean{
        this.endSelect()
        return true;
    }
        //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.isSelectFirstPt){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }
        //键盘按下
    public override onKeyDown(event:KeyboardEvent) : boolean{
        if(event.key == KeyboardKey.Escape) {//取消
            this.onCancel()
        }else if(event.key == KeyboardKey.Space){
            this.endSelect()
        }
        return true;
    }
    //绘制临时图元
    private drawTempGraphic(secpoint):void{
        this.clearTempGeometry()
        let geometry1 =new THREE.BufferGeometry().setFromPoints([this.firstPoint,secpoint]);
        let material1 = new THREE.LineBasicMaterial({ color:'red'});
        let line1 = new THREE.Line(geometry1, material1);
        this.tempGeometry.push(line1)
        let ma = new sg.Matrix3()
        ma.mirror(new sg.LineSegment2d(this.threePointToSgPoint(this.firstPoint),this.threePointToSgPoint(secpoint)))
        let vecPoints = this.rect.getPointsVec()
        let n = vecPoints.size()
        let mirrorPts:any[] = []
        for(let i = 0 ; i < n ;i++)
        {
            let pt = vecPoints.get(i)
            pt.transform(ma)
            mirrorPts.push(new THREE.Vector3(pt.x(),pt.y(),0))
        }
        mirrorPts.push(mirrorPts[0])
        let geometry2 = new THREE.BufferGeometry().setFromPoints(mirrorPts);
        let material2 = new THREE.LineBasicMaterial({ color:'green'});
        let line2 = new THREE.Line(geometry2, material2);
        this.tempGeometry.push(line2)
        this.showTempGeometry()
    }
    //最后一步添加图元
    private addObjects(secpoint) : void{
        let ma = new sg.Matrix3
        let createObjs:any[] = []
        ma.mirror(new sg.LineSegment2d(this.threePointToSgPoint(this.firstPoint),this.threePointToSgPoint(secpoint)))
        this.MIds.forEach((id)=>{
            let obj = this.cadCtx.getPBaseObjById(id)
            let newOne = obj.copyNewOne()
            newOne.transform(ma)
            createObjs.push(newOne)
        })
        this.viewer.transaction.add(createObjs)
    }

    private endSelect(){//按空格键，确定
        if(this.isPickObj){//pick完成
            this.MIds = pickupInfo.pickupIdlist
            if(this.MIds.length == 0){
                console.log('未选取到对象,请重新选择')
            }else{
                singal.selectObj.dispatch()
                
                this.isPickObj = false
            }
        }
        else{
            this.onCancel()
        }
    }

}