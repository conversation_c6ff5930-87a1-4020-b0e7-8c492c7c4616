import { GripPoint } from "./GripCommon";
import { GripDragReply } from "./GripDragReply";
import * as THREE from "three";
//生成直线夹点
export function createEllipseGripPoints(obj) {
  let resPoints: GripPoint[] = [];
  let tpts = obj.selectPoints();
  let size = tpts.size();
  for (let i = 0; i < size; i++) {
    let temp = tpts.get(i);
    let strType = temp.getString();
    let index = 0;

    if (strType == "center") {
      index = 0;
    } else if (strType == "zeroPoint") {
      index = 1;
    } else if (strType == "quaterPoint") {
      index = 2;
    } else if (strType == "halfPoint") {
      index = 3;
    } else if (strType == "threeQuatersPoint") {
      index = 4;
    }
    let ppt1 = temp.getPoint();
    let gpoint = new GripPoint(ppt1, obj.getDrawObjectId(), index);
    resPoints.push(gpoint);
  }
  return resPoints;
}

//拖拽线段
export class DragEllipseReply extends GripDragReply {
  public oriLine: any;
  public objCurve: any;
  public copyCurve: any;

  constructor(viewer, gpt) {
    super(viewer, gpt);
    let group = this.viewer.transaction.gpObj;
    let obj = group.getPBaseObjById(this.gPoint.objId);
    this.objCurve = sg.SGObjTool.convertToCurve(obj);
    this.copyCurve = this.objCurve.copyNewOne();
    this.oriLine = this.objCurve.getCurve();
  }

  //消息返回false表示可以往下路由，返回true表示在这个节点截止不再往下路由
  // 左键按下
  public override onLButtonDown(event): boolean {
    let mousePt = this.getEventPoint(event);
    let pt = this.threePointToSgPoint(mousePt);
    let group = this.viewer.transaction.gpObj;
    group.getTransactionManager().beginTransaction();
    if (this.gPoint.gripIndex == 0) {
      this.objCurve.setEllipseCenterPoint(pt);
    } else if (this.gPoint.gripIndex == 1||this.gPoint.gripIndex == 3) {
      this.objCurve.setEllipseMajorPoint(pt);
    } else if (this.gPoint.gripIndex == 2||this.gPoint.gripIndex == 4) {
      this.objCurve.setEllipseMinorPoint(pt);
    } 
    group.getTransactionManager().commit();
    super.onLButtonDown(event);
    return true;
  }
  //鼠标移动
  public override onMouseMove(event): boolean {
    this.clearTempGeometry();
    super.onMouseMove(event);
    let mousePt = this.getEventPoint(event);
    let pt = this.threePointToSgPoint(mousePt);
    let material1 = new THREE.LineBasicMaterial({ color: "yellow" });
    let material2 = new THREE.LineBasicMaterial({ color: "grey" });

    if (this.gPoint.gripIndex == 0) {
      this.copyCurve.setEllipseCenterPoint(pt);
    } else if (this.gPoint.gripIndex == 1||this.gPoint.gripIndex == 3) {
      this.copyCurve.setEllipseMajorPoint(pt);
    } else if (this.gPoint.gripIndex == 2||this.gPoint.gripIndex == 4) {
      this.copyCurve.setEllipseMinorPoint(pt);
    } 
    let Ellipse = this.copyCurve.getCurve();
    let num = 50 * (1 + Ellipse.getMajorRadius());
    let points = Ellipse.getPoints(num);

    let newPoints: THREE.Vector2[] = [];
    let n = points.size();
    for (let i = 0; i < n; i++) {
      let value = points.get(i);
      newPoints.push(new THREE.Vector2(value.x(), value.y()));
    }

    let geometry1 = new THREE.BufferGeometry().setFromPoints(newPoints);
    let line1 = new THREE.Line(geometry1, material1);
    this.tempGeometry.push(line1);

    let pt3 = new THREE.Vector3(
      this.gPoint.gPoint.x(),
      this.gPoint.gPoint.y(),
      0
    );
    let geometry2 = new THREE.BufferGeometry().setFromPoints([mousePt, pt3]);
    let line2 = new THREE.Line(geometry2, material2);
    this.tempGeometry.push(line2);

    this.showTempGeometry();
    return true;
  }
}

export function createEllipseDragReplyByGripPoint(gpoint, viewer) {
  let res = new DragEllipseReply(viewer, gpoint);
  return res;
}
