import { mitts, btnFnObj } from "sgdraw/mittBus";
import { ElLoading, ElMessage } from "element-plus";

import { apiTask, apiTaskDetail } from "@/http/buildingBlockApi";
import { uploadfileFn } from "sgdraw/util/index";
import { globalDxf } from "dxf-viewer/src";
import JSZip, { JSZipGeneratorOptions } from "jszip";
import * as fflate from "fflate";
export class LoadDxf {
  public upload() {
    // 使用示例
    var input = document.createElement("input");
    // 设置input元素的属性
    input.type = "file"; // 设定为文件上传类型
    input.style.display = "none";
    document.body.appendChild(input);

    // 添加事件监听器，当文件选择后读取文件内容
    input.addEventListener("change", async (event) => {
      globalDxf.filenam = "";
      var file = event.target.files[0];
      const extension = file.name.split(".").pop().toLowerCase();
      if (file) {
        if (extension === "dxf") {
          mitts.emit(btnFnObj.lsfn, file);
          globalDxf.filenam = file.name;
          return;
        }
        let fileLoading = this.loading("正在上传中，请稍等...");
        const newFile = await this.compressFile(file);
        console.log(newFile);
        const ossData = await uploadfileFn([newFile]);
        await this.handleDwgFile({
          file,
          fileLoading,
          ossData,
          newFile,
        });
        // if (extension === "dxf") {
        //   let tempFile = await this.decompression(newFile); //解压上传的文件压缩后的zip
        //   mitts.emit(btnFnObj.lsfn, tempFile);
        //   globalDxf.filenam = tempFile.name;
        //   fileLoading.close();
        // } else {
        //   await this.handleDwgFile({
        //     file,
        //     fileLoading,
        //     ossData,
        //     newFile,
        //   });
        // }

        input.parentNode.removeChild(input);
      }
    });
    input.click();
  }
  private async compressFile(file: File) {
    try {
      const zip = new JSZip();
      zip.file(file.name, file);
      const ZP: JSZipGeneratorOptions = {
        type: "blob",
        compression: "DEFLATE",
        compressionOptions: { level: 9 },
      };
      const content = await zip.generateAsync(ZP);
      const tempFile = new File([content as Blob], "temp.zip", {
        type: "application/zip",
      });
      // console.log(URL.createObjectURL(tempFile));
      return tempFile;
    } catch (error) {
      console.log(error);
    }
  }
  public async decompression(file) {
    // const content = await file.arrayBuffer();
    // let JSZip1=new JSZip()
    // const zip = await JSZip1.loadAsync(content);

    // const tempFiles = await Promise.all(
    //   Object.keys(zip.files).map(async (relativePath) => {
    //     const zipEntry = zip.files[relativePath];
    //     const content = await zipEntry.async("blob");//改变原来的值

    //     return new File([content], zipEntry.name, {
    //       type: zipEntry._data.uncompressedSize,
    //     });
    //   })
    // );
    // // 处理 tempFiles，这里可以返回或使用它们
    // return tempFiles[0]

    try {
      const zipData = await file.arrayBuffer(); // 使用fflate解压

      const unzipped = fflate.unzipSync(new Uint8Array(zipData)); // 将解压后的文件转换为二进制File对象

      for (const [fileName, fileData] of Object.entries(unzipped)) {
        const blob = new Blob([new Uint8Array(fileData as ArrayBuffer)], {
          type: "application/octet-stream",
        });
        const unzippedFile = new File([blob], fileName, {
          type: "application/octet-stream",
        }); // debugger;
        console.log(`解压成功: ${fileName}`, unzippedFile);
        return unzippedFile;
      }
    } catch (error) {
      console.log("解压失败" + error);
    }
  }
  /**
   * 监听是否解析完成
   * @param id
   * @returns
   */
  private async analysisCompleted(id: string): Promise<string> {
    return new Promise((resolve) => {
      const monitor = setInterval(() => {
        apiTaskDetail(id).then((detailRes: any) => {
          if (detailRes.code == 200) {
            if (detailRes.data.status.toUpperCase() === "FAILED") {
              clearInterval(monitor);
              ElMessage({ message: "解析失败,DWG文件被加密！", type: "error" });
              resolve(""); // 返回 url
            }
            if (detailRes.data.status.toUpperCase() === "SUCCESS") {
              clearInterval(monitor);
              resolve(detailRes.data.resultDownloadUrl); // 返回 url
            }
          }
        });
      }, 1000);
    });
  }
  // 把url转成file对象
  private async urlToDxfFile(url: string): Promise<File> {
    const response = await fetch(url);
    const blob = await response.blob();
    return new File([blob], "temp.zip", {
      type: "application/zip",
    });
  }
  private loading(text: string) {
    return ElLoading.service({
      lock: true,
      text: text,
      background: "rgba(0, 0, 0, 0.7)",
    });
  }
  private async handleDwgFile({ file, fileLoading, ossData, newFile }) {
    apiTask({
      name: file.name || "tempFile",
      sourceData: ossData.storageFileId,
      taskType: "dwgzip2dxfzip",
    }).then(async (res: any) => {
      if (res.code !== 200) {
        fileLoading.close();
        return ElMessage({ message: res.message, type: "error" });
      }
      let sourceId = res.data.id;
      // let sourceId = "23da1afe3a094878b8ca358075caae8f";
      fileLoading.close();
      fileLoading = this.loading("上传成功，正在解析中，请稍等...");
      let tempFile = await this.decompression(newFile); //解压上传的文件压缩后的zip
      const fileUrl = await this.analysisCompleted(sourceId); //持续监听是否解析完成
      if (!fileUrl) {
        return fileLoading.close();
      }
      const newZip = await this.urlToDxfFile(fileUrl); //把网络资源转成File资源

      const newDxfFile = await this.decompression(newZip);
      tempFile = newDxfFile;
      // debugger;
      mitts.emit(btnFnObj.lsfn, tempFile);
      globalDxf.filenam = tempFile.name;
      fileLoading.close();
    });
  }
}
export const uploadFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject(new Error("No file selected."));
      return;
    }
    const reader = new FileReader();
    reader.onload = function (event) {
      // 文件读取成功，内容存储在event.target.result中
      resolve(event.target.result);
    };
    reader.onerror = function (error) {
      reject(error);
    };
    // 读取文件为文本
    reader.readAsText(file);
  });
};
