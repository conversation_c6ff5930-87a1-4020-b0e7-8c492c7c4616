/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    DxfViewer: typeof import('./components/DxfViewer.vue')['default']
    Properties: typeof import('./components/properties.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RowItem: typeof import('./components/rowItem.vue')['default']
    Sgcolorpicker: typeof import('./components/colorpicker/sgcolorpicker.vue')['default']
    Sglinetypepicker: typeof import('./components/colorpicker/sglinetypepicker.vue')['default']
    Switchbtn: typeof import('./components/switchbtn.vue')['default']
    TCard: typeof import('tdesign-vue-next')['Card']
    TCol: typeof import('tdesign-vue-next')['Col']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    TImage: typeof import('tdesign-vue-next')['Image']
    TInput: typeof import('tdesign-vue-next')['Input']
    TLoading: typeof import('tdesign-vue-next')['Loading']
    TPagination: typeof import('tdesign-vue-next')['Pagination']
    TRow: typeof import('tdesign-vue-next')['Row']
    TTag: typeof import('tdesign-vue-next')['Tag']
  }
}
