import signals from "signals";
export interface IEvtMsg{
    msg:""
}
export class DxfSignals {
    static defualt: DxfSignals = new DxfSignals()
    public test:signals.Signal<any> = new signals.Signal();
    public tragetRender: signals.Signal<any> = new signals.Signal();
    public selectObj: signals.Signal<any> = new signals.Signal();
    public pickPoint: signals.Signal<any> = new signals.Signal();
    public cadmousemove: signals.Signal<any> = new signals.Signal();
    public cadkeyup:signals.Signal<any> = new signals.Signal();
}

export const singal=DxfSignals.defualt
