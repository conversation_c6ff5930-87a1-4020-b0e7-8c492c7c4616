<template>
    <div class="mapBox">
        <Head :exportPageIsShow="false">
            <template #title style="font-size: 30px;">
                iSolarBuild-施工进度检查(demo)
            </template>
        </Head>
        <div class="view">
          <DxfViewer ref="mapViewers" :fonts="fonts"
              @dxf-loaded="_OnLoaded" @dxf-cleared="_OnCleared" @dxf-message="_OnMessage">
          </DxfViewer>
          <div class="mapboxviewer" ref="mapboxviewer" v-show="visMAP"></div>
          <div class="control_btns mapbox">
              <span class="btn" style="margin-right: 10px;" @click="onLoadMapbox"> {{ map.tip }} </span>
              <div v-show="map.tip == '关闭地图'" style="display: flex;margin-right: 10px;">
                  <span class="btn" @click="loadTiff"> 加载tiff </span>
                  <span class="btn" @click="loadShp"> 识图结果 </span>
                  <span class="btn" @click="syncView"> 视图联动 </span>
                  <span class="btn" @click="freezeMap"> 冻结地图 </span>
                  <span class="btn" @click="freezeCAD"> 冻结CAD </span>
                
                  <span class="btn" @click="extractData"> 提取数据 </span>
                  <span class="btn" @click="recognition"> 识图 </span>
                  <span class="btn" @click="showDiff"> 结果对比 </span>
                  <span class="btn" @click="closeTiff"> 隐藏tiff </span>
                  <span class="btn" @click="closeShp"> 隐藏识图 </span>
              </div>
          </div>
          <div class="control_btns cad" style="margin-top: 10px;">
            <span class="btn" @click="hideCAD"> 隐藏CAD </span>
            <span class="btn" @click="hideMAP"> 隐藏地图 </span>
            <span class="btn" @click="grayCAD"> 底图置灰 </span>
            <span class="btn" @click="ungrayCAD"> 反色 </span>
          </div>
          <div class="control_btns llh" >
            <span style="color: white;"> 层级: </span><el-input style="width: 100px;" v-model="map.zoom">  </el-input>
            <span style="color: white;"> 经度: </span><el-input style="width: 100px;" v-model="map.lng">  </el-input>
            <span style="color: white;"> 纬度: </span> <el-input style="width: 100px;" v-model="map.lat">  </el-input>
            <span class="btn" @click="locLngLat"> 定位 </span>
          </div>
        </div>
        <div class="resbox" v-if="resboxFlag">
          <H3 style="text-align: center;color: #fff;margin-bottom: 20px;">施工统计</H3>
          <div class="information"><span>总数量：</span> <span>{{resTotal}}</span> </div>
          <div class="information"><span>识别总数：</span> <span>{{identifyNum}}</span></div>
          <div class="information"><span>进度：</span> <span>{{ progress }}%</span></div>
        </div>
    </div>
</template>

<!-- name: 'mapBox' -->
<script setup>
import Head from '../home/<USER>/head.vue'
import DxfViewer from '../../components/DxfViewer.vue'

import { mitts, btnFnObj, mittEvents } from 'sgdraw/mittBus'
import { generateFileMD5 } from '@/util/index'
import { dxfZsObj,globalDxf,screen2wcs,GrayColorType } from 'dxf-viewer'

// 地图同步
import { OlViewerSync  } from 'src/mapviewsync'
import axios from 'axios'
import mapboxgl from 'mapbox-gl';
import { Transaction } from "dxf-viewer/src/global/transaction"

const fonts = ref([
//   mainFont, aux1Font, aux2Font, aux3Font
])
const dxfObj = reactive({
  layers: null,
  inputFile: null,
  isLocalFile: false,
  aboutDialog: false,
  urlDialog: false,
  inputUrl: null
})
const mapViewers = ref(null)
const dxfUrl = ref(null)
const visMAP=ref(true)
let shp
const map = reactive({
  tip: '加载地图',
  lng:120,
  lat:30,
  zoom:1
})
let cadmapviewer
let cadapp
const  mapboxviewer=ref(null)
const resboxFlag=ref(false)
const resTotal=ref(0)
const identifyNum=ref(0)
const progress=ref(0)


mitts.on(btnFnObj.lsfn, async (file) => {
  resboxFlag.value=false
  if (dxfUrl.value && dxfObj.isLocalFile) {
    URL.revokeObjectURL(dxfUrl.value)
  }
  let id = await generateFileMD5(file)
  dxfZsObj.drawingId = id
  dxfObj.isLocalFile = true
  dxfObj.inputFile = file
  dxfUrl.value = URL.createObjectURL(file)
  globalDxf.filenam =''
  await mapViewers.value?.Load(dxfObj.inputFile)
})

// 地图视图联动
const onLoadMapbox=()=>{
  // console.log('加载地图视图联动组件...');
  resboxFlag.value=false
  if (!cadmapviewer) { 
    cadapp=mapViewers.value.GetViewer()
    cadmapviewer = new OlViewerSync(mapboxviewer.value, cadapp)
    // cadapp.backgroundAlpha = 0.1
    map.tip = '关闭地图'
  } else {
    if (map.tip == '关闭地图') {
      cadmapviewer.dispose()
      cadmapviewer=null
      // cadapp.backgroundColor = 0x000000
      // cadapp.backgroundAlpha = 1
      map.tip = '打开地图'
    } else {
      // cadapp.backgroundAlpha = 0.1
      map.tip = '关闭地图'
    }
  }
}
const loadShp=()=>{
  shp=cadmapviewer.loadShp({
    url:'maps/map1/output.geojson',
    lng:118.06548603514608,
    lat:37.90810810776659,
  })
}
const loadTiff=()=>{
  cadmapviewer.loadTiff({
    // lng: 107.61863708496094,
    // lat: 34.41399077791693,
    lng:118.06548603514608,
    lat:37.90810810776659,
    url: 'maps/map1/{z}/{x}/{y}.png',
    projId: 'aaa'
  })
}
const recognition=()=>{
  cadmapviewer.cadMoveView()
}
const extractData=async()=>{
  let newda=null
  await axios.get('maps/map1/output.geojson').then(async res=>{
    let data=res.data;
    newda=await data.features.map(e=>{
      return e.geometry.coordinates.map(pts=>{
        return pts.map(pt=>{
          let {x,y}=lnglat2wcs(...pt)
          return {x,y}
        })
      })
    })
  })
  return newda
}
const lnglat2wcs=(lng,lat)=>{
  const t = cadmapviewer.map.transform.clone();
  let lnglat=new mapboxgl.LngLat(lng,lat)
  let {x,y}=t.locationPoint(lnglat)
  let cad=mapViewers.value.GetViewer()
  let wcs=screen2wcs({offsetX:x, offsetY:y, target:mapboxviewer.value}, cad.camera)
  return wcs
}
const locLngLat=()=>{
  let {lng,lat}=map
  cadmapviewer.map.jumpTo({center:{lng,lat}})
}
const syncView = () => {
  cadmapviewer.syncView()
}
const freezeMap = () => {
  cadmapviewer.freezeMap()
}
const freezeCAD = () => {
  cadmapviewer.freezeCAD()
}
const showDiff = async() => {
  let sgBDG=Transaction.sgBDG
  let newda=await extractData()
  console.log(newda,'newda');
  let VPlList=new sg.vector_PolyLine2d()
  let wasmBulge = new sg.vector_double()
  newda.forEach(list=>{
    let pl=null
    list.forEach(ptlist=>{
      let VPlist=new sg.vector_Point2d()
      ptlist.forEach(pts=>{
        VPlist.push_back(new sg.Point2d(pts.x, pts.y))
      })
      pl=new sg.PolyLine2d(VPlist, wasmBulge, false)
    })
    
    VPlList.push_back(pl)
  })
  let ppc=new sg.ProgressCheck(sgBDG,VPlList)
  let vstrlist=new sg.vector_string()
  let arrstr=["Array_沾化-56-1","Array_沾化-56-10513" ,"Array_沾化-28-1" ]
  arrstr.forEach(str=>{
    vstrlist.push_back(str)
  })
  ppc.setPvBlocks(vstrlist);
  
	ppc.match();

  let total=ppc.getCADPvCount() //总数
  console.log(total,'total');
  
  let plist=ppc.getMatchCADIds()
  let size=plist.size()
  let ids=[]
  for(let i=0;i<size;i++){
    ids.push(plist.get(i))
  }
  let cad=mapViewers.value.GetViewer()
  cad.graymanage.includeIds=ids
  resboxFlag.value=true
  resTotal.value=total
  identifyNum.value=size
  let lsprogress = (size / total) * 100;
  progress.value= isNaN(lsprogress) ? 0 : lsprogress.toFixed(2);

}
const hideMAP=()=>{
  visMAP.value=!visMAP.value
}
const hideCAD=()=>{
  mapViewers.value.hideCAD()
}
const grayCAD=async ()=>{
  let cad=mapViewers.value.GetViewer()
  cad.graymanage.grayType=GrayColorType.Include
  cad.rebuildScene()
}
const ungrayCAD=async ()=>{
  let cad=mapViewers.value.GetViewer()
  cad.graymanage.grayType=GrayColorType.Exclude
  cad.rebuildScene()
}
</script>


<style scoped>
.mapBox {
  width: 100vw;
  height: 100vh;
  /* background-color: hsl(218, 17%, 27%); */
  overflow: hidden;

  .btn {
    display: block;
    position: relative;
    width: 80px;
    height: 31px;
    opacity:0.8;
    cursor: pointer;
    color: #fff;
    background-image: url(img/btn.jpeg);
    background-position: center center; /* 或者直接写 center */
    background-repeat: no-repeat; /* 防止图像重复 */
    background-size: cover; /* 根据你的需求调整图片大小，也可以使用 contain */
    font-size: 12px;
    text-align: center;
    line-height: 31px;
    margin-right: 10px;
    &:hover {
      color: blue;
    }
  }

  .resbox {
    position: absolute;
    top: 200px;
    left: 20px;
    width: 200px;
    height: 200px;
    padding: 10px;
    background-color: #7fa9d3;
    border-radius:10px;
    z-index: 10003;
    opacity:0.75;
    .information {
      width: 100%;
      font-size: 20px;
      color: #fff;
      line-height: 40px;
      display: flex;
      justify-content: space-between;

    }
  }
  

  .view {
    width: 100vw;
    height: calc(100vh - 80px);
    position: relative;
    .mapboxviewer{
      position: absolute;
      top: 0px;
      left: 0px;
      height: 100%;
      width: 100%;
      z-index: 999;
    }
    .control_btns{
      position: absolute;
      z-index: 1001;
      display: flex;
    }
    .mapbox{
      top: 0px;
      left: 0px;
    }
    .cad{
      top: 30px;
      left: 0px;
    }
    .llh{
      top: 0px;
      right: 0px;
    }
  }

}

</style>
  