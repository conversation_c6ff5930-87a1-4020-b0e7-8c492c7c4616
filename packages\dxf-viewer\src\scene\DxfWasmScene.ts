
import { Matrix3, Scene, Vector2 } from "three"
import { textR<PERSON> } from "../entities"
import { BatchEntityType } from '../constant'
import "../patterns"
import { DxfParseScene } from "./DxfParseScene"
import { globalDxf } from "../global/dxfImpGroup"
import { <PERSON><PERSON>, RenderBatch } from "../batch"
import { DxfIdScene } from "./DxfIdScene"
import { dxflayers,modifylayerObj,pickupInfo,dxfZsObj, initDxfBaseData } from "../global"
import { Layer } from "../viewer/layer"
import { Block } from "../entities/Block"
import { TDiff } from "../libs/timediff"
import { getLineStyleUniforms } from "../viewer/DxfMaterials" 
import {gpCountLayer} from '../libs/utils'

/** This class prepares an internal representation of a DXF file, optimized fo WebGL rendering. It
 * is decoupled in such a way so that it should be possible to build it in a web-worker, effectively
 * transfer it to the main thread, and easily apply it to a Three.js scene there.
 */
export class DxfWasmScene  extends DxfParseScene{
    public scene:Scene//three场景
    public idScene:DxfIdScene

    constructor(viewer,options?) {
        super(viewer,options)
        this.scene=new Scene()
        this.idScene=new DxfIdScene(this)
    }

    async loadDxf(dxf, fontFetchers) {
        await textRender.init(dxf, this.options.textOptions)

        /* Scan all entities to analyze block usage statistics. */
        let gp= dxf.getDrawGroup()
        if(dxfZsObj.dxfBoxselection.length) {
            let p1= new sg.Point2d( dxfZsObj.dxfBoxselection[0].x, dxfZsObj.dxfBoxselection[0].y)
            let p2=new sg.Point2d( dxfZsObj.dxfBoxselection[1].x, dxfZsObj.dxfBoxselection[1].y)
            gp=sg.getRectDrawingGroup(dxfZsObj.drawingId,p1,p2)
            p1.delete()
            p2.delete()  
        }

        // pickupInfo.bluePrintCount=gp.count()
        pickupInfo.bluePrintCount=gp.getMaxImportObjCount()
        console.log(gp.count(),'gp.getMaxImportObjCount');
        pickupInfo.baseMapLayers=gpCountLayer(gp)

        
        if(pickupInfo.exportDxfText) {
            gp.appendCutOffString(pickupInfo.exportDxfText)
            modifylayerObj(pickupInfo.layerObj)
        }

        globalDxf.isMetric=dxf.isMetric();
        console.log('单位:',globalDxf.isMetric)

        if(!this.viewer.transaction) {
            this.viewer.createdGpwasm()
        }
        
        this.viewer.transaction.gpObj=gp
    }

    public async rebuildSceneDa(){
        await initDxfBaseData(this.viewer.transaction.gpObj)//初始化图层
        dxflayers.layers.forEach(layer=>{
            this.viewer.layers.set(layer.name,new Layer(layer.name, layer.displayName, layer.color,toRaw(layer.rgb) ))
        })
        globalDxf.miss.linestyles.clear()
        Object.assign(globalDxf.lineStyleUniforms, getLineStyleUniforms())
        let gp:any=this.viewer.transaction.gpObj
        // console.log( Date.now(),'111111 Date.now()');
        
        // processing blocks and inserts
        // 1. for each inserts, update the block's use count
/** 
        const insertCount = gp.insertBlockCount();
        let blockInsertCount = {};
        for (let i = 0; i < insertCount; i++) {
            const insert = gp.getInsertBlock(i);
            const insertRefedBlockName = insert.getReferredBlockName();
            if (!blockInsertCount.hasOwnProperty(insertRefedBlockName)) {
                blockInsertCount[insertRefedBlockName] = 1;
            } else {
                blockInsertCount[insertRefedBlockName]++;
            }
        }
*/

        // 2. process blocks in current dxf
        // debugger
        const strVec = gp.getBlockAnalysisOlder();
        const vecSize = strVec.size();
        console.log('块数量',vecSize)
        for (let i = 0; i < vecSize; i++) {
            const blockName = strVec.get(i);
            const sgBlock = gp.getBlockByName(blockName);
            const isUsed=gp.isBlockUsed(blockName)
            if(!isUsed){
                // console.log('图块未使用:',blockName)
                continue
            }
            const blockEntityCount = sgBlock.count();
            const insertCount = sgBlock.insertBlockCount();
            //console.log(blockEntityCount,insertCount,blockName)
            if (blockEntityCount + insertCount > 0) { //  (blockInsertCount[blockName] > 0)
                const basePoint = sgBlock.getBasePoint();
                let newBlock = new Block({
                    position : {
                        x : basePoint.x(),
                        y : basePoint.y()
                    },
                    name : blockName, 
                    sgBlock : sgBlock
                });
                this.blocks.set(blockName, newBlock);
                let defContext = newBlock.DefinitionContext();
                // for each block in use, process entity with block's definition context
                // for (let j = 0; j < blockEntityCount; j++) {
                //     let a = sgBlock.getBaseObj(j);
                //     let entityId = a.getDrawObjectId();
                //     try{
                //         await this._ProcessWasmEntity(a, defContext);
                //     } catch(error){
                //         console.error("error with entity id: " + entityId + " in block " + i + " . Error code: " + error)
                //     }
                // }
                
                // for (let j = 0; j < insertCount; j++) {
                //     let a = sgBlock.getInsertBlock(j);
                //     try{
                //         this._ProcessInsertEntity(a, defContext);
                //     } catch(error){
                //         console.error("error with entity in block " + j + " . Error code: " + error)
                //     }
                // }

                let blockEntities=sgBlock.getBaseObjVec();
                for(let j=0,n=blockEntities.size(); j<n; j++){
                    let a=blockEntities.get(j)
                    try{
                        await this._ProcessWasmEntity(a, defContext);
                        
                    } catch(error){
                        console.error("error with entity id: " + j + " in block " + i + " . Error code: " + error)
                    }
                }
                
                // let insertBlocks=sgBlock.getInsertVec();
                // for(let j=0,n=insertBlocks.size(); j<n; j++){
                //     let a=insertBlocks.get(j)
                //     try{
                //         await this._ProcessInsertEntity(a, defContext);
                //     } catch(error){
                //         console.error("error with entity in block " + j + " . Error code: " + error)
                //     }
                // }
            }
            // TDiff.log(`块${i}时间`)
        }
        // TDiff.log('js块解析时间')
        // 3. for each inserts, process it with block's instance context
        // const gpCount = gp.count()
        // console.log('图元数量:',gpCount)
        // for (let i = 0; i < gpCount; i++) {
        //     // console.log(i)
        //     let a = gp.getBaseObj(i);
        //     await this._ProcessWasmEntity(a)
        // }
        let objs=gp.getBaseObjVec()
        console.log('图元数量:',objs.size())
        for(let i=0,n=objs.size();i<n;i++){
            let o=objs.get(i)
            // console.log(o.getObjType())
            await this._ProcessWasmEntity(o)
        }
        // TDiff.log('js图元解析时间')
        this._BuildSceneDa()
        // TDiff.log('js装配时间')
        TDiff.log('合批时间')
        if(globalDxf.miss.linestyles.size>0){
            console.log('线型未定义:',Array.from(globalDxf.miss.linestyles))
        }
    }

    //新增  解析一次entity,定位到需要更新的batch,删除旧的mesh,batch重新创建实例
    //删除  找到所属的batch,删除旧的mesh,所有entity全部重新解析,batch重新创建实例
    //修改  删除，新增
    //删除  只需要通过id删除，删除单个和多个
    //新增  直接通过绘图添加 entity
    //修改  只通过id操作
    //undoredo 只通过id来操作
    public async updateTransactions(opt){//先删除，最后添加，再修改
        let t1=performance.now()
        let sDelId=new Set(opt.del)
        let sAddId=new Set(opt.add)
        let sModId=new Set(opt.modify)
        let delIds=sDelId.union(sModId)
        let addIds=sAddId.union(sModId)
        await this.delEntityByIDs(Array.from(delIds))
        await this.addEntityByIDs(Array.from(addIds))
        console.log('增量刷新运行时间:',performance.now()-t1)
    }

    public async delEntityByIDs(ids){
        let batches:Set<RenderBatch>=new Set()
        ids.forEach(id => {
            batches=batches.union(this.idMapBatch.get(id)!)
        });
        this.removeBatchMeshes(batches)
        let newids=new Set()
        batches?.forEach((b)=>{
            newids=newids.union(b.ids)
            b.init()
        })
        ids.forEach(id=>{
            newids.delete(id)
        })
        ids.forEach(id => {
            let batches:Set<RenderBatch>=this.idMapBatch.get(id)!
            for(let batch of batches){
                batch.ids.delete(id)
            }
            this.idMapBatch.delete(id)
        });
        await this._ProcessEntityIds(newids)
        
        batches?.forEach(b=>{
            if(b.ids.size>0){
                this._LoadBatch(b)
                this.idScene._LoadBatch(b)
                
            }
        })
    }

    public async addEntityByIDs(ids){
        let gp:any=this.viewer.transaction.gpObj
        let batches:Set<any>=new Set()
        for(let id of ids){
            let entity=gp.getPBaseObjById(id)
            await this._ProcessWasmEntity(entity)
            batches=batches.union(this.idMapBatch.get(id)!)
        }
        this.removeBatchMeshes(batches)
        batches?.forEach(b=>{
            this._LoadBatch(b)
            this.idScene._LoadBatch(b)
        })
    }

    public async addEntities(entities:any[]){
        let t1=performance.now()
        let batches:Set<any>=new Set()
        for(let entity of entities){
            await this._ProcessWasmEntity(entity)
            let id=entity.getDrawObjectId()
            batches=batches.union(this.idMapBatch.get(id)!)
        }
        this.removeBatchMeshes(batches)
        batches?.forEach(b=>{
            this._LoadBatch(b)
            this.idScene._LoadBatch(b)
        })
        console.log('添加运行时间',performance.now()-t1)
    }

    public async delEntityByID(id){
        let batches=this.idMapBatch.get(id)
        this.removeBatchMeshes(batches)
        let newids=new Set()
        batches?.forEach(b=>{
            newids=newids.union(b.ids)
            b.init()
        })
        newids.delete(id)
        await this._ProcessEntityIds(newids)
        batches?.forEach(b=>{
            this._LoadBatch(b)
            this.idScene._LoadBatch(b)
        })
    }


    async _ProcessEntityIds(ids){
        let gp:any=this.viewer.transaction.gpObj
        for(let id of ids){
            let a=gp.getPBaseObjById(id)
            if(!a){
                continue
            }
            this._ProcessWasmEntity(a)
        }
    }
    public removeBatchMeshes(batches){
        batches?.forEach(b=>{
            let layer=this.viewer.layers.get(b.key.layerName)
            for(let mesh of b.meshes){
                layer?.RemoveObject(mesh)
                this.scene.remove(mesh)
            }
            for(let mesh of b.idMeshes){ 
                this.idScene.remove(mesh)

            }
        })
    }

    public loadBatch(){
        this.batches.each(b=>{
                this._LoadBatch(b)
                this.idScene._LoadBatch(b)
            }
        )
    }
    public _LoadBatch( batch) {
        if (batch.key.blockName !== null &&
            batch.key.geometryType !== BatchEntityType.BLOCK_INSTANCE &&
            batch.key.geometryType !== BatchEntityType.POINT_INSTANCE) {
            /* Block definition. */
            return
        }

        const objects = new Batch(this.viewer,  batch).CreateObjects()
        
        const layer = this.viewer.layers.get(batch.key.layerName)
        // console.log(batch,'batch');
        // console.log(batch.key.layerName,'batch.key.layerNamebatch.key.layerName');
        // console.log(layer,'layer');
        
        // debugger
        for (const obj of objects) {
            batch.meshes.add(obj)
            this.scene.add(obj)
            if (layer) {
                layer.PushObject(obj)
            }
        }
        
    }
    public override clear(){
        this.idScene.clear()
        this.scene.clear()
        this.batches.clear()
        super.clear();
    }
}
