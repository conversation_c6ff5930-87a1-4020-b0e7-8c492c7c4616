import instance from "../utils/axios.js";
import axios from "axios";
import md5 from "js-md5";
import { ElMessage } from "element-plus";
import { apiQueryUploadFileUrl } from "@/view/mapBox/api/buildingBlockApi.js";
// 获取API基础URL，从axios.js导入
import { API_URLS } from '../utils/axios.js';

/**
 * 上传文件到OSS
 * @param files 要上传的文件数组（通常只使用第一个文件）
 * @returns Promise 返回包含storageFileId的对象
 */
export function apiUploadFileToOSS(
  files: File[]
): Promise<{ storageFileId: string }> {
  return new Promise((resolve, reject) => {
    if (!files || files.length === 0) {
      reject(new Error("没有选择文件"));
      return;
    }

    let params = {
      name: files[0].name,
      size: files[0].size,
      md5: "",
    };

    const reader = new FileReader();
    reader.onloadend = function (e: any) {
      // 从事件对象中获取已读取的文件内容（转换为字节数组）
      const content = e.target.result;
      // 使用js-md5计算文件内容的MD5散列值
      params.md5 = md5(content as string);

      apiQueryUploadFileUrl(params)
        .then((res: any) => {
          if (res.code === 200) {
            if (res.data.url) {
              var customAxios = axios.create();
              customAxios.interceptors.request.use((config) => {
                // 删除Content-Type，仅针对PUT请求
                if (config.method === "put") {
                  config.headers["Content-Type"] = "";
                }
                return config;
              });

              customAxios
                .put(res.data.url, files[0])
                .then(() => {
                  let fileReader = new FileReader();
                  fileReader.readAsDataURL(files[0]);
                  fileReader.onload = () => {
                    resolve({ storageFileId: res.data.storageFileId });
                  };
                })
                .catch((error) => {
                  reject(error);
                });
            } else {
              resolve({ storageFileId: res.data.storageFileId });
            }
          }
        })
        .catch((error) => {
          reject(error);
        });
    };

    // 开始读取文件内容
    reader.readAsBinaryString(files[0]);
  });
}