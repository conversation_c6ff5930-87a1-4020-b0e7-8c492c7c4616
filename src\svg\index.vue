<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@sungrowpower.com
 * @Date: 2023-11-08 10:01:14
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>@sungrowpower.com
 * @LastEditTime: 2023-11-08 10:57:09
 * @FilePath: \standard-component-product-library\src\svg\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
.svgDiv(:style="styIcon")
  svg(aria-hidden="true" class="svg-icon")
      use(:xlink:href="symbolId" :fill="color" :stroke="color")
</template>
 
<script setup lang="ts">

interface Props {
  prefix?: string, // 利用TS：限定父组件传 either 的值
  iconClass: string,
  color?: string, // 未设置默认值，为 undefined
  size?: string|number,
  w?: string|number,
  h?: string|number,
}

const props =withDefaults( defineProps<Props>(),{
  prefix:  'icon',
  size:  22,
  w:0,
  h:0
  
});
const styIcon = computed(()=>{
  let w=props.w=='0'?props.size:props.w;
  let h=props.h=='0'?props.size:props.h
  return {
    width:w+'px',
    height:h+'px',
    lineHeight:h+'px'
  }
})
 
const symbolId = computed(() => `#${props.prefix}-${props.iconClass}`);
</script>
 
<style scoped>
.svgDiv{
  text-align: center;
  margin: auto;
}
.svg-icon {
  overflow: hidden;
  fill: currentColor;
  vertical-align:middle;
  width: 100%;
  height: 100%;
  line-height: 100%;
  margin: auto;
}
</style>