import{Vector2 as t,Vector3 as e}from"three";class s{constructor(){}t(t){this.i.push(t.clone());const e=this.o.length-1;this.o[e]++}h(t){const e=this.l();if(!e||!t.equals(e)){this.i.push(t.clone());const e=this.o.length-1;this.o.push(this.o[e]+1)}}u(t,e){const s=this.l();s&&t.equals(s)||this.h(t),this.t(e)}k(e,s,n,i,c,o,a,r,h){for(let r of this.i){let l=r.x*n,u=r.y*i;l+=u*a,l+=s.x,u+=s.y;const f=l*c-u*o,k=l*o+u*c,d=e.x+f,b=e.y+k,w=new t(d,b);h.push(w)}const l=r.length;let u=0;l>0?u=r[l-1]:r.push(0);const f=this.o.length;for(let t=1;t<f;t++)r.push(this.o[t]+u)}m(t){const e=t.length;if(0===e)return;const s=this.l();let n=0;if(t[0]===s){if(1===e)return;n++}for(let s=n;s<e;s++)this.i.push(t[s]);const i=this.o.length-1;this.o[i]=this.i.length}p(e,s,n){let i=[e,s];for(let e=0;e<3;e++){const e=(new t).subVectors(i[1],i[0]).length(),s=e/2*n,c=i.length-1,o=[i[0]];for(let n=0;n<c;n++){const c=i[n],a=i[n+1],r=(new t).subVectors(a,c),h=(new t).copy(r).divideScalar(e),l=new t(h.y,-h.x),u=(new t).addVectors(c,(new t).copy(r).divideScalar(2)),f=(new t).addVectors(l.multiplyScalar(s),u);o.push(f),o.push(a)}i=o,n=(-1+Math.sqrt(1+n*n))/n}this.u(i[0],i[1]);for(let t=2;t<9;t++)this.t(i[t])}T(t){const e=this.o.length,s=this.o[e-1];if(0===s)this.i=t.i,this.o=t.o;else{const e=t.o,n=e.length;for(let t=1;t<n;t++){const n=e[t]+s;this.o.push(n)}for(let e of t.i)this.i.push(e)}}l(){const t=this.i.length;if(t>0)return this.i[t-1]}o=[0];i=[]}class n{_(){return this.S.x}M(){return 0}width(){return this.end.x}C(){return this.S.y}F(){let t=this.B.y;return t=t>=0?0:-t,t}height(){return this.S.y}L(){return this.B.x}N(){return this.end.x-this.S.x}A(t,e,s,n,i,c,o,a,r,h){this.H.k(e,s,n,i,c,o,a,r,h),s.x+=this.end.x*n,s.y+=this.end.y*i}O=0;P=0;H=new s;end=new t;B=new t(1/0,1/0);S=new t(-1/0,-1/0);scale=1}const i=Object.freeze({W:0,j:1,D:2}),c=Object.freeze({v:0,R:2});function o(t,e,s){let n="";for(let i=e;i<s+length;i++){const e=t.getUint8(i);if(0===e)break;n+=String.fromCharCode(e)}return n}function a(t,e,s){const n=t.byteLength;for(let i=s;i<n;i++)if(t.getUint8(i)===e)return i;return-1}function r(t,e){return t?e/t.U():1}class h{I=new Map;q=!1;X=i.W;data;G=0;V=0;J=0;K=c.v;Y=0;$=!1;Z=0;constructor(t){this.data=t,this.tt()}et(){return this.q}tt(){this.G=this.data.byteLength;const t=o(this.data,11,18);t.includes("shapes")?(this.X=i.W,this.st(),this.q=!0):t.includes("bigfont")?(this.X=i.j,this.nt(),this.q=!0):t.includes("unifont")?(this.X=i.D,this.it(),this.q=!0):this.q=!1}U(){return this.V}ct(){return this.J}ot(t){const e=this.I.get(t);return 0===e?.H.i.length&&this.rt(e,!1),e}st(){const t=this.data.getUint16(28,!0);let e=30,s=e+4*t;for(let i=0;i<t;i++){const t=this.data.getUint16(e,!0),c=this.data.getUint16(e+2,!0);if(0===i)if(0!==t)this.V=1;else{const t=s+(c-4);this.V=this.data.getUint8(t),this.J=this.data.getUint8(t+1),this.K=this.data.getUint8(t+2)}if(0!==t){const e=a(this.data,0,s)-s+1,i=new n;i.P=c-e,i.O=s+e,this.I.set(t,i)}s+=c,e+=4}}nt(){let t=27;const e=this.data.getUint16(t,!0);t+=2;const s=this.data.getUint16(t,!0);t+=2;for(let e=0;e<s;e++)this.data.getUint16(t,!0),t+=2,this.data.getUint16(t,!0),t+=2;let i=t;for(let t=0;t<e;t++){const e=new Uint8Array(2);e[1]=this.data.getUint8(i),e[0]=this.data.getUint8(i+1),i+=2;const s=this.data.getUint16(i,!0);i+=2;const c=this.data.getUint32(i,!0);if(i+=4,t>0&&0===e[1]&&0===e[0])continue;const o=c,r=a(this.data,0,o)-o+1,h=s-r,l=o+r;if(0===t)5===h?(this.V=this.data.getUint8(l),this.data.getUint8(l+1),this.K=this.data.getUint8(l+2),this.Z=this.data.getUint8(l+3)):(this.V=this.data.getUint8(l),this.data.getUint8(l+1),this.K=this.data.getUint8(l+2)),0===this.V&&(this.V=8);else{const t=new TextDecoder("gbk");let s=0;s=0===e[0]?e[1]:t.decode(e).charCodeAt(0);const i=new n;i.P=h,i.O=l,this.I.set(s,i)}}}it(){let t=25;const e=this.data.getUint32(t,!0)-1;t+=4;const s=this.data.getUint16(t,!0);t+=2,t+=s,this.V=this.data.getUint8(t-6),this.J=this.data.getUint8(t-5),this.K=this.data.getUint8(t-4);for(let s=0;s<e;s++){const e=this.data.getUint16(t,!0);t+=2;const s=this.data.getUint16(t,!0);t+=2;const i=a(this.data,0,t)-t+1,c=new n;c.P=s-i,c.O=t+i,this.I.set(e,c),t+=s}}rt(e,s=!1){const n=e.O,i=new t;this.ht(n,[],!0,i,1,e,s),e.B.x===1/0&&(e.B.x=0),e.B.y===1/0&&(e.B.y=0),e.S.x===-1/0&&(e.S.x=e.end.x),e.S.y===-1/0&&(e.S.y=0)}lt(e,s,n,i,c){const o=this.data.getInt8(e+s+1),a=this.data.getInt8(e+s+2),r=this.data.getInt8(e+s+3),h=o*n,l=a*n,u=new t(i.x+h,i.y+l);if(0===r)this.u(!0,i,c,u);else{const t=r/127;c.H.p(i,u,t),i.copy(u),this.ut(i,c.S,c.B)}}ft(e,s,n,i,c,o,a){const r=new t(Math.cos(s),Math.sin(s)),h=new t(e.x-i*r.x,e.y-i*r.y);c.h(e),this.ut(e,o,a);const l=n/8;for(let e=1;e<=8;e++){const n=s+l*e,r=new t;r.x=h.x+i*Math.cos(n),r.y=h.y+i*Math.sin(n),c.t(r),this.ut(r,o,a)}const u=c.l();u&&e.copy(u)}kt(t){return{dt:!(128&t),s:(112&t)>>4,c:7&t}}bt(t,e){let s=0,n=0;const i=(t>>4)*e;switch(15&t){case 0:s=i;break;case 1:s=i,n=i/2;break;case 2:s=i,n=i;break;case 3:s=i/2,n=i;break;case 4:n=i;break;case 5:s=-i/2,n=i;break;case 6:s=-i,n=i;break;case 7:s=-i,n=i/2;break;case 8:s=-i;break;case 9:s=-i,n=-i/2;break;case 10:s=-i,n=-i;break;case 11:s=-i/2,n=-i;break;case 12:s=0,n=-i;break;case 13:s=i/2,n=-i;break;case 14:s=i,n=-i;break;case 15:s=i,n=-i/2}return{offsetX:s,offsetY:n}}ht(t,e,s,c,o,a,r=!1){const h=a.P,l=a.H;let u=!1;for(let f=0;f<h;f++){const h=this.data.getUint8(t+f);switch(h){case 0:u||(u=!1);break;case 1:u||(s=!0),u=!1;break;case 2:u||(s=!1),u=!1;break;case 3:u||(o/=this.data.getUint8(t+f+1)),u=!1,f+=1;break;case 4:u||(o*=this.data.getUint8(t+f+1)),u=!1,f+=1;break;case 5:u||e.push(c.clone()),u=!1;break;case 6:u||c.copy(e.pop()),u=!1;break;case 7:{let h=0;switch(this.X){case i.W:h=this.data.getUint8(t+f+1),f+=1;break;case i.j:h=this.data.getUint8(t+f+1),0===h&&(f+=6),f+=1;break;case i.D:h=this.data.getUint16(t+f+1,!1),f+=2}if(!u){const t=this.I.get(h);if(t){const i=new n,h=t.O;i.P=t.P;const u=this.ht(h,e,s,c,o,i,r);s=u.wt,o=u.scale,i.H.i.length>0&&(this.ut(i.S,a.S,a.B),this.ut(i.B,a.S,a.B)),l.T(i.H)}}u=!1;break}case 8:if(!u){const e=this.data.getInt8(t+f+1),n=this.data.getInt8(t+f+2),i=c.clone();i.x+=e*o,i.y+=n*o,this.u(s,c,a,i)}u=!1,f+=2;break;case 9:for(u||s&&(l.h(c),this.ut(c,a.S,a.B));;){const e=this.data.getInt8(t+f+1),n=this.data.getInt8(t+f+2);if(f+=2,0===e&&0===n)break;u||(c.x+=e*o,c.y+=n*o,s&&(l.t(c),this.ut(c,a.S,a.B)))}u=!1;break;case 10:if(!u){const e=this.data.getUint8(t+f+1),s=this.data.getUint8(t+f+2),n=this.kt(s),i=n.s*Math.PI*2/8;0===n.c&&(n.c=8),n.dt||(n.c=-n.c);const r=n.c*Math.PI*2/8,h=e*o;this.ft(c,i,r,h,a.H,a.S,a.B)}f+=2,u=!1;break;case 11:if(!u){let e=this.data.getUint8(t+f+1),s=this.data.getUint8(t+f+2);const n=256*this.data.getUint8(t+f+3);let i=this.data.getUint8(t+f+4);i+=n,i*=o;const r=this.data.getUint8(t+f+5),h=this.kt(r);0===h.c&&(h.c=8),0!==s&&(h.c=h.c-1),h.dt||(e=-e,s=-s,h.c=-h.c);const l=(45*e/256+45*h.s)/180*Math.PI,u=(45*s/256+45*(h.s+h.c))/180*Math.PI;this.ft(c,l,u-l,i,a.H,a.S,a.B)}f+=5;break;case 12:u||this.lt(t,f,o,c,a),f+=3,u=!1;break;case 13:for(;;){const e=this.data.getInt8(t+f+1),s=this.data.getInt8(t+f+2);if(0===e&&0===s)break;u||this.lt(t,f,o,c,a),f+=3}f+=2,u=!1;break;case 14:2!==this.K||r||(u=!0);break;default:if(!u){const t=this.bt(h,o),e=c.clone();e.x+=t.offsetX,e.y+=t.offsetY,this.u(s,c,a,e)}u=!1}}return a.end=c,a.scale=o,{wt:s,scale:o}}u(t,e,s,n){const i=s.H;t&&(i.h(e),i.t(n),this.ut(e,s.S,s.B),this.ut(n,s.S,s.B)),e.copy(n)}ut(t,e,s){t.x>e.x&&(e.x=t.x),t.y>e.y&&(e.y=t.y),t.x<s.x&&(s.x=t.x),t.y<s.y&&(s.y=t.y)}}const l="tssdeng.shx",u="tssdchn.shx",f=-1,k=-2,d=-3;class b{r=0;g=0;b=0;gt=0}class w{static#t=new Map;static async parse(t,e){const s=new h(e);s.et()&&w.#t.set(t,s)}static hasFont(t){return void 0!==w.#t.get(t)}static Tt(t){return w.#t.get(t)}}const m=Object.freeze({bottom:0,_t:1,top:2});class g{constructor(t,e){this.font=t,this.St=e}location=new e;yt=m.bottom;font;St;Mt=1;xt=new t;Ct=2.5;Ft=1;Bt=1;Lt=1;Nt=1;At=1;Ht=0;Ot=0;Pt=1;color=new b;vertical=!1;Wt=!1;jt=!1;Dt=!1;vt=!1;Rt=!1;Ut=[new e,new e];It=[new e,new e];qt=[new e,new e];Et=0;clone(){const t=Object.assign(Object.create(Object.getPrototypeOf(this)),this);return t.Bt=r(t.font,t.Ct),t.Lt=r(t.St,t.Ct),t.Nt=t.Bt*t.Ft,t.At=t.Lt*t.Ft,t}}class p{constructor(t,e){this.text=t,this.format=e}text;Xt=[];zt=[];format}const T=Object.freeze({text:0,Gt:1});function _(t,e,s){const n=e.length;if(n>0){const i=t.clone(),c=e.splice(0,n),o=new p(c,i);s.push(o)}}function S(t,e,s,n){t===T.text&&_(e,s,n)}function y(t,e,s,n,i,c,o){t[e];const a=e;switch(t[e+1]){case"O":return S(s,n,i,o),n.vt=!0,e+=1,{Vt:T.Gt,Qt:e};case"o":return S(s,n,i,o),n.vt=!1,e+=1,{Vt:T.Gt,Qt:e};case"L":return S(s,n,i,o),n.Dt=!0,e+=1,{Vt:T.Gt,Qt:e};case"l":return S(s,n,i,o),n.Dt=!1,e+=1,{Vt:T.Gt,Qt:e};case"K":return S(s,n,i,o),n.Rt=!0,e+=1,{Vt:T.Gt,Qt:e};case"k":return S(s,n,i,o),n.Rt=!1,e+=1,{Vt:T.Gt,Qt:e};case"~":return i.push(160),e+=1,{Vt:T.text,Qt:e};case"\\":const c="\\";return i.push(c.charCodeAt(0)),e+=1,{Vt:T.text,Qt:e};case"C":case"c":return S(s,n,i,o),e=t.indexOf(";",e),t.substring(a+2,e),{Vt:T.Gt,Qt:e};case"F":case"f":return S(s,n,i,o),e=t.indexOf(";",e),t.substring(a,e),n.font=w.Tt(l),n.St=w.Tt(u),{Vt:T.Gt,Qt:e};case"H":{S(s,n,i,o),e=t.indexOf(";",e);let c=t.substring(a+2,e);c.endsWith("x")||c.endsWith("X")?(c=c.slice(0,c.length-1),n.Ct*=Number(c)):n.Ct=Number(c)}return{Vt:T.Gt,Qt:e};case"S":case"T":case"p":return S(s,n,i,o),e=t.indexOf(";",e),t.substring(a,e),{Vt:T.Gt,Qt:e};case"X":{e+=1,i.push(f);const t=n.clone(),s=i.length,c=i.splice(0,s),a=new p(c,t);o.push(a)}return{Vt:T.Gt,Qt:e};case"Q":{S(s,n,i,o),e=t.indexOf(";",e);const c=t.substring(a,e),r=Number(c);n.Ht=r}return{Vt:T.Gt,Qt:e};case"W":{S(s,n,i,o),e=t.indexOf(";",e);const c=t.substring(a+2,e);n.Ft=Number(c)}return{Vt:T.Gt,Qt:e};case"A":{S(s,n,i,o);const c=t.substring(e,e+1);e=t.indexOf(";",e);const a=Number(c);n.yt=a}return{Vt:T.Gt,Qt:e};case"P":{e+=1,i.push(k);const t=n.clone(),s=i.length,c=i.splice(0,s),a=new p(c,t);o.push(a)}return{Vt:T.Gt,Qt:e};case"N":{e+=1;const t=n.clone();i.push(d);const s=i.splice(0,i.length),c=new p(s,t);c.format=t,o.push(c)}return{Vt:T.Gt,Qt:e};case"U":if("+"===t[e+2]){const s=e+3,n=s+4,c=t.substring(s,n),o=parseInt(c,16);if(!isNaN(o))return i.push(o),e+=6,{Vt:T.text,Qt:e}}return i.push(92),e+=1,{Vt:T.text,Qt:e};default:return i.push(92),e+=1,{Vt:T.text,Qt:e}}}function M(t,e){const s=t.length,n=[],i=[];let c=0;const o=[];let a=T.Gt;for(;;){if(c===s){_(e,o,i);break}const r=t[c],h=t[c+1];switch(r){case"\\":const s=y(t,c,a,e,o,n,i);a=s.Vt,c=s.Qt;break;case"{":{S(a,e,o,i),a=T.Gt;const t=e.clone();n.push(t)}break;case"}":{S(a,e,o,i),a=T.Gt;const t=n.pop();t&&(e=t)}break;case"^":switch(h){case"I":o.push(32,32,32,32),c+=2;continue;case"J":o.push(f);const t=e.clone(),s=o.length,n=o.splice(0,s),a=new p(n,t);i.push(a),c+=2;continue;default:o.push(32),c+=2;continue}a=T.text,o.push(r.charCodeAt(0));break;case"%":if("%"===t[c+1])switch(t[c+2]){case"d":case"D":c+=3,a=T.text,o.push(176);continue;case"p":case"P":c+=3,a=T.text,o.push(177);continue;case"c":case"C":c+=3,a=T.text,o.push(8709);continue}a=T.text,o.push(37);break;default:a=T.text,o.push(r.charCodeAt(0))}c++}return i}function x(t){const e=[],s=t.length;let n=0;for(;n<s;){const s=t[n];switch(s){case"%":if("%"===t[n+1])switch(t[n+2]){case"o":case"O":case"u":case"U":n+=3;continue;case"d":case"D":n+=3,e.push(176);continue;case"p":case"P":n+=3,e.push(177);continue;case"c":case"C":n+=3,e.push(8709);continue;case"%":n+=3,e.push(37);continue;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":{const s=n+2,i=s+3,c=t.substring(s,i),o=parseInt(c,10);if(!isNaN(o)){e.push(o),n+=5;continue}}}break;case"\\":if("U"===t[n+1]&&"+"===t[n+2]){const s=n+3,i=s+4,c=t.substring(s,i),o=parseInt(c,16);if(!isNaN(o)){e.push(o),n+=7;continue}}}n+=1,e.push(s.charCodeAt(0))}return e}const C=/\p{P}/u,F=/\p{Script=Han}/u,B=Object.freeze({Jt:0,Kt:1,Yt:2,$t:3,Zt:4,te:5}),L=Object.freeze({ee:0,se:1,ne:2,ie:3});new e(1,0,0);const N=new e(0,1,0),A=new e(0,0,1);function H(t){let s=new e;return Math.abs(t.x)<1/64&&Math.abs(t.y)<1/64?s.crossVectors(N,t):s.crossVectors(A,t),s.normalize(),s}const O=" §÷ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩαβγδεζηικλμμνξοπρστυχψω∈∞",P=new Set,W=52;for(let t=0;t<W;t++)P.add(O.charCodeAt(t));const j="?",D=63;function v(t,e,s,n,i){const c=[];let o=0,a=0,r=0;const h=t.length;for(let l=0;l<h;l++){if(!t.at(l))continue;let u=0,f=0,k=n;e[l]&&(k=i);const d=s[l];u=d.width(),f=d.height(),0===l&&(r=d.L()*k),l===h-1&&(u=d._()),u*=k,f*=k,c.push(u),o+=u,a=Math.max(a,f)}return{ce:c,oe:o,ae:a,L:r}}function R(t,e){const s=[];let n=0;const i=t.length;for(let c=0;c<i;c++)s[c]=t[c]*e,n+=s[c];return{re:s,he:n}}function U(t,e,s){let n,i=!1;return s&&P.has(t)&&(n=s.ot(t),n)?(i=!0,{le:i,ue:n,fe:t}):(n=e.ot(t),n?(i=!1,{le:i,ue:n,fe:t}):s&&(n=s.ot(t),n)?(i=!0,{le:i,ue:n,fe:t}):(n=e.ot(D),n?(i=!1,{le:i,ue:n,fe:t=D}):{le:i,ue:n,fe:t}))}class I{static ke;static de;static be=3;static we=B.Jt;static me=L.ee;static ge=0;static pe=0;static Te=0;static _e=1;static Se=0;static ye=0;static setFont(t,e){I.ke=w.Tt(t),e&&(I.de=w.Tt(e))}static setTextHeight(t){I.be=t}static setWidthFactor(t){I._e=t}static setHorizontalMode(t){I.we=t}static setVerticalMode(t){I.me=t}static setRotation(t){I.ge=t,I.pe=Math.cos(I.ge),I.Te=Math.sin(I.ge)}static setOblique(t){I.Se=t*Math.PI/180,I.ye=Math.tan(I.Se)}}class q extends I{static#e(t,e,s){const n=Array.from(t),i=t.length;for(let c=0;c<i;c++){const i=U(t[c],q.ke,q.de);e[c]=i.le,n[c]=i.fe,s[c]=i.ue}return n}static outputPolylines(e,s,n,i){const c=x(e),o=c.length,a=Array(o),h=Array(o),l=q.#e(c,a,h);let u=r(q.ke,q.be),f=r(q.de,q.be),k=u*this._e,d=f*this._e;R(v(l,a,h,u,f).ce,q._e);const b=s.clone(),w=new t(0,0);for(let t=0;t<l.length;t++){const e=l.at(t);if(e){let s=k,c=u;a[t]&&(s=d,c=f);const o=h[t];o.A(e,b,w,s,c,q.pe,q.Te,q.ye,n,i);let r=1;r=o.scale,k*=r,u*=r,d*=r,f*=r}}}}class E{Me=[];xe=G.Ce}class X{Fe=[];Be=.25;maxHeight=0;Le=0;F=0;Ne=0;Ae=0;L=0;N=0}class z{lines=[];He=0;height=0;Oe=0;start=0;end=0}const G=Object.freeze({Ce:0,Pe:1,We:2,je:3,De:4}),V=Object.freeze({ve:0,Re:1,Ue:2}),Q=Object.freeze({Ie:1,qe:2}),J=Object.freeze({Ee:1,Xe:2,ze:3,Ge:4,Ve:5,Qe:6,Je:7,Ke:8,Ye:9,$e:10,Ze:11,ts:12,es:13,ss:14,ns:15,cs:16,os:17,rs:18,hs:19,ls:20,us:21,fs:22,ks:23,ds:24}),K=String.raw`"!%)]};:"',.?/|！%……）-}】：；“‘》，。？、"`,Y=new Set,$=K.length;for(let t=0;t<$;t++)Y.add(K.charCodeAt(t));const Z=String.raw`￥……&（+{【《$({[\"'`,tt=new Set,et=Z.length;for(let t=0;t<et;t++)tt.add(Z.charCodeAt(t));const st=new Set([32,9]);class nt extends I{static#s=V.ve;static#n=0;static#i=!0;static#c=[0];static#o=0;static#a=1;static#r=Q.qe;static#h=J.$e;static#l=0;static setColumnType(t){this.#s=t}static setHeight(t){this.#n=t}static setColumnCount(t){this.#c.length=t}static bs(){return this.#c.length}static setColumnHeight(t,e){this.#c[t]=e}static setColumnAutoHeight(t){this.#i=t}static setColumnWidth(t){this.#o=t}static ws(t,e,s){this.#o=t,this.#l=e,this.#i=s}static gs(t,e,s){this.#o=t,this.#l=e,this.setColumnCount(s)}static width(){const t=this.bs();return this.#o*t+this.#l*(t-1)}static setLineSpacingFactor(t){this.#a=t}static setLineSpacingStyle(t){this.#r=t}static setColumnGutterWidth(t){this.#l=t}static setAttachment(t){switch(nt.#h=t,t){case J.Ee:this.we=B.Jt,this.me=L.ie;break;case J.Xe:this.we=B.Kt,this.me=L.ie;break;case J.ze:this.we=B.Yt,this.me=L.ie;break;case J.Ge:this.we=B.Jt,this.me=L.ne;break;case J.Ve:this.we=B.Kt,this.me=L.ne;break;case J.Qe:this.we=B.Yt,this.me=L.ne;break;case J.Je:this.we=B.Jt,this.me=L.se;break;case J.Ke:this.we=B.Kt,this.me=L.se;break;case J.Ye:this.we=B.Yt,this.me=L.se}}static outputMTextPolylines(t,s,n,i){const c=0===nt.#o?Number.MAX_VALUE:nt.#o,o=new g(nt.ke,nt.de);o.Ft=nt._e,o.Ct=nt.be,o.Ht=nt.Se,o.Mt=nt.#a;const a=M(t,o),r=nt.#u(a,c),h=new e(s.x,s.y,0),l=new e(0,0,1);let u=h;const f=H(l);f.applyAxisAngle(l,this.ge);const k=l.cross(f),d=r.length,b=(nt.#l+c)*(d-1);let w=0;this.we===B.Kt?w=b/2:this.we===B.Yt&&(w=b);const m=f.clone();m.multiplyScalar(w),u.sub(m);for(const t of r){t.Oe;const e=u.clone();let s=0;this.me===L.ne?s=t.height/2:this.me===L.se&&(s=t.height),e.addScaledVector(k,s),nt.#f(e,t,f,k,n,i),u.addScaledVector(f,c+nt.#l)}}static#f(e,s,n,i,c,o){const a=s.lines;for(const s of a){const a=e.clone();a.addScaledVector(i,-s.Ne);const r=a.clone(),h=s.Ae-s.L-s.N;switch(nt.we){case B.Kt:r.addScaledVector(n,-h/2);break;case B.Yt:r.addScaledVector(n,-h);case B.Jt:case B.$t:case B.Zt:case B.te:}r.addScaledVector(n,-s.L);const l=s.Fe,u=new t(r.x,r.y),f=new t(0,0);for(const t of l){const e=t.Me;for(const t of e){const e=t.Xt,s=t.text,n=t.format;for(let i=0;i<e.length;i++){const a=s.at(i);if(a){const s=t.zt[i];let r=n.Nt,h=n.Bt;s&&(r=n.At,h=n.Lt),e[i].A(a,u,f,r,h,nt.pe,nt.Te,n.Ot,c,o)}}}}}}static#k(t){return" "===t?G.je:C.test(t)?G.We:F.test(t)?G.Pe:G.Ce}static#d(t,e,s,n){const i=t[s].text,c=t[s].format;e.Me=t.splice(0,s);const o=i.splice(0,n);if(o.length>0){const t=new p(o,c);e.Me.push(t)}return 0===i.length&&t.splice(0,1),e}static#b(t,e,s){return this.#w(st,t,e,s)}static#m(t,e,s){return this.#w(Y,t,e,s)}static#w(t,e,s,n){const i=e.length;for(let c=s;c<i;c++){const i=e[c];i.format;const o=i.text,a=o.length;let r=0;for(c===s&&(r=n);r<a;r++){const e=o[r];if(e<0)return{ps:c,gt:r+1};if(!t.has(e))return{ps:c,gt:r}}}return{ps:i-1,gt:e[i-1].text.length}}static#g(t){const e=t.length,s=new E;let n=0;if(e>0){const e=t[0],i=e.text[0];if(n=i,i<0)return s.Me=t.splice(0,1),s;e.format;const c=String.fromCharCode(i);s.xe=nt.#k(c)}for(let i=0;i<e;i++){const e=t[i];e.format;const c=e.text,o=c.length;if(0===o)continue;let a=0;for(0===i&&(a=1);a<o;a++){const e=c[a];if(e<0)return nt.#d(t,s,i,a+1);const o=String.fromCharCode(e),r=nt.#k(o);switch(s.xe){case G.Ce:case G.We:if(r===G.Pe||r===G.je){if(tt.has(n)){const{ps:e,gt:n}=nt.#m(t,i,a+1);return nt.#d(t,s,e,n)}return nt.#d(t,s,i,a)}break;case G.Pe:switch(r){case G.je:let e=this.#b(t,i,a);const{ps:n,gt:c}=nt.#m(t,e.ps,e.gt);if(n>e.ps||n===e.ps&&c>e.gt)return nt.#d(t,s,n,c);case G.Ce:case G.Pe:return nt.#d(t,s,i,a);case G.We:{const{ps:e,gt:n}=nt.#m(t,i,a);return nt.#d(t,s,e,n)}}break;case G.je:if(r!==G.je)return nt.#d(t,s,i,a)}n=e}}return s.Me=t.splice(0,e),s}static#p(t){const e=t.Me.length;let s=0,n=0,i=0,c=0;for(let o=0;o<e;o++){const a=t.Me[o],r=a.text,h=a.Xt,l=a.zt,u=a.format,f=u.font,k=u.St,d=u.Ct,b=u.Ft;let w=r.length;const m=r[w-1];m<0&&(c=m,w-=1);for(let t=0;t<w;t++){const c=r[t],{fe:a,ue:u,le:m}=U(c,f,k);r[t]=a,h.push(u),l.push(m);let g=8;g=m?k.U():f.U();const p=d/g*b;0===o&&0===t&&(s=u.L()*p),o===e-1&&t===w-1&&(i=u.N()*p),n+=u.width()*p}}return t.xe===G.je&&(i=n),{Ae:n,L:s,N:i,Ts:c}}static#T(t,e){const s=[];let n=new X;for(;;){if(0===t.length)return 0!==n.Fe.length&&s.push(n),s;const i=this.#g(t);let{Ae:c,L:o,N:a,Ts:r}=this.#p(i);i.xe===G.je&&(a=n.N+a),0===n.Fe.length&&(n.L=o);const h=c-o-a;h>e?(0!==n.Fe.length&&(s.push(n),n=new X),n.L=o,n.Ae=c,n.N=a,n.Fe.push(i),s.push(n),n=new X):(n.Ae+h>e?(s.push(n),n=new X,n.Fe.push(i),n.L=o,n.Ae=c,n.N=a):(n.Fe.push(i),n.Ae=n.Ae+c,n.N=a),r<0&&(s.push(n),n=new X))}}static#_(t){for(const e of t){const t=e.Fe;for(const s of t){const t=s.Me;for(const s of t){const t=s.zt,n=s.Xt,i=s.format,c=t.length;e.Be=Math.max(e.Be,i.Mt),e.maxHeight=Math.max(e.maxHeight,i.Ct);for(let s=0;s<c;s++){const c=t[s],o=n[s];let a=1;a=c?i.Lt:i.Bt,e.Le=Math.max(e.Le,o.C()*a),e.F=Math.max(e.F,o.F()*a)}}}}}static#u(t,e){const s=[],n=nt.#T(t,e);for(nt.#_(n);0!==n.length;){let t=0,e=0;switch(nt.#s){case V.ve:t=nt.#n,e=Number.MAX_VALUE;break;case V.Re:t=nt.#n,e=s.length===nt.#c.length-1?Number.MAX_VALUE:nt.#n;break;case V.Ue:nt.#i?(t=nt.#n,e=nt.#n):s.length<=nt.#c.length?(t=nt.#c[s.length],e=t):(t=0,e=Number.MAX_VALUE)}0===e&&(e=Number.MAX_VALUE);const i=nt.#S(n,e);i.height=nt.#y(i),0===t&&(t=i.height),i.Oe=t;for(const t of i.lines)i.He=Math.max(i.He,t.Ae-t.L-t.N);s.push(i)}return s}static#S(t,e){let s=0;const n=t.length;let i=n,c=0,o=0;for(let a=0;a<n;a++){const n=t[a];let r=n.maxHeight*(5/3*n.Be-1),h=0;if(nt.#r===Q.Ie&&(h=Math.max(n.Le-n.maxHeight,h)),s+=n.maxHeight+c+h-o*(1-n.Be),s+n.F-e>1e-6){i=a;break}n.Ne=s,c=r,o=h}const a=new z;return a.lines=t.splice(0,i),a}static#y(t){const e=t.lines,s=e.length;let n=0;if(s>0){const t=e[s-1];n=t.Ne+t.F}return n}}export{J as AttachmentPoint,V as ColumnType,I as CommonProperty,Q as LineSpacingStyle,nt as MText,w as Shx,q as Text,B as TextHorzMode,L as TextVertMode};

