import { InstanceType } from "../constant"
export const vfs_point=(instanceType, pointSize)=>{
    const fullInstanceAttr = instanceType === InstanceType.FULL ?
        `
        /* First row. */
        in vec3 instanceTransform0;
        /* Second row. */
        in vec3 instanceTransform1;
        /* Second row. */
        in vec4 instanceColor;
        out vec4 vInstanceColor;
        ` : "";
    const fullInstanceTransform = instanceType === InstanceType.FULL ?
        `
        pos.xy = mat2(instanceTransform0[0], instanceTransform1[0],
                        instanceTransform0[1], instanceTransform1[1]) * pos.xy +
                    vec2(instanceTransform0[2], instanceTransform1[2]);
        vInstanceColor=instanceColor;
        ` : "";

    const pointInstanceAttr = instanceType === InstanceType.POINT ?
        `
        in vec2 instanceTransform;
        ` : "";
    const pointInstanceTransform = instanceType === InstanceType.POINT ?
        `
        pos.xy += instanceTransform;
        ` : "";

    const pointSizeUniform = pointSize ? "uniform float pointSize;" : ""
    const pointSizeAssigment = pointSize ? "gl_PointSize = pointSize;" : ""
    const fullInstanceColorAttr= instanceType === InstanceType.FULL ?   `
    in vec4 vInstanceColor;
    ` : ``;
    const fullInstanceColor = instanceType === InstanceType.FULL ?   `
    int mod=int(vEntityColor.x);//
    int mod2=int(vInstanceColor.x);
    if(mod==2){//固定色
        vColor=vEntityColor.yzw/255.;
    }
    if(mod==1){
        vColor=layerColor;
        if(mod2==0){
            vColor=vInstanceColor.yzw/255.;
        }
        if(mod2==2){
            vColor=vInstanceColor.yzw/255.;
        }
    }
    if(mod==0){
        vColor=layerColor;
        if(mod2==0){
            vColor=vInstanceColor.yzw/255.;
        }
    }
    ` : `
    vColor=vEntityColor.yzw/255.;
    `;
    const vertex= `
        precision highp float;
        precision highp int;
        in vec2 position;
        in uint entityId;
        flat out uint entId;
        in vec4 entityColor;
        out vec4 vEntityColor;
        ${fullInstanceAttr}
        ${pointInstanceAttr}
        uniform mat4 modelViewMatrix;
        uniform mat4 projectionMatrix;
        
        ${pointSizeUniform}

        void main() {
            entId=entityId;
            vEntityColor=entityColor;
            vec4 pos = vec4(position, 0.0, 1.0);
            ${fullInstanceTransform}
            ${pointInstanceTransform}
            gl_Position = projectionMatrix * modelViewMatrix * pos;
            ${pointSizeAssigment}
        }
        `
    const fragment= `
        uniform sampler2D pointTexture;
        precision highp float;
        precision highp int;
        uniform vec3 color;
        out vec4 fragColor;
        flat in uint entId;
        in vec4 vEntityColor;
        uniform int neid;
        uniform uint eids[100];
        uniform vec3 layerColor;
        uniform int isHL;
        ${fullInstanceColorAttr}
        bool isInArray(uint element, uint arr[100], int arrlen) {
            for (int i = 0; i < arrlen; i++) {
                if (arr[i] == element) {
                    return true;  // 找到元素，返回 true
                }
            }
            return false;  // 如果未找到，返回 false
        }

        void main() {
            vec3 vColor=color;
            if(neid>0){
                if(!isInArray(entId, eids, neid)){
                    discard;
                }
            }
            ${fullInstanceColor}
            if(isHL==1){
                vColor=color;
            }
            vec4 c = vec4( vColor, 1.0 ) * texture( pointTexture, gl_PointCoord );
            fragColor = c;
        }
        `
    return {vertex,fragment}
}
