import { createLineDragReplyByGripPoint, createLineGripPoints } from "./dragLine";
import { createCircleDragReplyByGripPoint, createCircleGripPoints } from "./dragCircle";
import { createComBinCurveDragReplyByGripPoint, createComBinCurveGripPoints } from "./dragComBinCurve";
import { createArcDragReplyByGripPoint, createArcGripPoints } from "./dragArc";
import { createEllipseDragReplyByGripPoint, createEllipseGripPoints } from "./dragEllipse";
import { createEllipseArcDragReplyByGripPoint, createEllipseArcGripPoints } from "./dragEllipseArc";
import { createBSplineDragReplyByGripPoint, createBSplineGripPoints } from "./dragBSpline";


//夹点信息类
export class GripPoint{
    public gPoint : sg.Point2d;  //夹点坐标
    public objId : number; //对应的obj的id
    public gripIndex :number; //夹点特征值

    constructor(pt,id,index){
        this.gPoint = pt
        this.objId = id
        this.gripIndex = index
    }
}

//根据图元id，生成图元对应的夹点数组
export function createGripPointsById(id,group){
    let resPoints:GripPoint[] = []
    const obj=group.getPBaseObjById(id)
    if(!obj) return resPoints
    const type =obj.getObjType()
    if(type == 'SGObjCurve'){
        let objCurve = sg.SGObjTool.convertToCurve(obj)
        let lineType = objCurve.getCurveType()
        if(lineType == 'LineSegment2d'){
            return createLineGripPoints(obj)
        }
        else if(lineType == 'Arc2d'){
            return createArcGripPoints(obj)
        }else if(lineType == 'Circle2d'){
            return createCircleGripPoints(obj)
        }else if(lineType == 'Ellipse2d'){
            return createEllipseGripPoints(obj)
        }else if(lineType == 'EllipseArc2d'){
            return createEllipseArcGripPoints(obj)
        }else if(lineType == 'BSpline2d'){
            return createBSplineGripPoints(obj)
        }
    }
    else if(type == 'SGObjComBinCurve'){
        return createComBinCurveGripPoints(obj)
    }
    else if(type == 'SGObjAlignDim'){

    }
    return resPoints
}

//根据图元夹点生成对应的夹点拖拽响应类
export function createDragReplyByGripPoint(gpoint,viewer){
    let group = viewer.transaction.gpObj
    const obj=group.getPBaseObjById(gpoint.objId)
    if(!obj) return 
    const type =obj.getObjType()
    if(type == 'SGObjCurve'){
        let objCurve = sg.SGObjTool.convertToCurve(obj)
        let lineType = objCurve.getCurveType()
        if(lineType == 'LineSegment2d'){
            return createLineDragReplyByGripPoint(gpoint,viewer)
        }
        else if(lineType == 'Arc2d'){
            return createArcDragReplyByGripPoint(gpoint,viewer)
        }else if(lineType == 'Circle2d'){
            return createCircleDragReplyByGripPoint(gpoint,viewer)
        }else if(lineType == 'Ellipse2d'){
            return createEllipseDragReplyByGripPoint(gpoint,viewer)
        }else if(lineType == 'EllipseArc2d'){
            return createEllipseArcDragReplyByGripPoint(gpoint,viewer)
        }else if(lineType == 'BSpline2d'){
            return createBSplineDragReplyByGripPoint(gpoint,viewer)
        }
    }
    else if(type == 'SGObjComBinCurve'){
        return createComBinCurveDragReplyByGripPoint(gpoint,viewer)
    }
}