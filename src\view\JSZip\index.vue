<template>
  <div class="jszip-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>文件上传</span>
        </div>
      </template>
      <div class="file-list">
        <el-tag type="success" v-for="(file, index) in fileList" closable>
          {{ file.name }}
        </el-tag>
      </div>
      <div class="file-upload">
        <input type="file" ref="fileRef" multiple @change="fileChange" class="file-input" />
        <el-button type="primary" size="default" @click="uploadFile">文件上传</el-button>
      </div>


      <div class="jz-tabs">
        <el-tabs @tab-click="changeTab">
          <el-tab-pane label="文件上传">
            <el-button type="primary" plain size="default" @click="toZip">压缩</el-button>
          </el-tab-pane>
          <el-tab-pane label="文件解压">
            <el-button type="success" plain size="default" @click="unZip">解压</el-button>
          </el-tab-pane>
        </el-tabs>
      </div>

    </el-card>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { ref, getCurrentInstance } from 'vue'
import JSZip from 'jszip';
import { saveAs } from "file-saver"; // 用于保存压缩后的文件

const fileRef = ref('fileRef')
const fileList = ref([])
const { proxy } = getCurrentInstance()
function uploadFile() {
  fileRef.value.click()
}
// 根据文件名和文件大小判断是否重复文件
function filterFileByNameAndSize(files) {
  const fileKeys = new Set(fileList.value.map(f => `${f.name}-${f.size}`));
  for (const file of files) {
    const key = `${file.name}-${file.size}`;
    if (fileKeys.has(key)) {
      return ElMessage.warning(`${file.name}文件已存在,请重新上传`);
    }
  }
  fileList.value.push(...files);
}

function fileChange(e) {
  const files = e.target.files
  filterFileByNameAndSize(files)
}
async function toZip() {
  console.log('toZip')
  const files = fileList.value
  if (files.length === 0) {
    ElMessage.warning("请先选择文件！");
    return;
  }
  const zip = new JSZip();
  for (let file of files) {
    zip.file(file.name, file);
  }
  try {
    const content = await zip.generateAsync({ type: "blob", compression: "DEFLATE", compressionOptions: { level: 9 } });
    saveAs(content, timeStamp() + ".zip"); // 保存压缩后的 zip 文件
    fileList.value = [];
    proxy.$refs.fileRef.value = "";
    ElMessage.success("压缩成功！");

  } catch (err) {
    ElMessage.error("压缩文件时出错，请重试！", err);
    return;
  }
}
async function unZip() {
  console.log('unZip')
  const files = fileList.value
  // const file = fileList.value[0]
  try {
    files.forEach(async (file) => {
      const content = await file.arrayBuffer();
      const zip = await JSZip.loadAsync(content); // 异步加载 ZIP 文件

      zip.forEach(async (relativePath, zipEntry) => {
        const content = await zipEntry.async("blob");
        // 更新解压文件列表
        saveAs(content, relativePath + timeStamp());
      });
    })
  } catch (err) {
    console.error("解压文件时出错：", err);
  }
}
function timeStamp() {
  return '?t=' + new Date().getTime()
}

function changeTab() {
  fileList.value = []
  proxy.$refs.fileRef.value = "";
}

onMounted(() => {
  console.log(JSZip);
})

</script>
<style lang="scss" scoped>
.jszip-container {
  padding: 20px;
  display: flex;
  gap: 20px;
  justify-content: center;
  // align-items: center;
  // height: calc(100vh - 100px);

  .el-card {
    // flex: 1;
    width: 500px;

    .file-input {
      display: none;
    }

    .file-list {
      margin-bottom: 10px;

      .el-tag {
        margin-top: 8px;
        // min-width: 200px;
        // max-width: 500px;
        display: block;
      }
    }
  }
}
</style>