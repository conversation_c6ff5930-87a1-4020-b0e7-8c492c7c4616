export declare class Shx {
    #private;
    static parse(fontName: string, fontdata: DataView): Promise<void>;
    static hasFont(fontName: string): boolean;
}
import { Vector2 } from 'three';

export declare const TextHorzMode: Readonly<{
    kTextLeft: 0;
    kTextCenter: 1;
    kTextRight: 2;
    kTextAlign: 3;
    kTextMid: 4;
    kTextFit: 5;
}>;
type TextHorzModeKeys = keyof typeof TextHorzMode;
type TextHorzModeValues = typeof TextHorzMode[TextHorzModeKeys];
export declare const TextVertMode: Readonly<{
    kTextBase: 0;
    kTextBottom: 1;
    kTextVertMid: 2;
    kTextTop: 3;
}>;
type TextVertModeKeys = keyof typeof TextVertMode;
type TextVertModeValues = typeof TextVertMode[TextVertModeKeys];
export declare class CommonProperty {
    static setFont(fontName: string, bigfontName: string | undefined): void;
    static setTextHeight(textHeight: number): void;
    static setWidthFactor(widthFactor: number): void;
    static setHorizontalMode(hmode: TextHorzModeValues): void;
    static setVerticalMode(vmode: TextVertModeValues): void;
    static setRotation(rotation: number): void;
    static setOblique(oblique: number): void;
}
export declare class Text extends CommonProperty {
    #private;
    static outputPolylines(textstr: string, position: Vector2, counts: number[], points: Vector2[]): void;
}
export declare const ColumnType: Readonly<{
    kNoColumns: 0;
    kStaticColumns: 1;
    kDynamicColumns: 2;
}>;
type ColumnTypeKeys = keyof typeof ColumnType;
type ColumnTypeValues = typeof ColumnType[ColumnTypeKeys];
export declare const LineSpacingStyle: Readonly<{
    kAtLeast: 1;
    kExactly: 2;
}>;
type LineSpacingStyleKeys = keyof typeof LineSpacingStyle;
type LineSpacingStyleValues = typeof LineSpacingStyle[LineSpacingStyleKeys];
export declare const AttachmentPoint: Readonly<{
    kTopLeft: 1;
    kTopCenter: 2;
    kTopRight: 3;
    kMiddleLeft: 4;
    kMiddleCenter: 5;
    kMiddleRight: 6;
    kBottomLeft: 7;
    kBottomCenter: 8;
    kBottomRight: 9;
    kBaseLeft: 10;
    kBaseCenter: 11;
    kBaseRight: 12;
    kBaseAlign: 13;
    kBottomAlign: 14;
    kMiddleAlign: 15;
    kTopAlign: 16;
    kBaseFit: 17;
    kBottomFit: 18;
    kMiddleFit: 19;
    kTopFit: 20;
    kBaseMid: 21;
    kBottomMid: 22;
    kMiddleMid: 23;
    kTopMid: 24;
}>;
type AttachmentPointKeys = keyof typeof AttachmentPoint;
type AttachmentPointValues = typeof AttachmentPoint[AttachmentPointKeys];
export declare class MText extends CommonProperty {
    #private;
    static setColumnType(colType: ColumnTypeValues): void;
    static setHeight(height: number): void;
    static setColumnCount(numCol: number): void;
    static getColumnCount(): number;
    static setColumnHeight(col: number, colHeight: number): void;
    static setColumnAutoHeight(bAutoHeigh: boolean): void;
    static setColumnWidth(colWidth: number): void;
    static setDynamicColumns(width: number, gutter: number, bAutoHeigh: boolean): void;
    static setStaticColumns(width: number, gutter: number, count: number): void;
    static width(): number;
    static setLineSpacingFactor(lineSpacingFactor: number): void;
    static setLineSpacingStyle(lineSpacingStyle: LineSpacingStyleValues): void;
    static setColumnGutterWidth(colGutter: number): void;
    static setAttachment(type: AttachmentPointValues): void;
    static outputMTextPolylines(content: string, position: Vector2, counts: number[], points: Vector2[]): void;
}
