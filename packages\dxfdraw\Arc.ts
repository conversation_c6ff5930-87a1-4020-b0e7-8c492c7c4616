import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial,EllipseCurve, Vector3,Line as ThLine } from "three"

export class Arc extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    public arc:any
    // public color:string='white'
    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        console.log(this.color,'this.color');
        
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.arc=new sg.Arc2d()
        this.init()
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        let p1=await this.lengthPick({
            tip:'指定第二个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p1)
        let p2=await this.lengthPick({
            tip:'指定第三个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p2)
        this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }else if(this.pnts.length==1){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }else if(this.pnts.length==2){
            let secpt = this.getEventPoint(event)
            this.drawMoveArc(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
    }
    public drawMoveArc(v:Vector3){
        let sgPointList=this.setWasmVPlist([...this.pnts,v])
        var points = this.getvertices(sgPointList);
        this.mesh.geometry=new BufferGeometry().setFromPoints(points);
    }
    
    complete(){
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        if(this.pnts.length<2) return
        let plist=this.setWasmVPlist(this.pnts)
        let gparc=new sg.Arc2d(plist[0],plist[1],plist[2])
        let els:any=new sg.SGObjCurve()
        els.setpCurve(gparc)
        let [r,g,b]=this.color
        els.setColor(new sg.SGColor(r,g,b,255))
        els.setLineType(this.lineType)
        this.viewer.transaction.add(els)
        globalDxf.operation=GraphicType.None
        globalDxf.isdraw = false
        this.isSnap=false
        setTimeout(()=>{
            globalDxf.drawstate=DrawState.Pick
        })
    }

    private getvertices(vList:any) {
        this.arc.set(...vList)
        const radius =this.arc.getRadius()
        const startAngle =this.arc.getStartAngle()
        const endAngle=this.arc.getEndAngle()
        const center=this.arc.getCenter()
        const anticlockwise=this.arc.isAntiClock()
        var curve = new EllipseCurve(center.x(), center.y(), 
                                            radius,radius, startAngle,endAngle,
                                            !anticlockwise,0);
        let num=50*(1+radius)                                    
        var points = curve.getPoints(num);
        return points
    }
    private setWasmVPlist(vList){
        let {x,y}=globalDxf.wcsOff
        const wasmVPlist:any=[]
        vList.forEach(item=>{
            wasmVPlist.push(new sg.Point2d(item.x+x, item.y+y))
        })
        return wasmVPlist
    }
}