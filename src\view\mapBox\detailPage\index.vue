<template>
  <div class="data-management" v-loading.fullscreen.lock="loading" element-loading-text="加载中...">
    <Header class="header--detail-wrap">
      <template #controlButton>
        <ControlBtnList @click="handleControlClick" @uploadCAD="handleUploadCAD" @handleUploadTIF="handleUploadTIF" />
      </template>
    </Header>
    <main class="content-wrap">
      <olBox ref="olBoxRef" @cadTexts="getCadTexts" @exportList="initial"></olBox>
    </main>
    <!-- 文件上传对话框 -->
    <el-dialog v-model="uploadDialogVisible" :title="uploadType === 'cad' ? 'CAD图纸上传' : 'TIF图像上传'" width="50%">
      <div class="upload-container">
        <FileUploader ref="fileUploaderRef" :buttonText="uploadType === 'cad' ? '选择CAD图纸' : '选择TIF图像'"
          :fileType="uploadType" :projectCode="projectCode" @handleUpload="handleFileUploaded" />
        <div class="upload-note" v-if="uploadType === 'cad'">
          <p>
            <el-icon>
              <InfoFilled />
            </el-icon>
            CAD图纸使用标准上传方式
          </p>
        </div>
        <div class="upload-note" v-else-if="uploadType === 'tif'">
          <p>
            <el-icon>
              <InfoFilled />
            </el-icon>
            TIF图像使用大文件分片上传方式，支持断点续传
          </p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="jumpSure">确定</el-button>
          <el-button @click="uploadDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <CoordinateInputDialog v-model:visible="coordinateDialogVisible" @confirm="updateProjectCoordinates"
      @close="coordinateDialogVisible = false" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import olBox from "../indexOL.vue";
import Header from "@/view/mapBox/components/mapBoxHeader.vue";
import ControlBtnList from "@/view/mapBox/components/controlBtnList.vue";
import FileUploader from "../components/fileUploader.vue";
import CoordinateInputDialog from "../components/coordinateInputDialog.vue";
import { useProjectStore } from "../store";
import { apiUpdateProjectFile } from "../api/listPageApi.js";
import { apiGetProjectDetail } from "../api/detailPageApi.js";
import { createTxtFileFromString, goDifyWorkFlow } from "../utils/difyUpLoad";
import useProjectBuild from "./hooks/useProjectBuild.js";

const route = useRoute();
const projectStore = useProjectStore();
const loading = ref(false);
const projectCode = ref("");
const olBoxRef = ref(null);
const updateData = ref({
  cadUrl: "",
  tiffUrl: "",
});

const { initial } = useProjectBuild(olBoxRef)

// 上传相关
const uploadDialogVisible = ref(false);
const uploadType = ref(""); // 'cad' 或 'tif'
const fileUploaderRef = ref(null);
const coordinateDialogVisible = ref(false);

let abortController = null;

// 处理控制按钮点击
const handleControlClick = (item) => {
  console.log("点击了控制按钮:", item);
};

// 处理CAD上传按钮点击
const handleUploadCAD = () => {
  if (!projectCode.value) {
    ElMessage.warning("请先选择项目");
    return;
  }
  uploadType.value = "cad";
  uploadDialogVisible.value = true;
  fileUploaderRef.value.resetUpload();
};

// 处理TIF上传按钮点击
const handleUploadTIF = () => {
  if (!projectCode.value) {
    ElMessage.warning("请先选择项目");
    return;
  }
  uploadType.value = "tif";
  uploadDialogVisible.value = true;
  fileUploaderRef.value.resetUpload();
};

// 文件上传完成回调
const handleFileUploaded = async (fileInfo) => {
  // 关闭对话框
  uploadDialogVisible.value = false;

  // 更新项目文件信息
  if (fileInfo && fileInfo.filePath && fileInfo.fileType) {
    if (fileInfo.fileType === "cad") {
      updateData.value.cadUrl = fileInfo.filePath;
    } else if (fileInfo.fileType === "tif") {
      updateData.value.tiffUrl = fileInfo.filePath;
    }
    loading.value = true;

    // try {
    //   if (Object.keys(updateData).length > 0) {
    //     loading.value = true;
    //     const res = await apiUpdateProjectFile(projectCode.value, updateData.value);
    //     if (res.api_status && res.code === 200) {
    //       ElMessage.success(`${fileInfo.fileType.toUpperCase()}文件信息已更新`);
    //     } else {
    //       ElMessage.error(`更新${fileInfo.fileType.toUpperCase()}文件信息失败`);
    //     }
    //   }
    // } catch (error) {
    //   ElMessage.error("更新项目文件信息时发生错误");
    // } finally {
    // }
  }
};
/**
 * 将projectData数据转换为projInf对象的pmin和pmax
 * @param projectData 项目属性列表
 * @param projInf 目标projInf对象
 */
const convertAttributesToProjInf = (projectData) => {
  if (
    !projectData?.projectAttributeList ||
    !Array.isArray(projectData.projectAttributeList)
  ) {
    return projectData;
  }

  // 遍历属性列表
  projectData.projectAttributeList.forEach((attr) => {
    try {
      if (attr.attributeKey === "pmin" || attr.attributeKey === "pmax") {
        // 解析JSON字符串为对象
        const valueObj = JSON.parse(attr.attributeValue);

        // 将解析后的对象赋值给projInf对应的属性
        projectData[attr.attributeKey] = valueObj;
      }
    } catch (error) {
      console.error(`解析${attr.attributeKey}出错:`, error);
    }
  });

  return projectData;
};
// 获取项目详情信息
const getProjectDetail = () => {
  const id = route.query.id || "zhanhua";
  if (id) {
    projectCode.value = id;
    loading.value = true;
    apiGetProjectDetail(id)
      .then((res) => {
        console.log("项目ID详情:", res);
        if (res.code === 200 && res.data) {
          // 是否存在经纬度
          const hasCoords = res.data.longitude && res.data.latitude;
          // 将res.data数据转换为projInf对象的pmin和pmax
          const newProjInf = convertAttributesToProjInf(res.data);
          console.log("new----对象的pmin和pmax", newProjInf);
          // 是否存在pmin和pmax
          const hasPminPmax = newProjInf?.pmin && newProjInf?.pmax;
          olBoxRef.value?.handleData(newProjInf, hasCoords, hasPminPmax);

          projectStore.setProjectData(res.data);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    console.log("无项目ID");
  }
};
// 获取CAD文本,并执行dify工作流,获取经纬度坐标,并跳转到地图位置
const getCadTexts = async (texts) => {
  console.log(texts, "getCadTexts");

  if (abortController) {
    abortController.abort();
  }
  abortController = new AbortController();
  const signal = abortController.signal;

  loading.value = true;
  try {
    const file = createTxtFileFromString(texts, "cad.txt");
    const res = await goDifyWorkFlow(file, signal);
    console.log("dify---response", res);

    let lng, lat;
    //TODO: 后续修改经纬度获取方式
    if (res && res.location) {
      const match = /x:(\d+\.?\d*),y:(\d+\.?\d*)/.exec(res.location);
      if (match) {
        lng = parseFloat(match[1]);
        lat = parseFloat(match[2]);
      }
    }

    if (typeof lng === "number" && typeof lat === "number") {
      await updateProjectCoordinates({ lng, lat });
    } else {
      if (!signal.aborted) {
        ElMessage.info("未能自动识别经纬度，请手动输入。");
        coordinateDialogVisible.value = true;
      }
    }
  } catch (error) {
    if (error.name !== "AbortError") {
      console.error("getCadTexts error:", error);
      ElMessage.error("识别经纬度时发生错误。");
    }
  } finally {
    loading.value = false;
    abortController = null;
  }
};
// 更新项目经纬度
const updateProjectCoordinates = async ({ lng, lat }) => {
  if (typeof lng !== "number" || typeof lat !== "number") {
    ElMessage.error("经纬度格式不正确！");
    return;
  }
  loading.value = true;
  try {
    await apiUpdateProjectFile(projectCode.value, {
      longitude: lng,
      latitude: lat,
    });
    ElMessage.success("项目经纬度更新成功！");
    coordinateDialogVisible.value = false;
    getProjectDetail(); // Refresh data
  } catch (error) {
    ElMessage.error("更新项目经纬度失败。");
  } finally {
    loading.value = false;
  }
};
// 更新项目信息
const updateProjectInfo = async () => {
  // 关闭对话框
  uploadDialogVisible.value = false;
  // 更新项目信息
  if (updateData.value.cadUrl) {
    // 调用接口更新CAD图纸信息
    const res = await apiUpdateProjectFile(projectCode.value, {
      cadUrl: updateData.value.cadUrl,
    });
    if (res.api_status && res.code === 200) {
      ElMessage.success("CAD图纸信息更新成功！");
    } else {
      ElMessage.error("CAD图纸信息更新失败！");
    }
  } else if (updateData.value.tiffUrl) {
    // 调用接口更新航拍图像信息
    const res = await apiUpdateProjectFile(projectCode.value, {
      tiffUrl: updateData.value.tiffUrl,
    });
    if (res.api_status && res.code === 200) {
      ElMessage.success("航拍图像信息更新成功！");
    } else {
      ElMessage.error("航拍图像信息更新失败！");
    }
  }
};
// 弹出框--确定按钮逻辑
const jumpSure = async () => {
  await updateProjectInfo();
};

onMounted(() => {
  try {
    window.postMessage({ type: 'Wasm', payload: 'Loading completed' }, '*');
  } catch (error) {
    window.postMessage({ type: 'Wasm', payload: 'Loading completed' }, '*');
  }
  // 页面加载时获取项目详情
  getProjectDetail();
});

onUnmounted(() => {
  if (abortController) {
    abortController.abort();
    abortController = null;
  }
});
</script>

<style scoped lang="scss">
.header--detail-wrap {
  border-bottom: 1px solid #f9fafc;
}

.data-management {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  .content-wrap {
    flex: 1;
    background-color: #000;
    position: relative;

    .mapBox {
      width: 100vw;
      height: calc(100vh - 157px);
    }
  }

  .upload-container {
    padding: 20px;

    .upload-note {
      margin-top: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      color: #5e6d82;
      font-size: 14px;

      i {
        color: #909399;
        margin-right: 5px;
      }
    }
  }
}
</style>
