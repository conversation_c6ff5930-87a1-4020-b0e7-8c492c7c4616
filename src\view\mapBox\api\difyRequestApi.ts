import { mapBoxConfig } from "@/view/mapBox/config/index";
import axios from "axios";

const instance = axios.create({
  baseURL: mapBoxConfig.difyUrl,
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${mapBoxConfig.difyToken}`,
  },
  transformRequest: [
    function (data, headers) {
      if (headers["Content-Type"] === "multipart/form-data") {
        const formData = new FormData();
        for (const key in data) {
          formData.append(key, data[key]);
        }
        return formData;
      }
      return JSON.stringify(data);
    },
  ],
});
interface DifyUploadFileApiParams {
  file: File;
  user: string;
}
/**
 * 调用dify上传接口
 * @param data
 * @returns
 */
export const difyUploadFileApi = async (
  data: DifyUploadFileApiParams
): Promise<DifyUploadResponse> => {
  const response = await instance.post("/files/upload", data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export interface DifyUploadResponse {
  id: string;
  name: string;
  size: number;
  extension: string;
  type: string;
  created_by: string;
  created_at: number;
}

interface DifyRunWorkflowApiParams {
  inputs: {
    file: {
      transfer_method: "local_file";
      upload_file_id: string;
      type: "document";
    };
  };
  response_mode: "streaming" | "blocking";
  user: string;
}
/**
 * 执行dify工作流
 * @param data
 * @returns
 */
export const difyRunWorkflowApi = async (
  data: DifyRunWorkflowApiParams,
  signal?: AbortSignal
): Promise<DifyCompletionResponse> => {
  return new Promise(async (resolve, reject) => {
    let taskId: string | null = null;
    let workflowRunId: string | null = null;

    const handleAbort = async () => {
      if (taskId && workflowRunId && data.user) {
        try {
          await difyStopWorkflowTaskApi(taskId, data.user);
          const finalResult = await difyGetWorkflowResultApi(workflowRunId);
          resolve(finalResult);
        } catch (e) {
          console.error("Error during task stop/result fetch sequence:", e);
          reject(e);
        }
      } else {
        reject(
          new Error(
            "Abort called, but task_id or workflow_run_id was not available."
          )
        );
      }
    };

    if (signal) {
      signal.addEventListener("abort", handleAbort);
    }

    try {
      const response = await fetch(mapBoxConfig.difyUrl + "/workflows/run", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${mapBoxConfig.difyToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        signal: signal, // Pass the signal to fetch
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `HTTP error! status: ${response.status}, message: ${errorText}`
        );
      }

      if (!response.body) {
        throw new Error("Response body is empty");
      }

      const reader = response.body
        .pipeThrough(new TextDecoderStream())
        .getReader();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          if (buffer) {
            // Process any remaining data in the buffer
            processChunk(buffer);
          }
          break;
        }

        buffer += value;
        const parts = buffer.split("\n\n");

        for (let i = 0; i < parts.length - 1; i++) {
          processChunk(parts[i]);
        }
        buffer = parts[parts.length - 1];
      }
    } catch (error: any) {
      if (error.name === "AbortError") {
        console.log(
          "Fetch aborted, relying on abort handler to resolve/reject."
        );
      } else {
        reject(error);
      }
    } finally {
      if (signal) {
        signal.removeEventListener("abort", handleAbort);
      }
    }

    function processChunk(chunk: string) {
      const lines = chunk
        .split("\n")
        .filter((line) => line.startsWith("data:"));
      for (const line of lines) {
        try {
          const jsonStr = line.substring(5).trim();
          if (!jsonStr) continue;

          const eventData = JSON.parse(jsonStr);

          // Capture task_id and workflow_run_id from the 'workflow_started' event
          if (eventData.event === "workflow_started") {
            taskId = eventData.task_id;
            workflowRunId = eventData.data.id;
          }

          if (eventData.event === "workflow_finished") {
            if (signal) {
              signal.removeEventListener("abort", handleAbort);
            }
            if (eventData.data.status === "succeeded") {
              resolve(eventData.data as DifyCompletionResponse);
            } else {
              reject(
                new Error(
                  `Workflow did not succeed. Status: ${eventData.data.status}. Error: ${eventData.data.error}`
                )
              );
            }
            return;
          }

          if (eventData.event === "error") {
            if (signal) {
              signal.removeEventListener("abort", handleAbort);
            }
            reject(new Error(eventData.message || "Unknown stream error"));
            return;
          }
        } catch (e) {
          console.error("Failed to parse stream event:", e);
        }
      }
    }
  });
};

export interface DifyCompletionResponse {
  id: string;
  workflow_id: string;
  status: "running" | "succeeded" | "failed" | "stopped";
  inputs: unknown;
  outputs: unknown;
  error: string | null;
  total_steps: number;
  total_tokens: number;
  created_at: number;
  finished_at: number | null;
  elapsed_time: number;
}

/**
 * 停止 Dify 工作流任务
 */
export const difyStopWorkflowTaskApi = async (
  taskId: string,
  user: string
): Promise<{ result: "success" }> => {
  const response = await fetch(
    `${mapBoxConfig.difyUrl}/workflows/tasks/${taskId}/stop`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${mapBoxConfig.difyToken}`,
      },
      body: JSON.stringify({ user }),
    }
  );
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `Failed to stop task ${taskId}: ${response.status} ${errorText}`
    );
  }
  return response.json();
};

/**
 * 获取 Dify 工作流的最终运行结果
 */
export const difyGetWorkflowResultApi = async (
  workflowRunId: string
): Promise<DifyCompletionResponse> => {
  const response = await fetch(
    `${mapBoxConfig.difyUrl}/workflows/run/${workflowRunId}`,
    {
      method: "GET",
      headers: {
        Authorization: `Bearer ${mapBoxConfig.difyToken}`,
      },
    }
  );
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `Failed to get workflow result for ${workflowRunId}: ${response.status} ${errorText}`
    );
  }
  return response.json();
};
