var cacheDrawing=new Map()
var cacheSGD=new Map()
export namespace Model{
    export function getCacheSGD(){
        return cacheSGD
    }
    export function emptyCacheSGD(){
       return cacheSGD=new Map()
    }
    export function fromSGD(uuid:string){
        return cacheSGD.get(uuid)
    }
    export function setSGD(uuid:string,geo:any){
        cacheSGD.set(uuid,geo)
    }
    export function fromDraw(uuid:string){
        return cacheDrawing.get(uuid)
    }
    export function setDraw(uuid:string,geo:any){
        cacheDrawing.set(uuid,geo)
    }
    export function delSGD(uuid:string){
        cacheSGD.delete(uuid)
    }
    export function saveDxf(){
        
    }
}