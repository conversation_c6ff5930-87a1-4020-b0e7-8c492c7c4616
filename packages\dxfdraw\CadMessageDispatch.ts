import {emitter} from 'emitter'
import { CADEvents } from 'dxf-viewer/src/controls'
import { MouseButton } from 'dxf-viewer/src/controls'


export class CadMessageDispatch {
    public viewer
    public get canvas() {
        return this.viewer.canvas
    }
    constructor(viewer) {
        this.viewer=viewer
        this.addListener()
    }
    public onKeyDown(event:KeyboardEvent){
        emitter.emit(CADEvents.KeyDown,event)
    }
    public onKeyUp(event:KeyboardEvent){
        emitter.emit(CADEvents.KeyUp,event)
    }
    public onPointerDown(event:PointerEvent) {
        if(event.button==MouseButton.Left){
            emitter.emit(CADEvents.LButtonDown,event)
        }else if(event.button==MouseButton.Right){
            emitter.emit(CADEvents.RButtonDown,event)
        }
    }

    public onPointerMove(event) {
        emitter.emit(CADEvents.MouseMove,event)
    }

    public onPointerUp(event) {
        if(event.button==MouseButton.Left){
            emitter.emit(CADEvents.LButtonUp,event)
        }else if(event.button==MouseButton.Right){
            emitter.emit(CADEvents.RButtonUp,event)
        }
    }

    public onMouseWheel(event) {
        emitter.emit(CADEvents.Wheel,event)
    }
    
    public onContextMenu(event){

    }
    public addListener() {
        this.canvas.addEventListener('pointerdown', this.onPointerDown.bind(this))
        this.canvas.addEventListener('pointermove', this.onPointerMove.bind(this))
        this.canvas.addEventListener('pointerup', this.onPointerUp.bind(this))
        this.canvas.addEventListener('wheel', this.onMouseWheel.bind(this));
        this.canvas.addEventListener('contextmenu', this.onContextMenu.bind(this))
        document.addEventListener('keydown', this.onKeyDown.bind(this))
        document.addEventListener('keyup', this.onKeyUp.bind(this))
    }

    public dispose() {
        this.canvas.removeEventListener('pointerdown', this.onPointerDown)
        this.canvas.removeEventListener('pointermove', this.onPointerMove)
        this.canvas.removeEventListener('pointerup', this.onPointerUp)
        this.canvas.removeEventListener('wheel', this.onMouseWheel);
        this.canvas.removeEventListener('contextmenu', this.onContextMenu)
        document.removeEventListener('keydown', this.onKeyDown)
        document.removeEventListener('keyup', this.onKeyUp)
    }
}