import { Matrix3, Vector2 } from "three"
import { <PERSON><PERSON><PERSON>gn, VAlign } from "./constant"
import { EntityType } from "../../constant"
import { Entity } from "../Entity"

/** Encapsulates calculations for a single-line text block. */
export class TextBlock {
    public fontSize
    public glyphs
    public bounds
    public curX
    public prevChar
    public prevFont
    public entityId:number=-1
    constructor(fontSize,entityId=-1) {
        this.fontSize = fontSize
        this.entityId=entityId
        /* Element is {shape: CharShape, vertices: ?{Vector2}[]} */
        this.glyphs = []
        this.bounds = null
        this.curX = 0
        this.prevChar = null
        this.prevFont = null
    }

    /**
     * @param char {string}
     * @param shape {CharShape}
     */
    PushChar(char, shape) {
        /* Initially store with just font size and characters position applied. Origin is the first
         * character base point.
         */
        let offset
        if (this.prevChar !== null && this.prevFont === shape.font) {
            offset = this.prevFont.GetKerning(this.prevChar, char)
        } else {
            offset = 0
        }
        const x = this.curX + offset * this.fontSize
        let vertices
        if (shape.vertices && shape.vertices.length > 0) {
            vertices = shape.GetVertices({x, y: 0}, this.fontSize)
            const xMin = x + shape.bounds.xMin * this.fontSize
            const xMax = x + shape.bounds.xMax * this.fontSize
            const yMin = shape.bounds.yMin * this.fontSize
            const yMax = shape.bounds.yMax * this.fontSize
            /* Leading/trailing spaces not accounted intentionally now. */
            if (this.bounds === null) {
                this.bounds = {xMin, xMax, yMin, yMax}
            } else {
                if (xMin < this.bounds.xMin) {
                    this.bounds.xMin = xMin
                }
                if (yMin < this.bounds.yMin) {
                    this.bounds.yMin = yMin
                }
                if (xMax > this.bounds.xMax) {
                    this.bounds.xMax = xMax
                }
                if (yMax > this.bounds.yMax) {
                    this.bounds.yMax = yMax
                }
            }
        } else {
            vertices = null
        }
        this.curX = x + shape.advance * this.fontSize
        this.glyphs.push({shape, vertices})
        this.prevChar = char
        this.prevFont = shape.font
    }

    GetCurrentPosition() {
        return this.curX
    }

    /**
     * @param startPos {{x,y}} TEXT group first alignment point.
     * @param endPos {?{x,y}} TEXT group second alignment point.
     * @param rotation {?number} Rotation attribute, deg.
     * @param widthFactor {?number} Relative X scale factor (group 41).
     * @param hAlign {?number} Horizontal text justification type code (group 72).
     * @param vAlign {?number} Vertical text justification type code (group 73).
     * @param color {number}
     * @param layer {?string}
     * @return {Generator<Entity>} Rendering entities. Currently just indexed triangles for each
     *  glyph.
     */
    *Render(startPos, endPos, rotation, widthFactor, hAlign, vAlign, color, layer) {

        if (this.bounds === null) {
            return
        }

        endPos = endPos ?? startPos
        if (rotation) {
            rotation *= -Math.PI / 180
        } else {
            rotation = 0
        }
        widthFactor = widthFactor ?? 1
        hAlign = hAlign ?? HAlign.LEFT
        vAlign = vAlign ?? VAlign.BASELINE

        let origin = new Vector2()
        let scale = new Vector2(widthFactor, 1)
        let insertionPos =
            (hAlign === HAlign.LEFT && vAlign === VAlign.BASELINE) ||
            hAlign === HAlign.FIT || hAlign === HAlign.ALIGNED ?
            new Vector2(startPos.x, startPos.y) : new Vector2(endPos.x, endPos.y)

        const GetFitScale = () => {
            const width = endPos.x - startPos.x
            if (width < Number.MIN_VALUE * 2) {
                return widthFactor
            }
            return width / (this.bounds.xMax - this.bounds.xMin)
        }

        const GetFitRotation = () => {
            return -Math.atan2(endPos.y - startPos.y, endPos.x - startPos.x)
        }

        switch (hAlign) {
        case HAlign.LEFT:
            origin.x = this.bounds.xMin
            break
        case HAlign.CENTER:
            origin.x = (this.bounds.xMax - this.bounds.xMin) / 2
            break
        case HAlign.RIGHT:
            origin.x = this.bounds.xMax
            break
        case HAlign.MIDDLE:
            origin.x = (this.bounds.xMax - this.bounds.xMin) / 2
            origin.y = (this.bounds.yMax - this.bounds.yMin) / 2
            break
        case HAlign.ALIGNED: {
            const f = GetFitScale()
            scale.x = f
            scale.y = f
            rotation = GetFitRotation()
            break
        }
        case HAlign.FIT:
            scale.x = GetFitScale()
            rotation = GetFitRotation()
            break
        default:
            console.warn("Unrecognized hAlign value: " + hAlign)
        }

        switch (vAlign) {
        case VAlign.BASELINE:
            break
        case VAlign.BOTTOM:
            origin.y = this.bounds.yMin
            break
        case VAlign.MIDDLE:
            origin.y = (this.bounds.yMax - this.bounds.yMin) / 2
            break
        case VAlign.TOP:
            origin.y = this.bounds.yMax
            break
        default:
            console.warn("Unrecognized vAlign value: " + vAlign)
        }

        const transform = new Matrix3().translate(-origin.x, -origin.y).scale(scale.x, scale.y)
            .rotate(rotation).translate(insertionPos.x, insertionPos.y)

        for (const glyph of this.glyphs) {
            if (glyph.vertices) {
                for (const v of glyph.vertices) {
                    v.applyMatrix3(transform)
                }
                yield new Entity({
                   type: EntityType.TRIANGLES,
                   vertices: glyph.vertices,
                   indices: glyph.shape.indices,
                   layer, color,
                   entityId:this.entityId
               })
            }
        }
    }
}