import * as THREE from "three"
import {ElementStyle} from './ElementStyle'

export class Block extends ElementStyle {
    public vlist:THREE.Vector3[]=[]
    public Block:any
    public surroundingBoxwidth:any
    public surroundingBoxheight:any
    public blockName:string=''
    public blockcenter:any
    public count:number=0
    public iscomplete:boolean=false

    public bkWasm:any
    public ibkWasm:any

    // public wasmVPlist:any
    public gp:any=null

    constructor() {
        // 调用父类的构造函数以初始化继承的属性
        super();
        // 定义线的两个端点
        const geometry = new THREE.PlaneGeometry(2, 3);
        // 创建线的材质
        const material = new THREE.MeshBasicMaterial({ color:'pink'});
        // 使用几何体和材质创建线
        this.Block = new THREE.Mesh(geometry, material);
    }
    setV(v:THREE.Vector3){
        this.count++
        this.iscomplete=true
        
        
        this.ibkWasm=new sg.InsertSGBlock()
        this.ibkWasm.setInsertBlockName(this.blockName)

        let ipv=this.bkWasm.getBasePoint()
        let x1=ipv.x()+v.x-this.blockcenter.x()
        let y1=ipv.y()+v.y-this.blockcenter.y()
        this.ibkWasm.setInsertBlockPostionPoint(new sg.Point2d(x1, y1))

        // Point2d pt = point;
        // if (m_document!=nullptr)
        // {
        //     auto block = m_document->getBlockByName(m_insertData.name);
        //     if (block != nullptr)
        //     {
        //         auto bbox = block->getBoundingBox();
        //         Point2d mid = bbox.getMiddlePoint();
        //         auto ipt = block->getBasePoint();
        //         pt.set(ipt.x() + point.x() - mid.x(), ipt.y() + point.y() - mid.y());
        //     }
        // }

        

    }
    getmove(v:THREE.Vector3){
        this.Block.geometry = new THREE.PlaneGeometry(this.surroundingBoxwidth, this.surroundingBoxwidth); // 宽度为2，高度为3
        this.Block.position.set(v.x, v.y, v.z); // 默认位置
    }
    
    complete(){
        this.iscomplete=true
    }
    getisitCompleted(){
        return this.iscomplete
    }

    private setWasmVPlist(vList){
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x, item.y))
        })
        return wasmVPlist
    }
}