import { defineStore } from 'pinia';


export const canvasStates = defineStore('canvasStates', ()=>{
    const coordinateList=reactive({

    })
    const statusConfig={
        IDLE:0,
        COPY:1,//复制
        FILLING:2,//填充
        MOVE_START:3,//准备移动画布
        MOVING:4,//移动画布
        DrawStatus:5 //绘制状态
      }
    const stateslist=reactive({
        status:statusConfig.IDLE,
        dragTarget:undefined,//选中的圆
        laseEvtPos:{x:0,y:0},
        offsetEvtPos:{x:0,y:0},
        offsetMouseEvtPos:{x:0,y:0},
        offset:{x:0,y:0},
        scale:1,
        scaleStep:0.03,
        maxScale:1000,
        minScale:0.001,
    })
    const lsObj=reactive({
        lsflag:false,
        dxfurl:'',
    })
    return {
        statusConfig,
        stateslist,
        lsObj,
    }
});

