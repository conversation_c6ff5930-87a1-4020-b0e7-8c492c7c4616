import {
  difyUploadFileApi,
  difyRunWorkflowApi,
  DifyCompletionResponse,
  DifyUploadResponse,
} from "../api/difyRequestApi";
import { ElMessage } from "element-plus";

/**
 * 完整执行dify文件处理工作流
 * 1. 上传文件
 * 2. 触发工作流并等待流式处理完成
 * @param file 要处理的文件
 * @param user 用户标识
 */
export const processFileWithDify = async (
  file: File,
  user: string,
  signal?: AbortSignal
): Promise<DifyCompletionResponse> => {
  // Step 1: Upload file
  const uploadResult: DifyUploadResponse = await difyUploadFileApi({
    file: file,
    user: user,
  });

  const upload_file_id = uploadResult.id;
  if (!upload_file_id) {
    throw new Error("File upload failed, no ID returned.");
  }

  // Step 2: Start workflow and wait for stream to complete
  const runParams = {
    inputs: {
      file: {
        transfer_method: "local_file" as const,
        upload_file_id: upload_file_id,
        type: "document" as const,
      },
    },
    response_mode: "streaming" as const,
    user: user,
  };
  const finalResult = await difyRunWorkflowApi(runParams, signal);

  return finalResult;
};

/**
 * 将字符串内容创建为TXT文件对象
 * @param content 要写入的字符串内容
 * @param fileName 生成的文件名 (例如: "my-document.txt")
 * @returns {File} 创建的File对象
 */
export const createTxtFileFromString = (
  content: string,
  fileName: string
): File => {
  const blob = new Blob([content], { type: "text/plain" });
  const file = new File([blob], fileName, { type: "text/plain" });
  return file;
};

/**
 * 执行dify工作流
 * @param file 要写入的字符串内容
 * @returns {} 该方法输出一个经纬度坐标
 */
export const goDifyWorkFlow = async (
  file: File,
  signal?: AbortSignal
): Promise<DifyCompletionResponse["outputs"]> => {
  try {
    const result = await processFileWithDify(file, "luliangjun", signal);
    console.log("✅ Workflow finished successfully!", result.outputs);
    ElMessage.success("文件处理成功！");
    return result.outputs;
  } catch (error) {
    console.error("❌ Workflow failed:", error);
    if (error instanceof Error && error.name === "AbortError") {
      ElMessage.info("文件处理已取消。");
    } else if (error instanceof Error) {
      ElMessage.error(`文件处理失败: ${error.message}`);
    } else {
      ElMessage.error(`文件处理失败: ${String(error)}`);
    }
    throw error; // Propagate the error
  }
};
