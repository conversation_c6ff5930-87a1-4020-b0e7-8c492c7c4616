
import { Vector2 } from "three"
import { text<PERSON><PERSON>, Entity } from "../entities"
import { EntityType, BatchEntityType } from '../constant'
import { ParseSpecialChars, MTextFormatParser } from '../entities'
import { HatchCalculator } from "../entities"
import { LookupPattern } from "../patterns/Pattern"
import {
    MAX_HATCH_LINES, MAX_HATCH_SEGMENTS
} from '../constant'
import "../patterns"

import { DxfScene } from "./DxfScene"
import { getEntityInfo, getEntityInfo2, getEntityRgba, getInterpNum, getSgBufInf, getSGColorRGB, getSGColorRgba } from "./sceneUtils"
import { globalDxf } from "../global/dxfImpGroup"
import { BatchingKey } from "../batch"


/** This class prepares an internal representation of a DXF file, optimized fo WebGL rendering. It
 * is decoupled in such a way so that it should be possible to build it in a web-worker, effectively
 * transfer it to the main thread, and easily apply it to a Three.js scene there.
 */
export class DxfParseScene extends DxfScene {
    constructor(viewer, options?) {
        super(viewer, options)
    }
    _ProcessWasmEntityOrBlock(a, blockCtx) {
        try {
            const isInsertBlock = a.isInsertBlock();
            if (isInsertBlock) {
                console.log(`insert or entity? insert`)
            }
        } catch (e) {
            const typestr = a.getObjType();
            console.log(`insert or entity? entity: ${typestr}`)
            return;
        }
    }

    async _ProcessWasmEntity(a, blockCtx?) {
        let typestr;
        try {
            typestr=a.getObjType();
            // console.log(typestr)
            if (['SGObjMText','SGObjText','SGObjLinearDim'].includes(typestr) ){
                let txt
                if(typestr=='SGObjMText'){
                    const mtext = sg.SGObjTool.convertToMText(a)
                    const singleTexts =  mtext.convertToSingleTexts()
                    const n = singleTexts.size()
                    for(let i = 0; i < n; i++){
                        const singleText = singleTexts.get(i)
                        let text = sg.SGObjTool.convertToText(singleText)
                        txt+=text.getText()
                    }
                }else{
                    txt=a.getText()
                }
            }
        } catch(e) {
            console.error(e);
            return;
        }

        let renderEntities: any
        if (typestr === 'SGObjCurve') {//单条线 
            renderEntities = this._decomposeCurve(a)
        } else if (typestr === 'SGObjPoint') {
            renderEntities = this._decomposePoint(a)
        } else if (typestr === 'SGObjComBinCurve') { // polyline
            renderEntities = this._decomposeCombinCurve(a)
        } else if (typestr === 'SGObjText') {
            renderEntities = this._decomposeText(a)
        } else if (typestr === 'SGObjMText') {
            renderEntities = this._decomposeMText(a)
        } else if (typestr === 'SGObjHatch') {
            globalDxf.statistic.hatch += 1
            renderEntities = this._decomposeHatch(a)
        } else if (typestr === 'SGObjAlignDim') {
            renderEntities = this._decomposeAlignDim(a)
        } else if (typestr === 'SGObjLinearDim') {
            renderEntities = this._decomposeLinearDim(a)
        } else if (typestr === 'SGObjRadialDim') {
            renderEntities = this._decomposeRadialDim(a)
        } else if (typestr === 'SGObjAngular2LDim') {
            renderEntities = this._decomposeAngularDim(a)
        } else if (typestr === 'SGObjDiametricDim') {
            renderEntities = this._decomposeDiametricDim(a)
        } else if (typestr === 'SGObjOrdinateDim') {
            renderEntities = this._decomposeOrdinateDim(a)
        } else if (typestr === 'SGObjLeader') {
            renderEntities = this._decomposeLeader(a)
        } else if (typestr === 'SGObjInsert') {
            const insert = a.getInsertSGBlock();
            // console.log(insert,'insertinsertinsertinsertinsertinsert');
            this._ProcessInsertEntity(insert, blockCtx);
            return;
        } else {
            console.warn("unhandled SGDrawGroup type: " + typestr)
        }
        for (const renderEntity of renderEntities) {
            this._ProcessEntity(renderEntity, blockCtx)
        }
    }

    _ProcessWasmEntityToAddSelectionPoints(a) {
        let typestr = a.getObjType()
        let renderEntities: any
        if (typestr === 'SGObjCurve') {//单条线 
            renderEntities = this._decomposeCurveSelectionPoints(a)
        } else if (typestr === 'SGObjComBinCurve') { // polyline
            renderEntities = this._decomposeCombinCurveSelectionPoints(a)
        } else {
            console.warn("unhandled SGDrawGroup type for selection points: " + typestr)
            renderEntities = []
        }
        for (const renderEntity of renderEntities) {
            this._ProcessEntity(renderEntity)
        }
    }

    selectionPallate = {
        "startPoint": 'rgba(255, 0, 0, 255)',
        "middlePoint": 'rgba(0, 255, 0, 255)',
        "endPoint": 'rgba(0, 0, 255, 255)',
        "center": 'rgba(255, 255, 0)'
    };

    *_decomposeSelectionPoint(geom, layer) {
        let points = geom.selectPoints();
        const pointsNumber = points.size();
        for (let i = 0; i < pointsNumber; i++) {
            const typedPoint = points.get(i);
            const color = this.selectionPallate[typedPoint.getString()];
            let p1 = {
                x: typedPoint.getPoint().x(),
                y: typedPoint.getPoint().y()
            }
            // console.log(typedPoint.getString(), p1, color)
            yield new Entity({
                type: EntityType.POINTS,
                vertices: [p1.x, p1.y],
                layer, color,
                lineType: undefined
            })
        }
    }

    *_decomposeCurveSelectionPoints(a) {
        let rr = sg.SGObjTool.convertToCurve(a)
        const layer = a.getLayer()
        yield* this._decomposeSelectionPoint(rr, layer)
    }

    *_decomposeCombinCurveSelectionPoints(a) {
        let tt = sg.SGObjTool.convertToComBinCurve(a)
        const layer = a.getLayer()
        yield* this._decomposeSelectionPoint(tt, layer)
    }

    *_decomposeCurve(a, wrap?) {
        let curv = sg.SGObjTool.convertToCurve(a)
        let curvtype: string = curv.getCurveType()
        // console.log(curvTyep)
        if (curvtype === 'LineSegment2d') {
            yield* this._decomposeLineSegment2d(a, wrap)
        } else if (curvtype === 'Arc2d' || curvtype === 'Circle2d' || curvtype === 'Ellipse2d' || curvtype === 'EllipseArc2d' || curvtype === 'BSpline2d') {
            yield* this._decomposeCurveSegment(a, wrap)
        } else {
            console.warn("unhandled SGObjCurve type: " + a.getObjType())
            yield []
        }
    }

    *_decomposeLineSegment2d(a, wrap?) {
        a.getRenderPoints(100)
        let { id, color, lWidth, lscale, vertices, rgb } = getSgBufInf()
        let entInf = getEntityInfo2(a)
        yield new Entity({
            type: EntityType.DASHLINE,
            vertices,
            color,
            entityId: getRenderId(id, wrap),
            rgb,
            ...entInf
        })
    }

    *_decomposeCurveSegment(a, wrap?) {
        const nInterp = getInterpNum(a)
        a.getRenderPoints(nInterp)
        let { id, color, lWidth, lscale, vertices, rgb } = getSgBufInf()
        let entInf = getEntityInfo2(a)
        yield new Entity({
            type: EntityType.DASHLINE,
            vertices,
            color,
            shape: false,
            entityId: getRenderId(id, wrap),
            rgb,
            ...entInf
        })
    }

    *_decomposeCombinCurve(a) {
        let comb = sg.SGObjTool.convertToComBinCurve(a)
        yield* this._decomposeCurveSegment(comb)
    }

    *_decomposePoint(a) {
        let point = sg.SGObjTool.convertToPoint(a)
        let vertices = [
            point.getPoint().x(),
            point.getPoint().y()
        ]
        const color = getEntityRgba(point)
        let rgb=getSGColorRGB(point.getColor())
        const layer = a.getLayer()
        yield new Entity({
            type: EntityType.POINTS,
            vertices,
            layer, color,
            lineType: undefined,
            entityId: a.getDrawObjectId(),
            rgb
        })
    }

    *_decomposeText(obj, wrap?) {
       
        
        let text = sg.SGObjTool.convertToText(obj)
        let entInf = getEntityInfo2(obj)
        text.getRenderInfo()
        let buf = sg.getShareBuffer();
        sg.freeShareBuffer();
        let [id, r, g, b, a, lWidth, lscale] = buf.slice(4, 11)
        let rgb = [r, g, b]
        let [inx, iny, posx, posy, vAlign, hAlign, widFactor, tHei, tRot] = buf.slice(11, 20)
        let color = `rgba(${r},${g},${b},${a})`

        const insertPos = { x: inx, y: iny };
        const alignPos = { x: posx, y: posy };

        if (!widFactor || widFactor === 0) {
            widFactor = 1
        }

        

        const textstr = text.getText()
        const textStyleName = text.getTextStyle()
        const gp = this.viewer.transaction.gpObj
        const basedata = gp.getDxfBaseData()
        const textStyle = basedata.getTextStyleByName(textStyleName)

        const fontFileName = textStyle.getPrimaryFontFile()
        const bigfontFileName = textStyle.getBigFontFile()
        
        yield* textRender.Render({
            text: textstr,
            fontFileName,
            bigfontFileName,
            fontSize: tHei,
            pos: insertPos,
            alignPoint: alignPos,
            rotation: tRot,//entity.rotation,
            hAlign, //hAlignment,
            vAlign, // TODO: vAlignment,
            widthFactor: widFactor,
            color,
            entityId: getRenderId(id, wrap),
            rgb,
            ...entInf
        })
    }

    *_decomposeMText(obj, wrap?) {
        let entInf = getEntityInfo2(obj)
        const mtext = sg.SGObjTool.convertToMText(obj)
        const id = mtext.getDrawObjectId()
        const sgpos = mtext.getInsertPoint()
        const pos = new Vector2(sgpos.x(), sgpos.y())

        const sgxdir = mtext.getXDir()
        const textHeight = mtext.getHeight()
        const columnWidth = mtext.getWidth()
        const attachmentPoint = mtext.getAttachmentPoint()
        const sgDrawingDirection = mtext.getDrawingDirection()
        const lineSpacingStyle = mtext.getLineSpacingStyle()
        const lineSpacingFactor = mtext.getLineSpacingFactor()
        const formattedText = mtext.getText()
        const style = mtext.getStyle()
        const angle = mtext.getAngle()

        const gp = this.viewer.transaction.gpObj
        const basedata = gp.getDxfBaseData()
        const textStyle = basedata.getTextStyleByName(style)

        const fontFileName = textStyle.getPrimaryFontFile()
        const bigfontFileName = textStyle.getBigFontFile()
        let widFactor = textStyle.getWidthFactor()

        if (!widFactor || widFactor === 0) {
            widFactor = 1
        }
        let colorObj = mtext.getColor()
        const rgb = getSGColorRGB(colorObj)
        const color = getSGColorRgba(colorObj)
  
        yield* textRender.RenderMText({
            formattedText,
            fontFileName,
            bigfontFileName,
            textHeight,
            pos,
            angle,
            attachmentPoint,
            widFactor,
            columnWidth,
            lineSpacingStyle,
            lineSpacingFactor,
            color,
            entityId: getRenderId(id, wrap),
            rgb,
            ...entInf
        })

    }

    *_decomposeHatch(a) {
        try {
            // 1. calc boundary as vector2[][]
            let hatchDrawObject = sg.SGObjTool.convertToHatch(a)
            let ll = hatchDrawObject.getOutLoop()
            let num = ll.curveCount()

            let loop: any[] = []
            for (let i = 0; i < num; i++) {
                let lItm = ll.getBoundCurve(i)
                const curveType = lItm.getCurveType()
                if (curveType === 'LineSegment2d') {
                    loop.push(new Vector2(lItm.startPoint().x(), lItm.startPoint().y()))
                } else {
                    let dxfEntity;
                    if (curveType === 'Arc2d') {
                        dxfEntity = sg.Geometry2dUtils.convertToArc2d(lItm)
                    } else if (curveType === 'EllipseArc2d') {
                        dxfEntity = sg.Geometry2dUtils.convertToEllipseArc2d(lItm)
                    } else if (curveType === 'BSpline2d') {
                        dxfEntity = sg.Geometry2dUtils.convertToBSpline2d(lItm)
                    } else {
                        console.log("hatch - else: " + curveType)
                        return
                    }
                    const nInterp = 16;
                    let plist = dxfEntity.getInterpolatePoints(nInterp);
                    let vert;
                    for (let j = 0; j < nInterp; j++) {
                        vert = plist.get(j);
                        loop.push(new Vector2(vert.x(), vert.y()));
                    }
                }
            }
            let boundaryLoops: any[] = []
            boundaryLoops.push(loop)

            // 2. generate entity by boundary and pattern
            const style = 0
            const calc = new HatchCalculator(boundaryLoops, style)

            const color = getEntityRgba(hatchDrawObject)
            const layer = a.getLayer()
            const transform = null //this._GetEntityExtrusionTransform(entity)
            const scale = hatchDrawObject.getPatternScale() * 20
            const angle = hatchDrawObject.getPatternAngle()
            const patternName = hatchDrawObject.getPatternName();

            const isSolidPattern = hatchDrawObject.isSolid();

            if (isSolidPattern) {
                let bboxCenter = hatchDrawObject.getBoundingBox().getMiddlePoint();
                let vertices = [bboxCenter.x(), bboxCenter.y()];
                let indices: any[] = []
                for (let loop of boundaryLoops) {
                    const len = loop.length;
                    for (let i = 0; i < len; i++) {
                        vertices.push(loop[i].x, loop[i].y)
                    }
                    for (let i = 0; i < len; i++) {
                        indices.push(0) // index 0 is the center point
                        indices.push((i + 1));
                        if (i == (len - 1)) {
                            indices.push(1)
                        } else {
                            indices.push(i + 2)
                        }
                    }
                }
                yield new Entity({
                    type: EntityType.TRIANGLES,
                    vertices,
                    indices, layer, color, entityId: a.getDrawObjectId()
                })
                return
            }

            let pattern = LookupPattern(patternName, globalDxf.isMetric);
            if (!pattern) {
                return
            }

            const seedPoints = [{ x: 0, y: 0 }]

            for (const seedPoint of seedPoints) {

                const patTransform = calc.GetPatternTransform({
                    seedPoint,
                    angle,
                    scale
                })

                for (const line of pattern.lines) {
                    let offsetX
                    let offsetY
                    if (pattern.offsetInLineSpace) {
                        offsetX = line.offset.x
                        offsetY = line.offset.y
                    } else {
                        const sin = Math.sin(-(line.angle ?? 0))
                        const cos = Math.cos(-(line.angle ?? 0))
                        offsetX = line.offset.x * cos - line.offset.y * sin
                        offsetY = line.offset.x * sin + line.offset.y * cos
                    }

                    /* Normalize offset so that Y is always non-negative. Inverting offset vector
                     * direction does not change lines positions.
                     */
                    if (offsetY < 0) {
                        offsetY = -offsetY
                        offsetX = -offsetX
                    }

                    const lineTransform = calc.GetLineTransform({
                        patTransform,
                        basePoint: line.base,
                        angle: line.angle ?? 0
                    })

                    const bbox = calc.GetBoundingBox(lineTransform)
                    const margin = (bbox.max.x - bbox.min.x) * 0.05

                    /* First determine range of line indices. Line with index 0 goes through base point
                     * (which is [0; 0] in line coordinates system). Line with index `n`` starts in `n`
                     * offset vectors added to the base point.
                     */
                    let minLineIdx, maxLineIdx
                    if (offsetY == 0) {
                        /* Degenerated to single line. */
                        minLineIdx = 0
                        maxLineIdx = 0
                    } else {
                        minLineIdx = (bbox.min.y >= 0) ? Math.ceil(bbox.min.y / offsetY) : Math.floor(bbox.min.y / offsetY);
                        maxLineIdx = (bbox.max.y >= 0) ? Math.floor(bbox.max.y / offsetY) : Math.ceil(bbox.max.y / offsetY);
                    }

                    if (maxLineIdx - minLineIdx > MAX_HATCH_LINES) {
                        console.warn("Too many lines produced by hatching pattern")
                        continue
                    }

                    let dashPatLength
                    if (line.dashes && line.dashes.length > 1) {
                        dashPatLength = 0
                        for (const dash of line.dashes) {
                            if (dash < 0) {
                                dashPatLength -= dash
                            } else {
                                dashPatLength += dash
                            }
                        }
                    } else {
                        dashPatLength = null
                    }

                    const ocsTransform = lineTransform.clone().invert()

                    for (let lineIdx = minLineIdx; lineIdx <= maxLineIdx; lineIdx++) {
                        const y = lineIdx * offsetY
                        const xBase = lineIdx * offsetX

                        const xStart = bbox.min.x - margin
                        const xEnd = bbox.max.x + margin
                        const lineLength = xEnd - xStart
                        const start = new Vector2(xStart, y).applyMatrix3(ocsTransform)
                        const end = new Vector2(xEnd, y).applyMatrix3(ocsTransform)
                        const lineVec = end.clone().sub(start)
                        const clippedSegments = calc.ClipLine([start, end])

                        function GetParam(x) {
                            return (x - xStart) / lineLength
                        }

                        function RenderSegment(seg) {
                            const p1 = lineVec.clone().multiplyScalar(seg[0]).add(start)
                            const p2 = lineVec.clone().multiplyScalar(seg[1]).add(start)
                            if (transform) {
                                p1.applyMatrix3(transform)
                                p2.applyMatrix3(transform)
                            }
                            // if (seg[1] - seg[0] <= Number.EPSILON) {
                            //     return new Entity({
                            //         type: EntityType.POINTS,
                            //         vertices: [p1.x,p1.y],
                            //         layer, color, entityId: a.getDrawObjectId()
                            //     })
                            // }
                            return new Entity({
                                type: EntityType.LINE_SEGMENTS,
                                vertices: [p1.x, p1.y, p2.x, p2.y],
                                layer, color, entityId: a.getDrawObjectId()
                            })
                        }

                        /** Clip segment against `clippedSegments`. */
                        function* ClipSegment(segStart, segEnd) {
                            for (const seg of clippedSegments) {
                                if (seg[0] >= segEnd) {
                                    return
                                }
                                if (seg[1] <= segStart) {
                                    continue
                                }
                                const _start = Math.max(segStart, seg[0])
                                const _end = Math.min(segEnd, seg[1])
                                yield [_start, _end]
                                segStart = _end
                            }
                        }

                        /* Determine range for segment indices. One segment is one full sequence of
                         * dashes. In case there is no dashes (solid line), just use hatch bounds.
                         */
                        if (dashPatLength !== null) {
                            let minSegIdx = Math.floor((xStart - xBase) / dashPatLength)
                            let maxSegIdx = Math.floor((xEnd - xBase) / dashPatLength)
                            if (maxSegIdx - minSegIdx >= MAX_HATCH_SEGMENTS) {
                                console.warn("Too many segments produced by hatching pattern line")
                                continue
                            }

                            for (let segIdx = minSegIdx; segIdx <= maxSegIdx; segIdx++) {
                                let segStartParam = GetParam(xBase + segIdx * dashPatLength)

                                for (let dashLength of line.dashes) {
                                    const isSpace = dashLength < 0
                                    if (isSpace) {
                                        dashLength = -dashLength
                                    }
                                    const dashLengthParam = dashLength / lineLength
                                    if (!isSpace) {
                                        for (const seg of ClipSegment(segStartParam,
                                            segStartParam + dashLengthParam)) {
                                            yield RenderSegment(seg)
                                        }
                                    }
                                    segStartParam += dashLengthParam
                                }
                            }

                        } else {
                            /* Single solid line. */
                            for (const seg of clippedSegments) {
                                yield RenderSegment(seg)
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.warn("!!! error: _decomposeHatch", error)
            return
        }
    }

    *_decomposeAlignDim(a) {
        let dimension = sg.SGObjTool.convertToAlignDim(a)
        yield* this._generateEntityForDimension(dimension)
    }

    *_decomposeLinearDim(a) {
        let dimension = sg.SGObjTool.convertToLinearDim(a)
        yield* this._generateEntityForDimension(dimension)
    }

    *_decomposeRadialDim(a) {
        let dimension = sg.SGObjTool.convertToRadialDim(a)
        yield* this._generateEntityForDimension(dimension)
    }

    *_decomposeAngularDim(a) {
        let dimension = sg.SGObjTool.convertToAngular2LDim(a)
        yield* this._generateEntityForDimension(dimension)
    }

    *_decomposeDiametricDim(a) {
        let dimension = sg.SGObjTool.convertToDiametricDim(a)
        yield* this._generateEntityForDimension(dimension)
    }

    *_decomposeOrdinateDim(a) {
        let dimension = sg.SGObjTool.convertToOrdinateDim(a)
        yield* this._generateEntityForDimension(dimension)
    }

    *_decomposeLeader(a) {
        let dimension = sg.SGObjTool.convertToLeader(a)
        yield* this._generateEntityForDimension(dimension)
    }

    *_generateEntityForDimension(dimension) {
        let data;
        if (this.viewer.transaction.gpObj) {
            data = dimension.getDimensonDrawData(this.viewer.transaction.gpObj)
        } else {
            data = dimension.getDefaultDimDrawData()
        }
        let { layer, colorMod } = getEntityInfo2(dimension)

        const color = getSGColorRgba(data.getArrowColor())
        const rgb = getSGColorRGB(data.getArrowColor())
        let id = dimension.getDrawObjectId()
        let wrap = { id }
        let curves = data.getCurve();
        for (let i = 0; i < curves.size(); i++) {
            let curve = curves.get(i);
            yield* this._decomposeCurve(curve, wrap)
        }

        let arrows = data.getArrow();
        for (let i = 0; i < arrows.size(); i++) {
            let points = arrows.get(i);
            let vertices: any[] = []
            let nPoint = points.size()
            for (let j = 0; j < nPoint; j++) {
                let p = points.get(j)
                vertices.push(p.x(), p.y());
            }
            let indices: number[] = nPoint == 4 ? [0, 1, 2, 0, 2, 3] : [0, 1, 2]//目前只仅支持三角形和四边形
            yield new Entity({
                type: EntityType.TRIANGLES,
                vertices,
                indices,
                color,
                rgb,
                entityId: id,
                layer,
                colorMod
            })
        }

        let texts = data.getText();
        for (let i = 0; i < texts.size(); i++){
            let text = texts.get(i);
            yield* this._decomposeText(text,wrap)
        }

        // let entInf = getEntityInfo2(dimension)
        // //const mtext = sg.SGObjTool.convertToMText(obj)
        // //const id = dimension.getDrawObjectId()
        // const sgpos = dimension.getTextPos()
        // const pos = new Vector2(sgpos.x(), sgpos.y())
        // //const sgxdir = mtext.getXDir()
        // const textHeight = dimension.getTextHeight(this.viewer.transaction.gpObj)
        // const columnWidth = 0//mtext.getWidth()
        // const attachmentPoint = dimension.getAttachment()
        // //const sgDrawingDirection = mtext.getDrawingDirection()
        // const lineSpacingStyle = dimension.getLineSpacingStyle()
        // const lineSpacingFactor = dimension.getLineSpacingFactor()
        // const formattedText = dimension.getText()
        // const dimstyle = dimension.getStyle()
        // const angle = dimension.getAngle()

        // const gp = this.viewer.transaction.gpObj
        // const basedata = gp.getDxfBaseData()
        // //const textStyle = basedata.getTextStyleByName(style)

        // const fontFileName = ""
        // const bigfontFileName = ""
        // let widFactor = 1//dimension.getWidthFactor()

        // if (!widFactor || widFactor === 0) {
        //     widFactor = 1
        // }

        // // const r = 255
        // // const g = 255
        // // const b = 255
        // // const a = 255
        // //const color=`rgba(${r},${g},${b},${a})`
        // //let rgb=[r,g,b]
        // console.log(formattedText)
        // if("1.047197551196711" ===formattedText){
        //     debugger
        // }
        // yield* textRender.RenderMText({
        //     formattedText,
        //     fontFileName,
        //     bigfontFileName,
        //     textHeight,
        //     pos,
        //     angle,
        //     attachmentPoint,
        //     widFactor,
        //     columnWidth,
        //     lineSpacingStyle,
        //     lineSpacingFactor,
        //     color,
        //     entityId: getRenderId(id, wrap),
        //     rgb,
        //     ...entInf
        // })
    }



    /**
     * Updates batches directly.
     * @param insert
     * @param blockCtx {?BlockContext} Nested block insert when non-null.
     */
    _ProcessInsertEntity(insert, blockCtx?) {
        const insertName = insert.getReferredBlockName();

        // console.log(insert,'insert');
        if (blockCtx) {
            //XXX handle indirect recursion
            if (blockCtx.name === insertName) {
                console.warn("Recursive block reference: " + blockCtx.name)
                return
            }
            /* Flatten nested blocks definition. */
            let block
            if (this.scenetype === 'SlIdentifiedScene') {
                block = this.blocks.get(insertName);
            } else {
                block = this.viewer.dxfScene.blocks.get(insertName);
            }

            if (!block) {
                console.warn("Unresolved nested block reference: " + insertName)
                return
            }
            let nestedCtx = blockCtx.WasmNestedBlockContext(block, insert)
            if (block.data.sgBlock) {
                const sgBlock = block.data.sgBlock;
                const blockEntityCount = sgBlock.count();
                const insertCount = sgBlock.insertBlockCount();

                for (let j = 0; j < blockEntityCount; j++) {
                    let a = sgBlock.getBaseObj(j);
                    try {
                        this._ProcessWasmEntity(a, nestedCtx);
                    } catch (error) {
                        console.error("error with nested block " + j + " . Error code: " + error)
                    }
                }
                for (let j = 0; j < insertCount; j++) {
                    let a = sgBlock.getInsertBlock(j);
                    try {


                        this._ProcessInsertEntity(a, nestedCtx);
                    } catch (error) {
                        console.error("error with entity in block " + j + " . Error code: " + error)
                    }
                }
            }
            return;
        }
        let block: any = null
        if (this.scenetype === 'SlIdentifiedScene') {
            block = this.blocks.get(insertName);
        } else {
            block = this.viewer.dxfScene.blocks.get(insertName);
        }


        if (!block) {
            console.warn("Unresolved block reference in INSERT: " + insertName)
            return
        }
        // calculate insert transform matrix
        const transform = block.InstantiationContext().GetWasmInsertionTransform(insert)
        /* Update bounding box and origin with transformed block bounds corner points. */
        const bounds = block.bounds
        
        if(!bounds){
            console.log(`块${insertName}中解析元素为空`)
            return
        }
        this._UpdateBounds(new Vector2(bounds.minX, bounds.minY).applyMatrix3(transform))
        this._UpdateBounds(new Vector2(bounds.maxX, bounds.maxY).applyMatrix3(transform))

        transform.translate(-globalDxf.wcsOff.x, -globalDxf.wcsOff.y)

        const layer =insert.getLayer() || 1 //this._GetEntityLayer(entity, null) 图层写死了1  insert.getLayer() 
        const color = null //this._GetEntityColor(entity, null)
        const lineType = 1 //this._GetLineType(entity, null, null)
        const insertId = insert.getDrawObjectId();

        const key = new BatchingKey(layer, insertName, BatchEntityType.BLOCK_INSTANCE,
            color, lineType)
        const batch = this._GetBatch(key)
        batch.PushInstanceTransform(transform);
        // console.log(batch,'batchbatchbatch');
        
        batch.PushInstanceEntityId(insertId);
        let rgb = getBlockColor(insert)
        let grayrgb=this.viewer.graymanage.getIdColor(insertId,rgb)
        batch.PushInstanceColor(grayrgb)

        const attrObjects = insert.getInsertBlockAttriObjs();
        const n = attrObjects.size();
        for (let i = 0; i < n; i++) {
            const attrObj = attrObjects.get(i);
            this._ProcessWasmEntity(attrObj);
        }
    }
}

var getRenderId = (id, wrap) => {
    if (wrap && 'id' in wrap) { return wrap.id }
    return id
}
const getBlockColor = (insertblock) => {
    let color = insertblock.getColor()
    let mod = color.getMode().value
    let r = color.getR()
    let g = color.getG()
    let b = color.getB()
    return [mod, r, g, b]
}
