<template> 
    <el-dialog
        v-model="dialogVisible"
        title="新增"
        width="600"
        class="XDataPop"
        :before-close="close"
    >
        <div class="bodybox">
            <div v-for="(item,index) in arr">
               <div class="int">
                  <div>
                    <span style="margin-right: 20px;">KEY：<el-input v-model="item.label" style="width: 100px;" placeholder="" /></span>
                    <span>VALUE：<el-input v-model="item.value" style="width: 200px;" placeholder="" /></span>
                  </div>
                  <div>
                    <el-button type="primary" :icon="Plus"  @click="add" />
                    <el-button type="danger"  :icon="Minus" @click="minus(index)" />
                  </div>
                </div>
            </div>
        </div>
        <template #footer>
        <div class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="confirm">确认</el-button>
        </div>
        </template>
    </el-dialog>

</template>
  
<script setup>
  import { Plus,Minus } from '@element-plus/icons-vue'
  import { ElLoading, ElMessage } from "element-plus";
  import { pickupInfo,setInXData } from 'dxf-viewer'
  import { PubSub } from 'emitter'

  const dialogVisible=ref(false)
  const arr=ref([{label:'',value:''}])

  onMounted(() => {
    
  })
  const add=()=>{
    arr.value.push({label:'',value:''})
  }
  const minus=(idx)=>{
    if(idx===0) return
    arr.value=arr.value.filter((ite,index)=>{
        return index !==idx
    })
  }
  
  const confirm = () => {
   let flag= arr.value.every(item=>{
    return item.label && item.value
   })
   if(!flag) return ElMessage({ message: '请填写完整', type: 'warning', }) 
   setInXData(arr.value)
   close()
  }


  const open=()=>{
    dialogVisible.value=true
  }
  const close=()=>{
    dialogVisible.value=false
    arr.value=[{label:'',value:''}]
  }
  

  defineExpose({
    open,
  })
  
</script>
  
<style lang="scss" scoped>
.bodybox {
    // margin-bottom: 20px;
}
.int {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
</style>