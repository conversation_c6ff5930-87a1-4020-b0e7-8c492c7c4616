/**
 * useProjectBuild 相关的类型定义和验证函数
 */

/**
 * 材料组件代码枚举
 */
export const MATERIAL_COMPONENT_CODES = {
  CONVENTIONAL_BRACKET: 'SWGJL0003', // 常规支架组件
  FIXED_BRACKET: 'SWGJL0006',        // 固定支架
}

/**
 * 字典代码枚举
 */
export const DICTIONARY_CODES = {
  POWER: 'SX001',      // 功率
  THICKNESS: 'SX002',  // 厚度
  UNIT: 'SX033',       // 单位
}

/**
 * 查询模式枚举
 */
export const QUERY_PATTERNS = {
  EQUAL: 'EQUAL',
  NOT_NULL: 'NotNull',
  LIKE: 'LIKE'
}

/**
 * 查询字段枚举
 */
export const QUERY_FIELDS = {
  IDENTIFY_ITEM: 'identifyItem',
  PROJECT_CODE: 'projectCode',
  DETAILS: 'details'
}

/**
 * 验证项目代码格式
 * @param {string} projectCode - 项目代码
 * @returns {boolean} 是否有效
 */
export function validateProjectCode(projectCode) {
  if (typeof projectCode !== 'string') return false
  
  // 项目代码格式: PX + 年月日 + 6位序号
  const pattern = /^PX\d{8}\d{6}$/
  return pattern.test(projectCode)
}

/**
 * 验证材料组件代码
 * @param {string} materialCode - 材料组件代码
 * @returns {boolean} 是否有效
 */
export function validateMaterialCode(materialCode) {
  return Object.values(MATERIAL_COMPONENT_CODES).includes(materialCode)
}

/**
 * 验证查询参数
 * @param {Object} queryParam - 查询参数
 * @returns {boolean} 是否有效
 */
export function validateQueryParam(queryParam) {
  if (!queryParam || typeof queryParam !== 'object') return false
  
  const { field, value, pattern } = queryParam
  
  return (
    typeof field === 'string' &&
    value !== undefined &&
    Object.values(QUERY_PATTERNS).includes(pattern)
  )
}

/**
 * 验证查询数据数组
 * @param {Array} queryData - 查询数据数组
 * @returns {boolean} 是否有效
 */
export function validateQueryData(queryData) {
  if (!Array.isArray(queryData)) return false
  
  return queryData.every(validateQueryParam)
}

/**
 * 验证图例数据
 * @param {Object} legendItem - 图例项
 * @returns {boolean} 是否有效
 */
export function validateLegendItem(legendItem) {
  if (!legendItem || typeof legendItem !== 'object') return false
  
  return (
    typeof legendItem.uuid === 'string' &&
    legendItem.meta !== undefined
  )
}

/**
 * 验证图例列表
 * @param {Array} legendList - 图例列表
 * @returns {boolean} 是否有效
 */
export function validateLegendList(legendList) {
  if (!Array.isArray(legendList)) return false
  
  return legendList.every(validateLegendItem)
}

/**
 * 验证统计信息
 * @param {Object} stats - 统计信息
 * @returns {boolean} 是否有效
 */
export function validateStats(stats) {
  if (!stats || typeof stats !== 'object') return false
  
  return (
    typeof stats.matchNum === 'number' &&
    typeof stats.total === 'number' &&
    stats.matchNum >= 0 &&
    stats.total >= 0
  )
}

/**
 * 验证属性对象
 * @param {Object} attribute - 属性对象
 * @returns {boolean} 是否有效
 */
export function validateAttribute(attribute) {
  if (!attribute || typeof attribute !== 'object') return false
  
  return (
    typeof attribute.dictionaryCode === 'string' &&
    attribute.defaultValue !== undefined
  )
}

/**
 * 验证材料对象
 * @param {Object} material - 材料对象
 * @returns {boolean} 是否有效
 */
export function validateMaterial(material) {
  if (!material || typeof material !== 'object') return false
  
  return (
    typeof material.materialComponentCode === 'string' &&
    Array.isArray(material.attributes) &&
    material.attributes.every(validateAttribute)
  )
}

/**
 * 验证CAD元数据项
 * @param {Object} cadMetaItem - CAD元数据项
 * @returns {boolean} 是否有效
 */
export function validateCadMetaItem(cadMetaItem) {
  if (!cadMetaItem || typeof cadMetaItem !== 'object') return false
  
  return (
    typeof cadMetaItem.uuid === 'string' &&
    Array.isArray(cadMetaItem.materialList) &&
    cadMetaItem.materialList.every(validateMaterial)
  )
}

/**
 * 验证CAD元数据列表
 * @param {Array} cadMetaList - CAD元数据列表
 * @returns {boolean} 是否有效
 */
export function validateCadMetaList(cadMetaList) {
  if (!Array.isArray(cadMetaList)) return false
  
  return cadMetaList.every(validateCadMetaItem)
}

/**
 * 验证Excel行数据
 * @param {Array} rowData - 行数据
 * @returns {boolean} 是否有效
 */
export function validateExcelRow(rowData) {
  if (!Array.isArray(rowData)) return false
  
  // Excel行应该有7列
  if (rowData.length !== 7) return false
  
  // 检查数量字段（第5列，索引4）是否为有效数字
  const quantity = rowData[4]
  if (quantity !== '' && quantity !== undefined && isNaN(Number(quantity))) {
    return false
  }
  
  return true
}

/**
 * 验证Excel数据
 * @param {Array} excelData - Excel数据
 * @returns {boolean} 是否有效
 */
export function validateExcelData(excelData) {
  if (!Array.isArray(excelData)) return false
  
  return excelData.every(validateExcelRow)
}

/**
 * 验证组件数据
 * @param {Object} componentData - 组件数据
 * @returns {boolean} 是否有效
 */
export function validateComponentData(componentData) {
  if (!componentData || typeof componentData !== 'object') return false
  
  const requiredFields = ['SX001', 'total']
  
  return requiredFields.every(field => {
    const value = componentData[field]
    return value !== undefined && !isNaN(Number(value))
  })
}

/**
 * 验证格式化数据
 * @param {Object} formattedData - 格式化数据
 * @returns {boolean} 是否有效
 */
export function validateFormattedData(formattedData) {
  if (!formattedData || typeof formattedData !== 'object') return false
  
  return Object.values(formattedData).every(componentArray => {
    if (!Array.isArray(componentArray)) return false
    return componentArray.every(validateComponentData)
  })
}

/**
 * 创建默认查询参数
 * @param {string} projectCode - 项目代码
 * @returns {Array} 查询参数数组
 */
export function createDefaultQueryData(projectCode) {
  if (!validateProjectCode(projectCode)) {
    throw new Error('无效的项目代码格式')
  }
  
  return [
    {
      field: QUERY_FIELDS.IDENTIFY_ITEM,
      value: 'IDENTIFY',
      pattern: QUERY_PATTERNS.EQUAL
    },
    {
      field: QUERY_FIELDS.PROJECT_CODE,
      value: projectCode,
      pattern: QUERY_PATTERNS.EQUAL
    }
  ]
}

/**
 * 创建默认查询参数对象
 * @param {string} materialComponentCode - 材料组件代码
 * @returns {Object} 查询参数对象
 */
export function createDefaultQueryParams(materialComponentCode) {
  if (!validateMaterialCode(materialComponentCode)) {
    throw new Error('无效的材料组件代码')
  }
  
  return {
    materialComponentCode
  }
}

/**
 * 安全的数字转换
 * @param {any} value - 要转换的值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的数字
 */
export function safeNumberConversion(value, defaultValue = 0) {
  const num = Number(value)
  return isNaN(num) ? defaultValue : num
}

/**
 * 安全的字符串转换
 * @param {any} value - 要转换的值
 * @param {string} defaultValue - 默认值
 * @returns {string} 转换后的字符串
 */
export function safeStringConversion(value, defaultValue = '') {
  if (value === null || value === undefined) return defaultValue
  return String(value)
}

/**
 * 格式化功率显示
 * @param {number} power - 功率值
 * @param {string} unit - 单位
 * @returns {string} 格式化后的功率字符串
 */
export function formatPowerDisplay(power, unit = 'MW') {
  const numPower = safeNumberConversion(power)
  return `${numPower.toFixed(2)}${unit}`
}

/**
 * 格式化数量显示
 * @param {number} quantity - 数量
 * @param {string} unit - 单位
 * @returns {string} 格式化后的数量字符串
 */
export function formatQuantityDisplay(quantity, unit = '片') {
  const numQuantity = safeNumberConversion(quantity)
  return `${numQuantity}${unit}`
}
