
/** Comparator function for arbitrary types. Null is always first. This is used just to make some
 * ordering for keys in tree structures, so no locale-aware string comparison.
 */
export function CompareValues(v1, v2) {
    if (v1 === null) {
        if (v2 === null) {
            return 0
        }
        return -1
    }
    if (v2 === null) {
        return 1
    }
    if (v1 < v2) {
        return -1
    }
    if (v1 > v2) {
        return 1
    }
    return 0
}