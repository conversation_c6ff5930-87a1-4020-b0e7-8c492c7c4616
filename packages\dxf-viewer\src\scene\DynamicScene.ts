import { Scene } from "three"
import { DynamicSceneType, WasmObjType } from "../constant"
import { Batch } from "../batch"
import { DxfParseScene } from "./DxfParseScene"
import { PubSub } from "emitter"
import { globalDxf, HighLMod, pickupInfo,dxfZsObj } from 'dxf-viewer'
import { splitBlockId } from "../libs"
export class DynamicScene extends DxfParseScene{
    public ids:number[]
    public entIds:Set<number>
    public dynmScene:Scene
    constructor(viewer,dynamicType=DynamicSceneType.HighLight){
        super(viewer)
        this.dynamicType=dynamicType//图层类型 高亮，选中，算量的已经被选中对象
        this.ids = []
        this.entIds=new Set()
        this.dynmScene = new Scene()
    }

    getScene(){
        return this.dynmScene
    }

    async AddObjById(gp, id, opt={color:'',isEdit:false}){
        let color=opt.color||'';
        let isEdit=opt.isEdit||false
        await this.processEntityById(gp,id,{isEdit})
        await this.buildscene(color)
    }
    async addObjsByIds(gp,ids,opt={color:'',isEdit:false}){
        let color=opt.color||'';
        let isEdit=opt.isEdit||false
        ids.forEach(id=>{
            this.processEntityById(gp,id,{isEdit})
        })
        await this.buildscene(color)
        this.entIds.clear()
    }

    async processEntityById(gp,id,opt={isEdit:false}){
        let isEdit=opt.isEdit||false
        if (id in this.ids){
            return
        }
        let {entityId,blockId}=splitBlockId(id)
        // console.log(blockId,'blockId',entityId,'entityId');
        console.log(blockId,'blockId');
        
        if(blockId){
            let block=gp.getInsertBlockById(blockId)
            if(globalDxf.highlMod==HighLMod.Block){
                this._ProcessInsertEntity(block,null)
            }else{

                // this._ProcessInsertEntity(block,null)
                // this.entIds.add(entityId)
                 let a=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
                 console.log(a,'aaaaaaaaaaaaaa');
                 
                 const blockCtx = { TransformVertex : function(v) {
                    return { x: v.x - globalDxf.wcsOff.x, y: v.y - globalDxf.wcsOff.y }
                }};
                this._ProcessWasmEntity(a, blockCtx);
            }
        }else{
            let id=entityId
            let a=gp.getPBaseObjById(id)
            if(!a){
                console.log(`未获取到对象 id=${id}`)
                return
            }
            const blockCtx = { TransformVertex : function(v) {
                return { x: v.x - globalDxf.wcsOff.x, y: v.y - globalDxf.wcsOff.y }
            }};
            this._ProcessWasmEntity(a, blockCtx);
            isEdit&&this._ProcessWasmEntityToAddSelectionPoints(a);//编辑时候同时需要解析元和添加夹点
        }

    }
    async buildscene(color=''){
        let sceneDa = this._BuildSceneDa()
        
        if (!sceneDa) return;

        this.batches.each(b=>{
            this._LoadBatch(b,color)
        })
    }



    _LoadBatch( batch, color) {
        let eids=Array.from(this.entIds)

        if (! this.sceneDa) return;
        const objects = new Batch(this.viewer,  batch).CreateDynamicObjects(color, this.dynamicType,null, eids)
        for (const obj of objects) {
            this.dynmScene.add(obj)
        }
    }

    override clear(){
        if(this.ids.length>0 && this.dynamicType==DynamicSceneType.Selection){
            PubSub.default.pub('selectionIdsChanged',{type:this.dynamicType,ids:[]})
        }
        this.entIds.clear()
        this.ids = []
        this.dynmScene.clear()
        // clear structures from parent
        this.sceneDa = null
        this.batches.clear()
        this.layers.clear()
    }

}