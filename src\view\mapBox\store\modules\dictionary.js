import { defineStore } from 'pinia';

// import { getEnumListApi } from '@/api/dictionary';

export const useDictionaryStore = defineStore({
  id: 'dicionary',
  persist: {
    enabled: true,
    storage: window.sessionStorage,
  },
  state: () => ({
    dict: [],
  }),
  getters: {
    // getDictName: (state) => {
    //   return (type, val) => {
    //     return state.dict.hasOwnProperty(type)
    //       ? state.dict[type].find((item) => item.key === String(val))?.value ?? ''
    //       : '';
    //   };
    // },
    // getDictList: (state) => {
    //   return (type) => {
    //     return state.dict.hasOwnProperty(type) ? state.dict[type] : [];
    //   };
    // },
  },
  actions: {
    loadDict() {
      // getEnumListApi().then((res) => {
      //   this.dict = res.data;
      // });
    },
  },
});
