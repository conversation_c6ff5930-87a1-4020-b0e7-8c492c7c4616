<template>
    <div class="exportCenter" ref="exportCenter">
        <div class="titleFiex" ref="titleFiex">
            <div style="width: 30px;height: 30px;">
                <svg-icon iconClass="efanhui5" w=30 h=30 class="icon zpointer" @click="skip" style="padding-top:0px;"></svg-icon>
            </div>
            <div class="name">导出中心</div>
        </div>
        <div class="listbox">
            <div style="margin-bottom: 20px;" >
                <span style="margin-right: 10px;">类型</span>
                <el-select v-model="ty" @change="btntype" placeholder="类型" style="width: 150px">
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
                </el-select>
            </div>
            <tabulation :tableData="tableData" :maxHeight="maxHeight"></tabulation>
        </div>
    </div>
  </template>
  
  <script setup>
  import tabulation from './components/tabulation.vue'
  import {apiTaskList} from '@/http/buildingBlockApi'
  import { ElMessage } from 'element-plus'

  import { ref,onMounted,nextTick,reactive } from 'vue'

  import { useRouter } from "vue-router";
  const router = useRouter();
  
  const exportCenter=ref(null)
  const titleFiex=ref(null)
  const maxHeight=ref('250')
  const tableData=ref([])
  const ty = ref('drawingObject2dxf')

  const options = [
      {
        value: 'dwg2dxf',
        label: 'dwg转dxf',
      },
      {
        value: 'drawingObject2dxf',
        label: '导出dxf',
      },
    ]

  onMounted(()=>{
    // apiTaskList({status:null,taskType:'dwg2dxf'}).then(res=>{
    //     if(res.code===200) {
    //         tableData.value=res.data
    //     }else {
    //         ElMessage({ message: res.msg,  type: 'error'})
    //     }
    // })
    btntype()
    maxHeight.value=(exportCenter.value.offsetHeight-100)+'px'
    
  })
  
  const btntype=()=>{
    apiTaskList({status:null,taskType:ty.value}).then(res=>{
        if(res.code===200) {
            tableData.value=res.data
        }else {
            ElMessage({ message: res.msg,  type: 'error'})
        }
    })
  }
  const skip=()=>{
    router.go(-1)
  }

  
  </script>
  
  <style lang="scss" scoped>
  .exportCenter {
    width: 100vw;
    height: 100vh;
    padding-top: 100px;
    background-color: #3a4352;
    color: #fff;
    position: relative;
    .titleFiex {
        width: 100%;
        display: flex;
        height: 50px;
        align-items: center;
        padding-left: 10px;
        position: fixed;
        top: 0;
        left: 0;
        background-color: #212832;
        .name {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
        }
    }
    .listbox {
        // margin-top: 100px;
        // height: calc(100vh - 100px);
    }
  }
  </style>
  
  