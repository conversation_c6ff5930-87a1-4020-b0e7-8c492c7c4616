﻿import * as THREE from "three"

import {ElementStyle} from '../ElementStyle'
export class RotatedDimension extends ElementStyle {
    private readonly ext = 0.01
    private readonly offset = 0.01    
    private readonly tickLength = 5///0.005   
    public viewer
    //https://lee-mac.com/bulgeconversion.html#bulgearc
    private rotation = 0;
    private xLine1Point = new THREE.Vector3();
    private xLine2Point = new THREE.Vector3();
    private dimLinePoint = new THREE.Vector3();
  
    private xMin = 0;
    private xMax = 0;
    private yMin = 0;
    private yMax = 0;

    public count:number=0

    public iscomplete:boolean=false

    // private line1:THREE.LineSegments
    // private arrow1:THREE.Mesh
    // private arrow2:THREE.Mesh
    public gp = new sg.SGObjLinearDim()
    //private sgDim = new sg.SGObjLinearDim()
    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super();
        this.viewer=viewer
    }
    public setV(v:THREE.Vector3){
        this.count++
        switch ( this.count) {
			case 1:
                this.xLine1Point = v;
				break;
			case 2:
                this.xLine2Point = v;

                this.xMin = Math.min(this.xLine1Point.x, this.xLine2Point.x)
                this.xMax = Math.max(this.xLine1Point.x, this.xLine2Point.x)
                this.yMin = Math.min(this.xLine1Point.y, this.xLine2Point.y)
                this.yMax = Math.max(this.xLine1Point.y, this.xLine2Point.y)
                this.dimLinePoint = v;
                this.rotation = 0;
                break;
            case 3:
                this.dimLinePoint = v;
                this.updateRotation();
                this.iscomplete=true
                break;
			default:
                break;
		}
    }
    private updateRotation(){
        if(this.dimLinePoint.x > this.xMin && this.dimLinePoint.x < this.xMax){
            if(this.dimLinePoint.y < this.yMin || this.dimLinePoint.y > this.yMax){
                this.rotation = 0;
            }
        }else if(this.dimLinePoint.y > this.yMin && this.dimLinePoint.y < this.yMax){
            if(this.dimLinePoint.x < this.xMin || this.dimLinePoint.x > this.xMax){
                this.rotation = 90;
            }

        }
    }
    getmove(v:THREE.Vector3){
        if(this.count < 2) return
        this.dimLinePoint = v;
        this.updateRotation();
        this.update()
    }
    complete(){
        if(this.count < 2) return
        this.update()
    }
    
    getisitCompleted(){
        return this.iscomplete
    }

    private update() {
        
        const startPoint = new sg.Point2d(this.xLine1Point.x, this.xLine1Point.y)
        this.gp.setStartPoint(startPoint)
        const endPoint = new sg.Point2d(this.xLine2Point.x, this.xLine2Point.y)
        this.gp.setEndPoint(endPoint)
        const dimPoint = new sg.Point2d(this.dimLinePoint.x, this.dimLinePoint.y)
        this.gp.setDimensionPos(dimPoint)
        this.gp.setLineDimAngle(this.rotation)
        
        this.viewer.transientSceneWasm.clear()
        this.viewer.transientSceneWasm.draw(this.gp)
        
    }
}