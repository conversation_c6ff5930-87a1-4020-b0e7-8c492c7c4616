import {DrawState,dxflSobj,pickupInfo} from '../global'
import { PubSub } from 'emitter';

import { globalDxf } from "../global/dxfImpGroup"
import {distinguishObj} from "../../../../src/view/sketch/slMethod/measurementCategory"
import { DxfViewer } from "../viewer";
import {  Vector3 } from "three";
import { screen2wcs } from './coordinateSystem';

export class MouseButtonLeft {
    public viewer:DxfViewer
    public scene
    public get selectBoxHelp(){
        return this.viewer.viewControl.selectBoxHelp
    }
    constructor(viewer){
        this.viewer=viewer
        this.scene=this.viewer.scene
    }
    public btnType(event:PointerEvent){
        switch ( globalDxf.drawstate ){
            case DrawState.Pick:
                this.pick(event)
                break;
            // case DrawState.Drawing://图元绘制
            //     this.drawingGraphic(event)
            //     break;
            // case DrawState.Plot:
            //     this.plot(event)
            //     break;
            // case DrawState.IdentifiyBox:
            //     this.identifiyByBox(event)
            //     break;
            // case DrawState.IdentifiyPolygon:
            //     this.identifiyByBox(event)
            //     break;
            // case DrawState.Pickline:
            //     this.pickOne(event)
            //     break;
        }
    }

    private pick(event){   
        if(pickupInfo.isSelected){
            if(pickupInfo.pickupIdlist.length>0) {//是否选中了夹点，选中夹点则进入夹点拖拽模式,仿mxcad
                let worldPoint = screen2wcs(event,this.viewer.camera)
                let isdraging = this.viewer.selectionScene.onLButtonDown(worldPoint)
                if(this.viewer.selectionScene.getIsDraging())
                {
                    this.viewer.selector.beforeSelect(event)
                    return
                }
            }
        }
        if(this.viewer.selectionScene.getFinishDrag())
        {//完成夹点拖拽不需要进入pick，只需要将拖拽图元重新被选中
            this.viewer.selectionScene.setFinishDrag(false)
            this.viewer.selectionScene.highlightDragObj()
            return
        }
        //如果不是夹点拖拽相关进入正常选择状态
        this.viewer.selector.OnSelect(event) 
        if(!pickupInfo.isSelected){
            if(this.viewer.viewControl.selectBoxHelp.isSelecting) {
                this.viewer.viewControl.selectBoxHelp.setV2(event)
                let {_screenV1,_screenV2}=this.viewer.viewControl.selectBoxHelp
                this.viewer.viewControl.viewer.selector.selectByBox(_screenV1,_screenV2,event)
                this.viewer.viewControl.selectBoxHelp.del()
                return
            }
            this.selectBoxHelp.setV1(event)
        }
        
    }


}
