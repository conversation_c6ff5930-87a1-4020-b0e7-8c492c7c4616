
import { poptip } from "dxf-viewer/src"
import { BufferGeometry, } from "three"
import { Line } from "./Line"

export class SlLine extends Line {

    constructor(viewer) {
        super(viewer);
    }
  
    override complete(){
        this.viewer.scene.remove(this.Line)
        poptip.dispose()
        if(this.pnts.length<2) return
        this.iscomplete=true
        this.Line.geometry=new BufferGeometry().setFromPoints(this.pnts);
        this.pnts.forEach((item,index)=>{
            if(index>0) {
                let p1=new sg.Point2d(this.pnts[index-1].x, this.pnts[index-1].y)
                let p2=new sg.Point2d(item.x, item.y)
                let l=new sg.LineSegment2d(p1,p2)
                let cv=new sg.SGObjCurve()
                cv.setpCurve(l)
                this.gps.push(cv)
            }
        })
    }
}

