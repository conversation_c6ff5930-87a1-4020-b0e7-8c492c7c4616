<template>
    <div class="cad_dialogBody">
        <el-dialog
            :model-value="blockBox.blockPop"
            title="块"
            width="430"
            class="textPop"
            :before-close="btnclosePop"
            >
            <div class="body">
                <div style="margin-top: 20px;">名称：<el-input v-model="blockBox.value" style="width: 260px"/></div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                <el-button type="primary" @click="confirm">确认</el-button>
                <el-button @click="btnclosePop">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>

</template>

<script lang="ts" setup>
import { ref,onMounted,nextTick,reactive } from 'vue'
import { ElLoading , ElMessage } from "element-plus";
import {blockBox,DxfviewerCad,pickupInfo} from 'dxf-viewer'


const emits = defineEmits([''])
const props = defineProps({
    // viewers:{
    //   type:Object,
    // },
})
onMounted(()=>{
})

const confirm=()=>{
    if(!blockBox.value) return ElMessage({ message: '请输入名称', type: 'warning',})
    let transaction=DxfviewerCad.value.GetViewer().transaction
    let bk=new sg.SGBlock(blockBox.value,new sg.Point2d(0,0),true)
    let ibk=new sg.InsertSGBlock()

    ibk.setInsertBlockName(blockBox.value)
    ibk.setInsertBlockInsertPoint(new sg.Point2d(0,0))
    // let ibkID=ibk.getDrawObjectId()
    // console.log(ibkID,'ibkID');
    pickupInfo.pickupIdlist.forEach(item=>{
        let obj=transaction.gpObj.getPBaseObjById(item)
        const newobj=obj.copyNewOne()
        bk.addBaseObj(newobj) 
    })
    transaction.del(pickupInfo.pickupIdlist,false)
    transaction.addBlock({
        block:bk,
        InsertBlock:ibk
    })
    
    blockBox.blockList.push({
        blockName:blockBox.value
    })
    blockBox.blockPop=false
}

const btnclosePop=()=>{
    blockBox.blockPop=false
}


defineExpose({

})
</script>

<style lang="scss" scoped>
.textPop {
}
</style>
  
  