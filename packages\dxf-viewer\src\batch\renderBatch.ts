import { Mesh, Vector2 } from "three"
import { BatchEntityType, INDEXED_CHUNK_SIZE, NativeType } from "../constant"
import { DynamicBuffer } from "../libs"
import { IndexedChunkWriter } from "./indexedchunkwriter"
import { IndexedChunk } from "./indexchunk"
import { <PERSON><PERSON><PERSON><PERSON> } from "./BatchingKey"

export class RenderBatch {
    public key:BatchingKey
    public chunks:IndexedChunk[]=[]
    public vertices:DynamicBuffer
    public entityIds:DynamicBuffer
    public lineSytleIds:DynamicBuffer
    public lineScales:DynamicBuffer
    public entityColors:DynamicBuffer
    public instanceTransforms:DynamicBuffer
    public instanceIds:DynamicBuffer
    public instanceColors:DynamicBuffer
    public ids:Set<number>=new Set()
    public meshes:Set<Mesh>=new Set()
    public idMeshes:Set<Mesh>=new Set()
    constructor(key) {
        this.key = key
        this.init()
    }
    init(){
        if (this.key.IsIndexed()) {
            this.chunks = []
        } else if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE) {
            this.instanceTransforms = new DynamicBuffer(NativeType.FLOAT32)
            this.instanceIds = new DynamicBuffer(NativeType.UINT32)
            this.instanceColors = new DynamicBuffer(NativeType.UINT8)
        } else {
            this.vertices = new DynamicBuffer(NativeType.FLOAT32)
            this.entityIds = new DynamicBuffer(NativeType.UINT32)
            this.lineSytleIds=new DynamicBuffer(NativeType.INT8)
            this.lineScales=new DynamicBuffer(NativeType.FLOAT32)
            this.entityColors=new DynamicBuffer(NativeType.UINT8)
        }
    }

    PushVertex(v) {
        const idx = this.vertices.Push(v.x)
        this.vertices.Push(v.y)
        return idx
    }

    PushEntityId(id){
        this.ids.add(id)
        const idx = this.entityIds.Push(id)
        return idx
    }

    PushLineSyleId(id){
        this.lineSytleIds.Push(id)
    }

    PushLineScale(scale){
        this.lineScales.Push(scale)
    }

    PushEntityColor(color:number[]){
        for(let i of color){
            this.entityColors.Push(i)
        }
    }

    /**
     * @param matrix {Matrix3} 3x3 Transform matrix. Assuming 2D affine transform so only top 3x2
     *  sub-matrix is taken.
     */
    PushInstanceTransform(matrix) {
        /* Storing in row-major order as expected by renderer. */
        for (let row = 0; row < 2; row++) {
            for (let col = 0; col < 3; col++) {
                this.instanceTransforms.Push(matrix.elements[col * 3 + row])
            }
        }
    }

    PushInstanceEntityId(id){
        this.instanceIds.Push(id)
    }

    PushInstanceColor(color:number[]){
        for(let i of color){
            this.instanceColors.Push(i)
        }
    }

    /** This method actually reserves space for the specified number of indexed vertices in some
     * chunk. The returned object should be used to push exactly the same amount vertices and any
     * number of their referring indices.
     * @param verticesCount Number of vertices in the chunk.
     * @return {IndexedChunkWriter}
     */
    PushChunk(verticesCount):IndexedChunkWriter {
        if (verticesCount > INDEXED_CHUNK_SIZE) {
            throw new Error("Vertices count exceeds chunk limit: " + verticesCount)
        }
        /* Find suitable chunk with minimal remaining space to fill them as fully as possible. */
        let curChunk:any = null
        let curSpace = 0
        for (const chunk of this.chunks) {
            const space = INDEXED_CHUNK_SIZE - chunk.vertices.GetSize() / 2
            if (space < verticesCount) {
                continue
            }
            if (curChunk === null || space < curSpace) {
                curChunk = chunk
                curSpace = space
            }
        }
        if (curChunk === null) {
            curChunk = this._NewChunk(verticesCount)
        }
        return new IndexedChunkWriter(curChunk, verticesCount)
    }

    /** Merge other batch into this one. They should have the same geometry type. Instanced batches
     * are disallowed.
     *
     * @param batch {RenderBatch}
     * @param transform {?Matrix3} Optional transform to apply for merged vertices.
     */
    Merge(batch, transform = null) {
        if (this.key.geometryType !== batch.key.geometryType) {
            throw new Error("Rendering batch merging geometry type mismatch: " +
                            `${this.key.geometryType} !== ${batch.key.geometryType}`)
        }
        if (this.key.IsInstanced()) {
            throw new Error("Attempted to merge instanced batch")
        }

        if (this.key.IsIndexed()) {
            // TODO lines has no index so id not handled, need to double confirm
            /* Merge chunks. */
            for (const chunk of batch.chunks) {
                const verticesSize = chunk.vertices.size
                const chunkWriter = this.PushChunk(verticesSize / 2)
                for (let i = 0; i < verticesSize; i += 2) {
                    const v = new Vector2(chunk.vertices.Get(i), chunk.vertices.Get(i + 1))
                    if (transform) {
                        v.applyMatrix3(transform)
                    }
                    chunkWriter.PushVertex(v)
                }
                const numIndices = chunk.indices.size
                for (let i = 0; i < numIndices; i ++) {
                    chunkWriter.PushIndex(chunk.indices.Get(i))
                }
                const numEntityIds = chunk.entityIds.size
                for (let i = 0; i < numEntityIds; i++){
                    chunkWriter.PushEntityId(chunk.entityIds.Get(i))
                }
                chunkWriter.Finish()
            }
        } else {
            const n = batch.vertices.size
            for (let i = 0; i < n; i += 2) {
                const v = new Vector2(batch.vertices.Get(i), batch.vertices.Get(i + 1))
                if (transform) {
                    v.applyMatrix3(transform)
                }
                this.PushVertex(v)
            }

            const idSize = batch.entityIds.size
            for (let i = 0; i < idSize; i+= 1) {
                this.PushEntityId(batch.entityIds.Get(i))
            }
        }
    }

    /** @return Vertices buffer required size in bytes. */
    GetVerticesBufferSize() {
        if (this.key.IsIndexed()) {
            let size = 0
            for (const chunk of this.chunks) {
                size += chunk.vertices.GetSize()
            }
            return size * Float32Array.BYTES_PER_ELEMENT
        } else if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE) {
            return 0
        } else {
            return this.vertices.GetSize() * Float32Array.BYTES_PER_ELEMENT
        }
    }

    /** @return Indices buffer required size in bytes. */
    GetIndicesBufferSize() {
        if (this.key.IsIndexed()) {
            let size = 0
            for (const chunk of this.chunks) {
                size += chunk.indices.GetSize()
            }
            return size * Uint16Array.BYTES_PER_ELEMENT
        } else {
            return 0
        }
    }

    /** @return Entity id buffer required size in bytes. */
    GetEntityIdBufferSize() {
        if (this.key.IsIndexed()) {
            let size = 0
            for (const chunk of this.chunks) {
                size += chunk.entityIds.GetSize()
            }
            return size * Uint32Array.BYTES_PER_ELEMENT
        } else if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE) {
            return 0
        } else {
            return this.entityIds.GetSize() * Uint32Array.BYTES_PER_ELEMENT
        }
    }

    /** @return Instances transforms buffer required size in bytes. */
    GetTransformsSize() {
        if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE) {
            return this.instanceTransforms.GetSize() * Float32Array.BYTES_PER_ELEMENT
        } else {
            return 0
        }
    }

    GetInstanceIdsBufferSize() {
        if (this.key.geometryType === BatchEntityType.BLOCK_INSTANCE) {
            return this.instanceIds.GetSize() * Uint32Array.BYTES_PER_ELEMENT
        } else {
            return 0
        }
    }

    _NewChunk(initialCapacity) {
        const chunk = new IndexedChunk(this,initialCapacity)
        this.chunks.push(chunk)
        return chunk
    }
}