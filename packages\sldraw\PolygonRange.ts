import { DrawHelp } from "../dxfdraw/drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState,pickupInfo,SlScopeType } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"
import { PubSub } from 'emitter';
import { Snap } from "dxf-viewer/src/scene/Snap"

export class PolygonRange extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    public color:string='orange'
    public iscomplete:boolean=false

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        const material = new LineBasicMaterial({ color:this.color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        PubSub.default.sub("slidentifyclear", this.clear.bind(this));
        this.init()
        Snap.turnOffSnap()//关闭捕捉吸附
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        while (true) {
            let p1=await this.lengthPick({
                tip:'指定下一个点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
        }
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        
        if(this.iscomplete){
            this.pnts=[]
            pickupInfo.identifyScope=[]
        }
        this.iscomplete=false
        
        singal.pickPoint.dispatch(event)        
        return true;
    }
    public override onRButtonDown( event:PointerEvent ) : boolean{
        if(this.pnts.length) {
            this.iscomplete=true
            this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,this.pnts[0]]);
            pickupInfo.identifyScope=this.pnts
        }  
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        if(!this.iscomplete) {
            this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
        }
        
    }
    
    clear(){
        this.pnts=[]
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        this.dispose()
        this.viewer.Render()
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
    }
    private setWasmVPlist(vList){
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x, item.y))
        })
        return wasmVPlist
    }
}


