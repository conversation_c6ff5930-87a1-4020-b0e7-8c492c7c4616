stages:
  - build
  - deploy

build-dev:
  stage: build
  variables:
    ENV: "dev"
  tags:
    - global-deploy-nodejs
  script:
    - echo $ENV > jobEnv
    - chmod +x cicd/build.sh
    - cicd/build.sh
    - curl $ISOLAR_BUILD_PROGRESS_DEPLOY_TRIGGER_DEV
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^openlayer/'

build-staging:
  stage: build
  variables:
    ENV: "staging"
  tags:
    - global-deploy-nodejs
  script:
    - echo $ENV > jobEnv
    - chmod +x cicd/build.sh
    - cicd/build.sh
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^staging/'

build-prod:
  stage: build
  variables:
    ENV: "production"
  tags:
    - global-deploy-nodejs
  script:
    - echo $ENV > jobEnv
    - chmod +x cicd/build.sh
    - cicd/build.sh
  artifacts:
    reports:
      dotenv: ${CI_PROJECT_NAME}_${CI_JOB_ID}_${CI_COMMIT_SHORT_SHA}.env
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^release/'

after_script:
  - export ENV=$(cat jobEnv)
  - chmod +x cicd/notifier.sh
  - cicd/notifier.sh
