import {DxfFetcher} from "./DxfFetcher"
import {DxfJsScene} from "../scene/DxfJsScene"
import {DxfWasmScene} from "../scene"
import opentype from "opentype.js"
import { WorkerRequest } from "./workrequest"
import { uploadFileAsText } from "../libs"
import { dxfZsObj } from "../global"

const MSG_SIGNATURE = "DxfWorkerMsg"

/** Wraps web-worker instance and provides unified interface to its services, including the when
 * web-worker is not used and all heavy operations are performed in main thread.
 */
export class DxfWorker {
    /** @param worker Web worker instance with DxfViewer.SetupWorker() function called. Can be null
     *  for synchronous operations.
     *  @param isWorker True for worker-side wrapper.
     */
    public worker
    public reqSeq:number=-1
    public requests:Map<number,WorkerRequest>=new Map()
    public progressCbk:Function|null=null
    public isWasm:Boolean=false//数据加载方式
    constructor(worker, isWorker = false) {
        this.worker = worker
        if (isWorker) {
            worker.onmessage = this._ProcessRequest.bind(this)
        } else if (worker) {
            worker.addEventListener("message", this._ProcessResponse.bind(this), false)
            worker.addEventListener("error", this._OnError.bind(this), false)
            this.reqSeq = 1
            /* Indexed by sequence. */
            this.requests = new Map()
            this.progressCbk = null
        }
    }

    /**
     * @param url DXF file URL.
     * @param fonts {?string[]} Fonts URLs.
     * @param options Viewer options. See DxfViewer.DefaultOptions.
     * @param progressCbk {Function?} (phase, processedSize, totalSize)
     */
    async Load(url,isWasm, fonts, options, progressCbk) {
        this.isWasm=isWasm
        if (this.worker) {
            return this._SendRequest(DxfWorkerStatus.LOAD,
                                     { url, fonts, options: this._CloneOptions(options) },
                                     progressCbk)
        } else {
            return this._Load(url, fonts, options, progressCbk)
        }
    }

    async Destroy(noWait = false) {
        if (this.worker) {
            if (!noWait) {
                await this._SendRequest(DxfWorkerStatus.DESTROY)
            }
            /* close() in the worker is not enough, instance is still visible in dev tools. */
            this.worker.terminate()
        }
    }

    async _ProcessRequest(event) {
        const msg = event.data
        if (msg.signature !== MSG_SIGNATURE) {
            // console.log("Message with bad signature", msg)
            return
        }
        const resp:any = {seq: msg.seq, type: msg.type, signature: MSG_SIGNATURE}
        const transfers:any[] = []
        try {
            resp.data = await this._ProcessRequestMessage(msg.type, msg.data, transfers, msg.seq)
        } catch (error) {
            console.error(error)
            resp.error = String(error)
        }
        this.worker.postMessage(resp, transfers)
        if (msg.type === DxfWorkerStatus.DESTROY) {
            this.worker.onmessage = null
            this.worker.close()
            this.worker = null
        }
    }

    async _ProcessRequestMessage(type, data, transfers, seq) {
        switch (type) {
        case DxfWorkerStatus.LOAD: {
            const {scene, dxf} = await this._Load(
                data.url,
                data.fonts,
                data.options,
                (phase, size, totalSize) => this._SendProgress(seq, phase, size, totalSize))
            transfers.push(scene?.vertices)
            transfers.push(scene?.indices)
            transfers.push(scene?.transforms)
            return {scene, dxf}
        }
        case DxfWorkerStatus.DESTROY:
            return null
        default:
            throw "Unknown message type: " + type
        }
    }

    async _ProcessResponse(event) {
        const msg = event.data
        if (msg.signature !== MSG_SIGNATURE) {
            console.log("Message with bad signature", msg)
            return
        }
        const seq = msg.seq
        const req = this.requests.get(seq)
        if (!req) {
            console.error("Unmatched message sequence: ", seq)
            return
        }
        const data = msg.data
        if (msg.type === DxfWorkerStatus.PROGRESS && req.progressCbk) {
            req.progressCbk(data.phase, data.size, data.totalSize)
            return
        }
        this.requests.delete(seq)
        if (msg.hasOwnProperty("error")) {
            req.SetError(msg.error)
        } else {
            req.SetResponse(data)
        }
    }

    async _OnError(error) {
        console.error("DxfWorker worker error", error)
        const reqs = Array.from(this.requests.values())
        this.requests.clear()
        reqs.forEach(req => req.SetError(error))
    }

    async _SendRequest(type, data:any = null, progressCbk?:Function) {
        const seq = this.reqSeq++
        const req = new WorkerRequest(seq, progressCbk)
        this.requests.set(seq, req)
        this.worker.postMessage({ seq, type, data, signature: MSG_SIGNATURE})
        return await req.GetResponse()
    }

    _SendProgress(seq, phase, size, totalSize) {
        this.worker.postMessage({
            seq,
            type: DxfWorkerStatus.PROGRESS,
            data: {phase, size, totalSize},
            signature: MSG_SIGNATURE
        })
    }

    /** @return {Object} DxfScene serialized scene. */
    async _Load(url, fonts, options, progressCbk) {
        let fontFetchers:any[]= []
        if (fonts) {
            fontFetchers = this._CreateFontFetchers(fonts, progressCbk)
        } 
        let dxf,ctor=this.isWasm?DxfWasmScene:DxfJsScene;
        const dxfScene:DxfWasmScene|DxfJsScene =new ctor(options)
        if(this.isWasm){
            const fileContent = await uploadFileAsText(url);
            dxf = new sg.SGDxfImport(fileContent as string)
           
            
            dxf.setId(dxfZsObj.drawingId)     
            if(! dxf.importDxf()) {
                console.error('Error: dxf file NOT imported.');
                return {scene: undefined, dxf: undefined}
            }     
        }else{
            dxf = await new DxfFetcher(url, options.fileEncoding).Fetch(progressCbk)
        }
        if (progressCbk) {
            progressCbk("prepare", 0, null)
        }
        await dxfScene.Build(dxf, fontFetchers)
        return {scene: dxfScene.sceneDa, dxf: (options.retainParsedDxf? dxf : undefined) }
    }

    _CreateFontFetchers(urls, progressCbk) {
        function CreateFetcher(url) {
            return async function() {
                if (progressCbk) {
                    progressCbk("font", 0, null)
                }
                const data = await fetch(url).then(response => response.arrayBuffer())
                if (progressCbk) {
                    progressCbk("prepare", 0, null)
                }
                return opentype.parse(data)
            }
        }

        const fetchers:any[] = []
        for (const url of urls) {
            fetchers.push(CreateFetcher(url))
        }
        return fetchers
    }

    _CloneOptions(options) {
        /* Default options values are taken from prototype so need to implement deep clone here. */
        if (Array.isArray(options)) {
            return options.map(o => this._CloneOptions(o))
        } else if (typeof options === "object" && options !== null) {
            const result = {}
            for (const propName in options) {
                // noinspection JSUnfilteredForInLoop
                result[propName] = this._CloneOptions(options[propName])
            }
            return result
        } else {
            return options
        }
    }
}



