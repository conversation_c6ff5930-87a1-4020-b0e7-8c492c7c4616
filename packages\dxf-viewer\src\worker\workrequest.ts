export class WorkerRequest{
    public seq:number
    public progressCbk?:Function
    public promise
    public _Resolve
    public _Reject
    constructor(seq:number, progressCbk?:Function) {
        this.seq = seq
        this.progressCbk = progressCbk
        this.promise = new Promise((resolve, reject) => {
            this._Resolve = resolve
            this._Reject = reject
        })
    }

    async GetResponse() {
        return await this.promise
    }

    SetResponse(response) {
        this._Resolve(response)
    }

    SetError(error) {
        this._Reject(error)
    }
}