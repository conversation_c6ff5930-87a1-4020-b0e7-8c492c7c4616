
/**
 * @property {{color: ?number, start: Vector2, end: Vector2}[]} lines
 * @property {{color: ?number, vertices: Vector2[]}[], indices: number[]} triangles On or more
 *  triangles in each item.
 * @property {{text: string, size: number, angle: number, color: number, position: Vector2}[]} texts
 *   Each item position is specified as middle point of the rendered text.
 */
export class DimensionLayout {
    public lines:any[]
    public triangles:any[]
    public texts:any[]
    constructor() {
        this.lines = []
        this.triangles = []
        this.texts = []
    }

    AddLine(start, end, color = null) {
        this.lines.push({start, end, color})
    }

    /** Add one or more triangles. */
    AddTriangles(vertices, indices, color = null) {
        this.triangles.push({vertices, indices, color})
    }

    AddText(text, size, angle, color, position) {
        this.texts.push({text, size, angle, color, position})
    }
}