import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial,MathUtils, Vector3,Line as ThLine } from "three"

export class RevCloud extends DrawHelp {
    //https://lee-mac.com/bulgeconversion.html#bulgearc
    private static readonly  theta_degree = 110;
    private static readonly  theta = MathUtils.degToRad(RevCloud.theta_degree);
    private static readonly  scaleUp = 1.6;
    private static readonly  scaleDown = 0.4;
    private static readonly  arcLength = 0.1;
    private static readonly  minArcLength = RevCloud.arcLength/50;
    private static readonly  Bulge = Math.tan(RevCloud.theta/4);
    private static readonly  chordLength = RevCloud.arcLength * Math.sin(RevCloud.theta/2) / RevCloud.theta;
    private static readonly  maxChordLength = RevCloud.chordLength * RevCloud.scaleUp;
    private static readonly  minChordLength = RevCloud.chordLength * RevCloud.scaleDown;


    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    // public color:string='white'
    public els:any=[]
    public draw:boolean=true
    public RevCloud:any
    private sgpolyline:sg.PolyLine2d

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        console.log(this.color,'this.color');
        
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init()
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        while (this.draw) {
            let p1=await this.lengthPick({
                tip:'指定下一个点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
        }
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    public override onRButtonDown( event:PointerEvent ) : boolean{
        this.draw=false
        this.complete()        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        // this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
            if(!this.pnts.length) return
            let vertices=this.getvertices(v)
            this.mesh.geometry.dispose();
            this.mesh.geometry=new BufferGeometry().setFromPoints(vertices);
    }
    
    complete(){
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        if(this.pnts.length<2) return
        const wasmVPlist=this.setWasmVPlist(this.pnts)
        let wasmBulge = new sg.vector_double()
        let  pl=new sg.PolyLine2d(wasmVPlist, wasmBulge, false)
        let  plobj=new sg.SGObjComBinCurve()
        plobj.setpCurve(pl)
        let [r,g,b]=this.color
        plobj.setColor(new sg.SGColor(r,g,b,255))
        plobj.setLineType(this.lineType)
        this.els.push(plobj)
        this.viewer.transaction.add(this.els)
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
    }
    private setWasmVPlist(vList){
        let {x,y}=globalDxf.wcsOff
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x+x, item.y+y))
        })
        return wasmVPlist
    }
    private getvertices(pt2:THREE.Vector3) {
        const pt0 = this.pnts[0];
        const pt1 = new Vector3(pt2.x, pt0.y, 0);
        const pt3 = new Vector3(pt0.x, pt2.y, 0);
        const w = Math.abs(pt2.x - pt0.x)
        const h = Math.abs(pt2.y - pt0.y)

        let loop: THREE.Vector3[]= []
        let vertices:THREE.Vector3[]=[]

        if((w+h) < RevCloud.maxChordLength){
            loop = [pt0, pt2, pt0]
            vertices = this.polyline(loop)

        } else {
            const pts = [pt0, pt1, pt2, pt3, pt0]
            //check clockwise or counterclockwise
            //https://stackoverflow.com/a/1165943/7339351
            let sum = 0;
            for(let i = 0; i< (pts.length-1); i++){
                const ptStart = pts[i]
                const ptEnd = pts[i+1]
                const x1 = ptStart.x
                const y1 = ptStart.y
                const x2 = ptEnd.x
                const y2 = ptEnd.y
                sum = sum + (x2-x1)*(y2+y1)
            }
            //let sign = 1;
            if(sum > 0){//make it counterclockwise
                pts.reverse()
            }
    
            loop = this.getChords(pts)
    
            vertices = this.polyline(loop)
        }

        return vertices
    }

    private polyline(loop:THREE.Vector3[]){

        let arcsSeg:THREE.Vector3[]= []
        arcsSeg = []
        const plist= new sg.vector_Point2d()
        const bulge = new sg.vector_double()

        const nArc = loop.length
        for(let i = 0; i < nArc; i++){
            const pt3d = loop[i]
            plist.push_back(new sg.Point2d(pt3d.x, pt3d.y))
            bulge.push_back(RevCloud.Bulge)
        }

        this.sgpolyline=new sg.PolyLine2d(plist, bulge, true)
        const n = this.sgpolyline.curveCount()
        //let arcsSeg:THREE.Vector3[]= []
        for(let i =0; i< n; i++){
            const curve = this.sgpolyline.getBoundCurve(i)
            
            const plist = curve.getInterpolatePoints(30) as sg.vector_Point2d
            for(let j = 0; j < 30; j++){
                const pt = plist.get(j)
                const x = pt.x()
                const y = pt.y()
                arcsSeg.push(new Vector3(x, y,0))
            }

        }
        return arcsSeg
    }

    private getChords(loop:THREE.Vector3[] ){
        const n = loop.length - 1
        let sumLength = 0
        let curr = 0;
        let point = loop[0];
        const vertices = [new Vector3().copy(point)];
        while(true){
            let ptStart = loop[curr]
            let ptEnd = loop[curr + 1]
            const length = ptStart.distanceTo(ptEnd)
            let vec = new Vector3().subVectors(ptEnd, ptStart).normalize()
            sumLength = sumLength + length
            while(true){
                let chordLength = this.getRandomArbitrary(RevCloud.minChordLength, RevCloud.maxChordLength)
                
                while(sumLength < chordLength) {
                    curr++
                    if(curr === n) {
                        vertices.push(new Vector3().copy(loop[curr]))
                        return vertices
                    }
                    chordLength = chordLength - sumLength;
                    
                    ptStart = loop[curr]
                    ptEnd = loop[curr + 1]
                    point = new Vector3().copy(ptStart)
                    sumLength = ptStart.distanceTo(ptEnd)
                    vec = new Vector3().subVectors(ptEnd, ptStart).normalize()
                }
                const advanceVec = new Vector3().copy(vec).multiplyScalar(chordLength)

                point = new Vector3().addVectors(point, advanceVec)
                vertices.push(point)
                sumLength = sumLength - chordLength
            }
        }
    }

    private getRandomArbitrary(min:number, max:number) {
        return Math.random() * (max - min) + min;
    }

}






// import * as THREE from "three"
// import {ElementStyle} from './ElementStyle'
// export class RevCloud extends ElementStyle {
//     //https://lee-mac.com/bulgeconversion.html#bulgearc
//     private static readonly  theta_degree = 110;
//     private static readonly  theta = THREE.MathUtils.degToRad(RevCloud.theta_degree);
//     private static readonly  scaleUp = 1.6;
//     private static readonly  scaleDown = 0.4;
//     private static readonly  arcLength = 0.1;
//     private static readonly  minArcLength = RevCloud.arcLength/50;
//     private static readonly  Bulge = Math.tan(RevCloud.theta/4);
//     private static readonly  chordLength = RevCloud.arcLength * Math.sin(RevCloud.theta/2) / RevCloud.theta;
//     private static readonly  maxChordLength = RevCloud.chordLength * RevCloud.scaleUp;
//     private static readonly  minChordLength = RevCloud.chordLength * RevCloud.scaleDown;

//     public vPlist:THREE.Vector3[]=[]
//     public count:number=0
//     public iscomplete:boolean=false
//     public RevCloud:any
//     private sgpolyline:sg.PolyLine2d
//     public gp:any
//     constructor() {
//         // 调用父类的构造函数以初始化继承的属性
//         super();
//         // 创建直线的几何体
//         const geometry = new THREE.BufferGeometry();
//         // 生成圆形顶点数据
//         const vertices:any = [];
//         geometry.setFromPoints(vertices);
//         const material = new THREE.LineBasicMaterial({ color:this.color});
//         this.RevCloud = new THREE.LineLoop(geometry, material);
//     }
//     setV(v:THREE.Vector3){
//         this.count++
//         this.vPlist.push(v)
//         switch ( this.count) {
// 			case 1:
// 				break;
// 			case 2:
//                 this.gp=new sg.SGObjComBinCurve()
//                 this.gp.setpCurve(this.sgpolyline)
//                 this.iscomplete=true
//                 break;
// 			default:
//                 break;
// 		}
//     }
//     getmove(v:THREE.Vector3){
//         if(!this.vPlist.length) return
//         let vertices=this.getvertices(v)
//         this.RevCloud.geometry.dispose();
//         this.RevCloud.geometry=new THREE.BufferGeometry().setFromPoints(vertices);

//     }
//     complete(){
//         if(this.vPlist.length<2) return
//         let vertices=this.getvertices(this.vPlist[1])
//         this.RevCloud.geometry.dispose();
//         this.RevCloud.geometry=new THREE.BufferGeometry().setFromPoints(vertices);


//     }
    
//     getisitCompleted(){
//         return this.iscomplete
//     }

//     private getRandomArbitrary(min:number, max:number) {
//         return Math.random() * (max - min) + min;
//     }



//     private getChords(loop:THREE.Vector3[] ){
//         const n = loop.length - 1
//         let sumLength = 0
//         let curr = 0;
//         let point = loop[0];
//         const vertices = [new THREE.Vector3().copy(point)];
//         while(true){
//             let ptStart = loop[curr]
//             let ptEnd = loop[curr + 1]
//             const length = ptStart.distanceTo(ptEnd)
//             let vec = new THREE.Vector3().subVectors(ptEnd, ptStart).normalize()
//             sumLength = sumLength + length
//             while(true){
//                 let chordLength = this.getRandomArbitrary(RevCloud.minChordLength, RevCloud.maxChordLength)
                
//                 while(sumLength < chordLength) {
//                     curr++
//                     if(curr === n) {
//                         vertices.push(new THREE.Vector3().copy(loop[curr]))
//                         return vertices
//                     }
//                     chordLength = chordLength - sumLength;
                    
//                     ptStart = loop[curr]
//                     ptEnd = loop[curr + 1]
//                     point = new THREE.Vector3().copy(ptStart)
//                     sumLength = ptStart.distanceTo(ptEnd)
//                     vec = new THREE.Vector3().subVectors(ptEnd, ptStart).normalize()
//                 }
//                 const advanceVec = new THREE.Vector3().copy(vec).multiplyScalar(chordLength)

//                 point = new THREE.Vector3().addVectors(point, advanceVec)
//                 vertices.push(point)
//                 sumLength = sumLength - chordLength
//             }
//         }
//     }

//     private drawArc(ptStart:THREE.Vector3, ptEnd:THREE.Vector3, rotate90:THREE.Quaternion):THREE.Vector3[] {
//         let arcsSeg = [ptStart, ptEnd]
//         let B = RevCloud.Bulge
//         while(true){
//             const chordLength = new THREE.Vector3().subVectors(arcsSeg[0], arcsSeg[1]).length()
//             if(chordLength < RevCloud.minArcLength)
//                 return arcsSeg
//             const s = chordLength/2* B
//             let arcsSegCopy:THREE.Vector3[] = [arcsSeg[0]]
//             const n = arcsSeg.length - 1
//             for(let i = 0; i < n; i++){
//                 ptStart =arcsSeg[i]
//                 ptEnd = arcsSeg[i+1]
//                 const chord = new THREE.Vector3().subVectors(ptEnd, ptStart)
//                 const chordVecNorm = new THREE.Vector3().copy(chord).divideScalar(chordLength)
//                 //const vecRotate90 = chordVecNorm.applyQuaternion(rotate90)
//                 const vecRotate90 = new THREE.Vector3(chordVecNorm.y, -chordVecNorm.x, 0)
//                 const ptMid = new THREE.Vector3().addVectors(ptStart, new THREE.Vector3().copy(chord).divideScalar(2))
    
//                 const midOffset = new THREE.Vector3().addVectors(vecRotate90.multiplyScalar(s), ptMid)
//                 arcsSegCopy.push(midOffset)
//                 arcsSegCopy.push(ptEnd)
//             }
//             arcsSeg = arcsSegCopy
//             B=(-1+Math.sqrt(1+B*B))/B
//         }
//     }

//     private polyline(loop:THREE.Vector3[]){

//         let arcsSeg:THREE.Vector3[]= []
//         // var startTime = performance.now()
//         // const rotate90 = new THREE.Quaternion().setFromAxisAngle( new THREE.Vector3(0, 0, 1),   Math.PI/2);

//         // for(let i = 0; i < loop.length - 1; i++){
//         //     const ptStart = loop[i]
//         //     const ptEnd = loop[i+1]
//         //     const arcSegtemp= this.drawArc(ptStart, ptEnd, rotate90)
//         //     arcsSeg = arcsSeg.concat(arcSegtemp)
//         // }
//         // var endTime = performance.now()
//         // console.log(`Call to js code took ${endTime - startTime} milliseconds. arcsSeg have ${arcsSeg.length} points`)
//         // //return arcsSeg
//         // startTime = performance.now()
//         arcsSeg = []
//         const plist= new sg.vector_Point2d()
//         const bulge = new sg.vector_double()

//         const nArc = loop.length
//         for(let i = 0; i < nArc; i++){
//             const pt3d = loop[i]
//             plist.push_back(new sg.Point2d(pt3d.x, pt3d.y))
//             bulge.push_back(RevCloud.Bulge)
//         }

//         this.sgpolyline=new sg.PolyLine2d(plist, bulge, true)
//         const n = this.sgpolyline.curveCount()
//         //let arcsSeg:THREE.Vector3[]= []
//         for(let i =0; i< n; i++){
//             const curve = this.sgpolyline.getBoundCurve(i)
            
//             const plist = curve.getInterpolatePoints(30) as sg.vector_Point2d
//             for(let j = 0; j < 30; j++){
//                 const pt = plist.get(j)
//                 const x = pt.x()
//                 const y = pt.y()
//                 arcsSeg.push(new THREE.Vector3(x, y,0))
//             }

//         }
//         // endTime = performance.now()
//         // console.log(`Call to js wasm code took ${endTime - startTime} milliseconds. arcsSeg have ${arcsSeg.length} points`)
//         return arcsSeg

//     }

//     private getvertices(pt2:THREE.Vector3) {
//         const pt0 = this.vPlist[0];
//         const pt1 = new THREE.Vector3(pt2.x, pt0.y, 0);
//         const pt3 = new THREE.Vector3(pt0.x, pt2.y, 0);
//         const w = Math.abs(pt2.x - pt0.x)
//         const h = Math.abs(pt2.y - pt0.y)

//         let loop: THREE.Vector3[]= []
//         let vertices:THREE.Vector3[]=[]

//         if((w+h) < RevCloud.maxChordLength){
//             loop = [pt0, pt2, pt0]
//             vertices = this.polyline(loop)

//         } else {
//             const pts = [pt0, pt1, pt2, pt3, pt0]
//             //check clockwise or counterclockwise
//             //https://stackoverflow.com/a/1165943/7339351
//             let sum = 0;
//             for(let i = 0; i< (pts.length-1); i++){
//                 const ptStart = pts[i]
//                 const ptEnd = pts[i+1]
//                 const x1 = ptStart.x
//                 const y1 = ptStart.y
//                 const x2 = ptEnd.x
//                 const y2 = ptEnd.y
//                 sum = sum + (x2-x1)*(y2+y1)
//             }
//             //let sign = 1;
//             if(sum > 0){//make it counterclockwise
//                 pts.reverse()
//             }
    
//             loop = this.getChords(pts)
    
//             vertices = this.polyline(loop)
//         }

//         return vertices
//     }
// }