import { BatchEntityType } from "../constant"
import { CompareValues } from "./compare"

/** Key for render batches. */
export class BatchingKey {
    /**
     * Components order matters for lookup by prefix.
     * @param layerName {?String} Layer name, null if not bound to a layer (e.g. block definition).
     * @param blockName {?String} Block name if applicable. If specified and geometryType is not
     *  BLOCK_INSTANCE, the batch is part of block definition. Otherwise it is block instance.
     * @param geometryType {?number} One of BatchEntityType.
     * @param color {number} Color ARGB value.
     * @param lineType {?number} Line type ID, null for non-lines. Zero is default type (solid
     *  line).
     */
    public layerName:string
    public blockName:string
    public geometryType:number
    public color:string
    public lineType:number
    constructor(layerName, blockName, geometryType, color, lineType) {
        this.layerName = layerName ?? null
        this.blockName = blockName ?? null
        this.geometryType = geometryType ?? null
        this.color = color
        this.lineType = lineType ?? null
    }

    /** Comparator function. Fields lexical order corresponds to the constructor arguments order.
     * Null values are always first.
     */
    Compare(other) {
        let c = CompareValues(this.layerName, other.layerName)
        if (c !== 0) {
            return c
        }
        c = CompareValues(this.blockName, other.blockName)
        if (c !== 0) {
            return c
        }
        c = CompareValues(this.geometryType, other.geometryType)
        if (c !== 0) {
            return c
        }
        c = CompareValues(this.color, other.color)
        if (c !== 0) {
            return c
        }
        return CompareValues(this.lineType, other.lineType)
    }

    IsIndexed() {
        return this.geometryType === BatchEntityType.INDEXED_LINES ||
               this.geometryType === BatchEntityType.INDEXED_TRIANGLES||
               this.geometryType === BatchEntityType.DASH_LINES
    }

    IsInstanced() {
        return this.geometryType === BatchEntityType.BLOCK_INSTANCE ||
               this.geometryType === BatchEntityType.POINT_INSTANCE
    }
    IsPoint(){
        return this.geometryType===BatchEntityType.POINTS ||
                this.geometryType === BatchEntityType.POINT_INSTANCE
    }
    IsDashLine(){
        return this.geometryType===BatchEntityType.DASH_LINES||
                this.geometryType===BatchEntityType.DASH_SEGMENTS
    }
}


