
if (!Array.prototype.removeLast) {
    Array.prototype.removeLast = function () {
      this.pop();
      return this;
    };
  }
  
  if(!Array.prototype.absSum){
      Array.prototype.absSum=function(){
          return this.reduce((prev, curr, idx, arr)=>{
              return Math.abs(prev) + Math.abs(curr);
          })
      }
  }
  if(!Array.prototype.absNormal){
    Array.prototype.absNormal=function(n:number=1){
       let abslen=this.absSum()
       let newvals=this.map(e=>e*n/abslen)
       return newvals
    }
  }