import { Vector2 } from "three"
import { DimensionLayout } from "./DimensionLayout"
import { ParseSpecialChars } from "./text"

export class RadialDimension {

    /**
     * @typedef RadialDimensionParams
     * @property {Vector2} center Circle center point.
     * @property {Vector2} chord Circle chord point.
     * @property {double} leader Leader width.
     * @property {?string} text Dimension text pattern.
     * @property {?Vector2} textAnchor Text location (middle point) override.
     *  orientation (the direction of the dimension line)
     */

    public params
    public styleResolver
    public textWidthCalculator
    public isValid
    public vDimNorm
    public vBase
    public vDim
    public d1
    public d2
    constructor(params, styleResolver, textWidthCalculator) {
        this.params = params
        this.styleResolver = styleResolver
        this.textWidthCalculator = textWidthCalculator
        /* Can be set to indicate some invalid geometric solution.  */
        this.isValid = true
        this._CalculateGeometry()
    }

    IsValid() {
        return this.isValid
    }

    GetTexts() {
        return [this._GetText()]
    }

    /**
     * @return {DimensionLayout}
     */
    GenerateLayout() {
        /* See https://ezdxf.readthedocs.io/en/stable/tables/dimstyle_table_entry.html */
        const result = new DimensionLayout()

        const dimScale = 1;
        const text = this._GetText()
        const fontSize = (this.styleResolver("DIMTXT") ?? 1) * dimScale
        const angle = this.vDimNorm.angle() * 180 / Math.PI - 90 + (this.params.textRotation ?? 0)
        const textColor = this.styleResolver("DIMCLRT")
        let textAnchor = this.params.textAnchor

        result.AddText(text, fontSize, angle, textColor, textAnchor)
        return result;
    }

    /** Calculate and set basic geometric parameters (some points and vectors which define the
     * dimension layout).
     */
    _CalculateGeometry() {
        this.vBase = this.params.chord.clone().sub(this.params.center).normalize()
        this.vDim = this.vBase

        if (this.vDim.y < -this.vDim.x) {
            this.vDimNorm = new Vector2(this.vDim.y, -this.vDim.x)
        } else {
            this.vDimNorm = new Vector2(-this.vDim.y, this.vDim.x)
        }
    }

    _GetText() {
        if (this.params.text == " ") {
            /* Space indicates empty text. */
            return ""
        }
        if ((this.params.text ?? "") != "" && this.params.text.indexOf("<>") == -1) {
            /* No value placeholder, just return the text. */
            return ParseSpecialChars(this.params.text)
        }

        let measurement = this.d2.distanceTo(this.d1)
        measurement *= this.styleResolver("DIMLFAC") ?? 1

        const rnd = this.styleResolver("DIMRND") ?? 0
        if (rnd > 0) {
            const n = Math.round(measurement / rnd)
            measurement = rnd * n
        }

        const zeroSupp = this.styleResolver("DIMZIN") ?? 0
        const leadZeroSupp = (zeroSupp & 4) != 0
        const trailingZeroSupp = (zeroSupp & 8) != 0

        let measText = measurement.toFixed(this.styleResolver("DIMDEC") ?? 2)

        if (trailingZeroSupp) {
            measText = measText.replace(/.0+$/, "")
        }

        if (leadZeroSupp) {
            measText = measText.replace(/^0+/, "")
        }

        if (measText.startsWith(".")) {
            measText = "0" + measText
        } else if (measText == "") {
            measText = "0"
        }
        if (measText.endsWith(".")) {
            measText = measText.substring(0, measText.length - 1)
        }

        let decSep = this.styleResolver("DIMDSEP") ?? "."
        if (!isNaN(decSep)) {
            decSep = String.fromCharCode(decSep)
        }
        if (decSep != ".") {
            measText = measText.replace(".", decSep)
        }

        const suffix = this.styleResolver("DIMPOST") ?? ""
        if (suffix != "") {
            if (suffix.indexOf("<>") != -1) {
                measText = suffix.replaceAll("<>", measText)
            } else {
                measText += suffix
            }
        }

        if ((this.params.text ?? "") != "") {
            measText = this.params.text.replaceAll("<>", measText)
        }

        return ParseSpecialChars(measText)
    }
}