<!-- eslint-disable vue/no-mutating-props -->
<template>
    <div class="insertImgPop">
        <el-dialog
            :model-value="fillingPopVisible"
            title="单行文字"
            width="300"
        >
            <div style="margin-top: 20px;font-size: 12px;">
              <div class='sq zpointer' @click="btnSq">
                <div class="btn" ></div>添加识取点
              </div>
            </div>
            <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="btnSave">确认</el-button>
              <el-button @click="btnCancellation">关闭</el-button>
            </div>
            </template>
        </el-dialog>
    </div>
  </template>
  
  <script setup>
    import { ref } from 'vue'
    const emits = defineEmits(['btnfillingPopVisibleSq'])
    const props = defineProps({
      fillingPopVisible:{
          type: Boolean,
          default:false
       }
    })
    let content=ref('测试')
    let rotation=ref(0)
    let contentsize=ref(20)
    const cleanFn=()=>{
      content.value='测试'
      rotation.value=0
      contentsize.value=20
    }
    const handleClose=()=>{
        emits('handleClose3')
    }
    
    //文字弹框
    const btnSave=()=>{
      let obj={
        content:content.value,
        rotation:-rotation.value,
        contentsize:contentsize.value,
      }
        emits('btnSavefillingPopVisible',obj)
    }
    const btnCancellation=()=>{
      emits('btnCancelFillingPopVisible')
    }
  
    const btnSq=()=>{
      emits('btnfillingPopVisibleSq')
    }
  
    defineExpose({
        cleanFn
    })
  
  </script>
  
  <style lang="scss" scoped>
  .insertImgPop {
    :deep(.el-dialog) {
      background-color: #3a4352;
    }
    :deep(.el-dialog__body) {
        padding:0 !important;
    }
    :deep(.el-dialog__headerbtn) {
      width: 16px !important;
      height: 26px !important;
      top:1px;
      
    }
    :deep(.el-dialog__header) {
      padding:0 !important;
      padding-bottom:0;
      
      .el-dialog__title {
        font-size:16px;
        color: #fff !important;
      }
    }
    .sq {
      display: flex;
      align-items: center;
      .btn {
        width: 8px;
        height: 8px;
        background-color: blue;
      }
    }
  }
  </style>
    
    