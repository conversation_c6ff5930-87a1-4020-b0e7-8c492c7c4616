<template>
  <div class="drawhelptoolbar">
    <div class="btnBox" v-for="(item, index) in toolbarDrawHelpBtns" :key="index">
      <el-tooltip placement="left-end" :content="item.tip">
        <div
          :class="item.name === 'dimension' || item.name === 'RotatedDimension' || item.name === 'AlignedDimension' ? '' : 'bgc'"
          style="width: 40px;height: 40px;" @click="createShape(item)">
          <svg-icon :iconClass="item.icon" w=40 h=40 style="padding-top:0px"></svg-icon>
        </div>
      </el-tooltip>
    </div>
  </div>
  <copyarray ref="refcopyarray" @onConfirm="onCopy"></copyarray>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { toolbarDrawHelpBtns } from '@/store/toolbartips'
import { pickupInfo } from 'dxf-viewer'
import copyarray from './popFrame/copyarray.vue'
import { PubSub } from 'emitter';
const refcopyarray = ref()
const createShape = (item) => {
  console.log(item.name, 'item.nameitem.name');
  if (item.name == "copyarray") {
    if (pickupInfo.pickupIdlist.length > 0) {
      refcopyarray.value.show()
    } else {
      ElMessage({ message: "请选择对象" })
    }
  } else {
    PubSub.default.pub('executeCommand', item.name)
  }
}
const onCopy = (da) => {

}

</script>


<style lang="scss" scoped>
.drawhelptoolbar {
  flex: 0 0 auto;
  background-color: #3a4352;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap-reverse;

  border-top: 1px solid #4f4f4f;

  .btnBox {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    margin: 10px 5px;

    &:hover {
      background-color: #616975;
    }
  }

  .bgc {
    background-color: #4f4f4f;
  }
}
</style>