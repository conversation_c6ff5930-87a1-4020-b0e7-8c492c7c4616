<template>
    <div ref="container" class="scene-container"></div>
</template>
  
<script setup>
  import * as THREE from 'three';
  import { onMounted, onUnmounted, ref } from 'vue';
  import L from 'leaflet';
  import { useResizeObserver } from '@vueuse/core';
  
  const container = ref(null);
  
  onMounted(() => {
    const map = L.map(container.value).setView([51.505, -0.09], 13);
    <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19,
    }).addTo(map);
  
    let renderer, scene, camera, mesh;
  
    const init = () => {
      // 创建场景
      scene = new THREE.Scene();
  
      // 创建相机
      camera = new THREE.PerspectiveCamera(75, container.value.clientWidth / container.value.clientHeight, 0.1, 1000);
      camera.position.z = 1;
  
      // 创建渲染器
      renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
      renderer.setSize(container.value.clientWidth, container.value.clientHeight);
      container.value.appendChild(renderer.domElement);
  
      // 获取地图图像
      const image = new Image();
      image.src = map.getPanes().overlayPane.childNodes[0].toDataURL();
      image.onload = () => {
        const texture = new THREE.TextureLoader().load(image.src);
        const material = new THREE.MeshBasicMaterial({ map: texture });
  
        // 创建平面几何体
        const geometry = new THREE.PlaneGeometry(2, 2); // 调整大小以适应纹理
        mesh = new THREE.Mesh(geometry, material);
        scene.add(mesh);
      };
  
      // 更新渲染器大小
      const resizeRendererToDisplaySize = () => {
        const width = container.value.clientWidth;
        const height = container.value.clientHeight;
        const needResize = renderer.getSize().width !== width || renderer.getSize().height !== height;
        if (needResize) {
          renderer.setSize(width, height);
          camera.aspect = width / height;
          camera.updateProjectionMatrix();
        }
        return !needResize;
      };
  
      // 动画循环
      const animate = () => {
        requestAnimationFrame(animate);
        renderer.setPixelRatio(window.devicePixelRatio);
        if (resizeRendererToDisplaySize()) {
          camera.updateProjectionMatrix();
        }
        renderer.render(scene, camera);
      };
      animate();
    };
  
    // 初始化
    init();
  
    // 监听窗口大小变化
    useResizeObserver(container.value, () => {
      if (renderer && camera) {
        renderer.setSize(container.value.clientWidth, container.value.clientHeight);
        camera.aspect = container.value.clientWidth / container.value.clientHeight;
        camera.updateProjectionMatrix();
      }
    });
  
    // 清理函数
    onUnmounted(() => {
      renderer.dispose();
      map.remove();
    });
  });
  </script>
  
  <style scoped>
  .scene-container {
    position: relative;
    width: 100%;
    height: 400px;
  }
  </style>