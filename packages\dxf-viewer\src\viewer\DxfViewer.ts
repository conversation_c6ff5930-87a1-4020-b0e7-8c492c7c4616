import { Batch } from "../batch"
import * as THREE from "three"
import { BatchEntityType, defaultDxfViewerOptions, DynamicSceneType, EVENT_NAME_PREFIX, InstanceType, MessageLevel } from "../constant"
import { createFontFetchers, uploadFileAsText } from "../libs"
import { DxfWasmScene, DynamicScene, LodManage, Snap, SlIdentifiedScene, SlIdentifiedLsScene, DxfGrayManage } from "../scene"
import { scene2Base64 } from "../libs"
import { PubSub } from 'emitter';
import { Layer } from "./layer"
import { Block } from "./block"
import { ContrastRatio, Darken, Lighten, Luminance } from "./color"
import { Transaction, dxfZsObj, globalConf, globalDxf, pickupInfo, initDxfBaseData } from "../global"
import { FloatType, MOUSE, OrthographicCamera, TOUCH, Vector3, <PERSON><PERSON><PERSON><PERSON><PERSON>, WebGLRenderTarget } from "three"
import { DxfTransientScene } from "../scene/DxfTransientScene"
import { DxfTransientSceneWasm } from "../scene/DxfTransientSceneWasm"
import { GraphicContorls, Selector, OrbitControls, wcs2screen, screen2wcs } from "../controls"
import { ViewContorls } from "../controls/viewControls"
import { CadMessageDispatch } from "../../../dxfdraw"
import { SelectScene } from "../../../dxfdraw/gripedit"
import { DxfMaterials, getLineStyleUniforms } from "./DxfMaterials"

import { TDiff } from "../libs/timediff"
import { MyEvent } from "src/mapviewsync/myevent"


/** The representation class for the viewer, based on Three.js WebGL renderer. */
export class DxfViewer extends MyEvent {
    /** @param domContainer Container element to create the canvas in. Usually empty div. Should not
     *  have padding if auto-resize feature is usdxfviewdxfviewdxfviewdxfviewdxfviewdxfviewdxfviewdxfviewdxfviewdxfviewdxfviewdxfviewdxfviewed.
     * @param options Some options can be overridden if specified. See DxfViewer.DefaultOptions.
     */
    public domContainer;
    public options;
    public clearColor;
    public scene;
    public renderer: WebGLRenderer;
    public camera;
    public visual: boolean = true
    public canvasWidth;
    public canvasHeight;
    public resizeObserver;
    public canvas;
    public highlightScene;
    public selectionScene;
    public selectedScene;
    public selector: Selector;
    public idScene;
    public snap: Snap;
    public idRenderTarget;
    public dxfmats: DxfMaterials;
    public layers;
    public blocks;
    public controls;

    public origin;
    public bounds;
    public dxfScene: DxfWasmScene;
    public transaction: Transaction;
    // public slOperationType //算量操作类型(比例尺、拾取、文字识别.......)
    //public transientScene
    public transientSceneWasm;
    public viewControl: ViewContorls;
    public grapicControl: GraphicContorls;
    public cadMessageDispatch: CadMessageDispatch;
    public lod: LodManage;
    private selectedId;
    public slIdentifiedscene: SlIdentifiedScene;
    public slIdentifiedLSscene: SlIdentifiedLsScene;
    public graymanage: DxfGrayManage = DxfGrayManage.getInstance()
    public get screenErrThreshold() {
        //夹点编辑用
        let domW = this.canvas.clientWidth;
        let pixW = (this.camera.right - this.camera.left) / this.camera.zoom;
        let err = (globalConf.defaultPointSize * pixW) / domW;
        return err;
    }
    public _backgroundcolor = [0, 0, 0]
    public _backgroundalpah = 0.1

    constructor(domContainer, options: any) {
        super()
        this.domContainer = domContainer;
        this.options = Object.create(defaultDxfViewerOptions);
        if (options) {
            Object.assign(this.options, options);
        }
        options = this.options;

        this.clearColor = this.options.clearColor.getHex();

        const renderer = (this.renderer = new WebGLRenderer({
            alpha: options.canvasAlpha,
            premultipliedAlpha: options.canvasPremultipliedAlpha,
            antialias: options.antialias,
            depth: false,
            preserveDrawingBuffer: options.preserveDrawingBuffer,
        }));
        // console.log(this.renderer,'this.renderer');
        this.renderer.setClearColor(0xff0000, 0);
        renderer.sortObjects = false;
        renderer.setPixelRatio(window.devicePixelRatio);

        const camera = (this.camera = new OrthographicCamera(-1, 1, 1, -1, 0.1, 2));
        camera.position.z = 1;
        camera.position.x = 0;
        camera.position.y = 0;

        if (options.autoResize) {
            this.canvasWidth = domContainer.clientWidth;
            this.canvasHeight = domContainer.clientHeight;
            domContainer.style.position = "relative";
        } else {
            this.canvasWidth = options.canvasWidth;
            this.canvasHeight = options.canvasHeight;
            this.resizeObserver = null;
        }
        renderer.setSize(this.canvasWidth, this.canvasHeight);

        this.canvas = renderer.domElement;
        domContainer.style.display = "block";
        if (options.autoResize) {
            this.canvas.style.position = "absolute";
            this.resizeObserver = new ResizeObserver((entries) =>
                this._OnResize(entries[0])
            );
            this.resizeObserver.observe(domContainer);
        }
        domContainer.appendChild(this.canvas);

        this.dxfmats = new DxfMaterials();
        this.dxfScene = new DxfWasmScene(this, this.options);

        this.scene = this.dxfScene.scene;
        this.idScene = this.dxfScene.idScene;

        // 
        // this.scene.visible = false

        // this.cadMessageDispatch=new CadMessageDispatch.apply(this)
        this.cadMessageDispatch = new CadMessageDispatch(this);
        this._CreateControls();
        this.FitView(0, 0, 0, 0);
        this.viewControl = new ViewContorls(this);
        this.selector = new Selector(this);
        this.grapicControl = new GraphicContorls(this);
        this.snap = new Snap(this);
        this.lod = new LodManage(this);
        /** For highlight and selection */
        this.highlightScene = new DynamicScene(this, DynamicSceneType.HighLight);
        this.selectionScene = new SelectScene(this);
        this.selectedScene = new DynamicScene(this, DynamicSceneType.Selected);

        //this.transientScene = new DxfTransientScene(this);
        this.transientSceneWasm = new DxfTransientSceneWasm(this);
        this.idRenderTarget = new WebGLRenderTarget(
            this.canvasWidth,
            this.canvasHeight,
            {
                type: FloatType,
            }
        );
        this.slIdentifiedscene = new SlIdentifiedScene(this);
        this.slIdentifiedLSscene = new SlIdentifiedLsScene(this);
        this.Render();
        /* Indexed by layer name, value is Layer instance. */
        this.layers = new Map();
        /* Indexed by block name, value is Block instance. */
        this.blocks = new Map();

        window.addEventListener("message", this.createdGpwasm.bind(this));
        // window.addEventListener("wasm",e=>{
        //     console.log('xxxxxxxxxxxx');

        //     that.createdGpwasm.bind(that)
        // });

        PubSub.default.sub("showlayer", this._showlayer.bind(this));
        PubSub.default.sub("layercolorchanged", this.layercolorchange.bind(this));
        PubSub.default.sub("fitview", this.fitFullView.bind(this));
        PubSub.default.sub("testlod", this.test.bind(this));
        PubSub.default.sub("layernamechanged", this.layernamechange.bind(this));
    }

    /**
     * @param alpha 透明度 范围集合：[0,1]
     */
    public setBackgroundAlpha(alpha:number) {
        this._backgroundalpah = alpha;
    }
    public setBackgroundColor(rgba:[number,number,number]) {
        this._backgroundcolor = [rgba[0], rgba[1], rgba[2]]
    }
    public test() {
        this.lod.require();
    }

    public canvasvisibility(string) {
        this.canvas.style.visibility = string;
    }

    public gpShow(gp) {
        this.transaction.gpObj = gp;
        this.rebuildScene(true);
    }

    public layercolorchange(lay) {
        let gp = this.transaction.gpObj;
        let dxfbase = gp.getDxfBaseData();
        let layer = dxfbase.getLayer(lay.idx);
        let [r, g, b] = lay.color;
        layer.setColor(new sg.SGColor(r, g, b, 255));
        dxfbase.addLayer(layer);
        this.rebuildScene();
    }
    public layernamechange = (lay) => {
        let gp = this.transaction.gpObj;
        let dxfbase = gp.getDxfBaseData();
        dxfbase.changeLayerName(lay.usedName, lay.newname);
        this.rebuildScene();
    };

    /** @return {boolean} True if renderer exists. May be false in case when WebGL context is lost
     * (e.g. after wake up from sleep). In such case page should be reloaded.
     */
    HasRenderer() {
        // console.log(this.renderer,'this.renderer');
        return Boolean(this.renderer);
    }

    /**
     * @returns {three.WebGLRenderer | null} Returns the created Three.js renderer.
     */
    GetRenderer() {
        return this.renderer;
    }

    createdGpwasm(event) {//event

        if (!window.sg) return
        // if (event.origin !== window.location.origin) return; // 可选的安全检查
        setTimeout(async () => {
            this.transaction = new Transaction(this)
            await initDxfBaseData(this.transaction.gpObj)//初始化图层
            // Object.assign(globalDxf.lineStyleUniforms, getLineStyleUniforms())
            // console.log(this.transaction,'执行');
        }, 10)


        // if (event.data.type === 'Wasm') {
        //  this.transaction=new Transaction(this)
        // }
    }



    GetCanvas() {
        return this.canvas;
    }

    SetSize(width, height) {
        console.log("SetSize", width, height);

        this._EnsureRenderer();

        const hScale = width / this.canvasWidth;
        const vScale = height / this.canvasHeight;

        const cam = this.camera;
        const centerX = (cam.left + cam.right) / 2;
        const centerY = (cam.bottom + cam.top) / 2;
        const camWidth = cam.right - cam.left;
        const camHeight = cam.top - cam.bottom;

        cam.left = centerX - (hScale * camWidth) / 2;
        cam.right = centerX + (hScale * camWidth) / 2;
        cam.bottom = centerY - (vScale * camHeight) / 2;
        cam.top = centerY + (vScale * camHeight) / 2;
        cam.updateProjectionMatrix();

        this.canvasWidth = width;
        this.canvasHeight = height;
        this.renderer.setSize(width, height);
        this.idRenderTarget.setSize(width, height);
        // this.canvas.style.opacity=0.1
        if (this.controls) {
            this.controls.update();
        }
        this._Emit("resized", { width, height });
        this._Emit("viewChanged");
        this.Render();
    }

    async Load({ url, fonts = null, progressCbk }) {
        TDiff.start();
        if (url === null || url === undefined) {
            throw new Error("`url` parameter is not specified");
        }
        this._EnsureRenderer();
        let fontFetchers: any[] = [];
        if (fonts) {
            fontFetchers = createFontFetchers(fonts, progressCbk);
        }
        const fileContent = await uploadFileAsText(url);
        let dxf: any = new sg.SGDxfImport(fileContent as string);
        //getGCPoints 获取高程点

        dxf.setId(dxfZsObj.drawingId);
        if (!dxf.importDxf()) {
            console.error("Error: dxf file NOT imported.");
            return;
        }
        if (progressCbk) {
            progressCbk("prepare", 0, null);
        }
        this.queryGcdList(dxf); //获取高程点
        TDiff.log("wasm解析");
        await this.dxfScene.loadDxf(dxf, fontFetchers);
        dxf.delete();
        // this.drawingSelected()//算量已识别图元生成图层
        TDiff.log("图元装配");
        await this.rebuildScene(true);
    }
    public async rebuildScene(isInit: boolean = false, callback: any = null) {
        this.Clear();
        await this.dxfScene.rebuildSceneDa();
        let sceneDa = this.dxfScene.sceneDa;
        this.origin = this.dxfScene.origin;
        this.bounds = this.dxfScene.bounds;
        /* Load all blocks on the first pass. */
        this.dxfScene.batches.each((batch) => {
            if (
                batch.key.blockName !== null &&
                batch.key.geometryType !== BatchEntityType.BLOCK_INSTANCE &&
                batch.key.geometryType !== BatchEntityType.POINT_INSTANCE
            ) {
                let block = this.blocks.get(batch.key.blockName);
                if (!block) {
                    block = new Block();
                    this.blocks.set(batch.key.blockName, block);
                }
                block.PushBatch(new Batch(this, batch));
            }
        });
        console.log(`DXF scene:
                     ${sceneDa.batchSize} batches,
                     ${this.layers.size} layers,
                     ${this.blocks.size} blocks,
                     vertices ${sceneDa.verticesSize} B,
                     indices ${sceneDa.indicesSize} B
                     transforms ${sceneDa.transformsSize} B
                     entityIds ${sceneDa.entityIdSize} B`);

        /* Instantiate all entities. */
        this.dxfScene.loadBatch();

        this._Emit("loaded");
        // this.fullView(isInit);
        // this.fullView(isInit);

        callback?.();
        this.Render();
    }
    public fitFullView() {
        this.fullView(true);
    }
    public fullView(isInit: boolean = false) {
        console.log(isInit, 'isInit');

        if (isInit) {
            if (this.bounds) {
                let { minX, minY, maxX, maxY } = this.bounds;
                this.FitView(minX, maxX, minY, maxY);
                this.controls.target = new Vector3(
                    this.camera.position.x,
                    this.camera.position.y,
                    0
                );
                this.controls.update();
            }
        } else {
            this.controls.target = new Vector3(
                this.camera.position.x,
                this.camera.position.y,
                0
            );
            this.controls.update();
        }
    }

    //获取图纸高程点
    public queryGcdList(dxf) {
        let wasmArr = dxf.getGCPoints();
        let count = wasmArr.size();
        console.log(count, "count");
        let arr: any = [];
        for (let j = 0; j < count; j++) {
            let point = wasmArr.get(j);
            arr.push([point.x(), point.y(), point.z()]);
        }
        // console.log(arr,'gcd......................');
        pickupInfo.gcdPointList = arr;
    }
    // 选中模型，生成图片
    async OnSelectModelToImage() {
        if (pickupInfo.pickupFalg) {
            let scene = this.selectionScene.dynmScene;
            const newScene = new THREE.Scene();
            if (scene.children.length > 0) {
                scene.children.forEach((item) => {
                    if (item.type !== "Points") {
                        console.log(item, "item");
                        const clonedObj = item.clone();
                        newScene.add(clonedObj);
                    }
                });
                const imgUrl: any = await scene2Base64(newScene);
                pickupInfo.pickupOBjImgurl = imgUrl.imgUrl;
            } else {
                pickupInfo.pickupOBjImgurl = "";
            }
            PubSub.default.pub("modelToImage");
        }
        if (pickupInfo.pickupAttributeFalg) {
            PubSub.default.pub("modelToAttribute");
        }
    }

    drawingDisplay(color) {
        this.selectionScene.clear();
        let arr: any[] = [];
        pickupInfo.identifiedIds.forEach((item) => {
            if (item && item.idLIst && item.idLIst.length) {
                item.idLIst.forEach((ite) => arr.push(ite));
            }
        });
        this.selectionScene.addObjsByIds(this.transaction.gpObj, arr, { color });
        this.Render();
    }
    drawingSelected() {
        // if(!pickupInfo.selectedIDlayer.length) return
        // slbatchAddLayer(this)  //已选择图元生成图层
    }
    async drawingSelected2() {
        await this.slIdentifiedscene.createdGp();
        this.Render();
    }

    Render() {
        if (!this.visual) {
            this.renderer.clear();
            return
        }
        // TDiff.start()
        this._EnsureRenderer();
        this.renderer.autoClear = false;
        // render id buffer
        this.renderer.setRenderTarget(this.idRenderTarget);
        this.renderer.clear(true, true, true);
        this.renderer.render(this.idScene, this.camera);
        this.renderer.setRenderTarget(null);

        // render main scene
        this.renderer.setClearColor((this._backgroundcolor[0] || 0,this._backgroundcolor[1] || 0,this._backgroundcolor[2] || 0), 0);
        this.renderer.setClearAlpha(this._backgroundalpah);
        this.renderer.clear(true, true, true);
        this.renderer.render(this.scene, this.camera);

        // console.log(this.slIdentifiedscene.getScene(),'this.slIdentifiedscene.getScene()');

        // render selections
        this.renderer.clear(false, true, true);

        this.renderer.render(this.selectedScene.getScene(), this.camera);
        // this.renderer.render(this.slIdentifiedLSscene.getScene(), this.camera)

        this.renderer.render(this.slIdentifiedscene.getScene(), this.camera);
        // console.log(this.slIdentifiedscene.getScene(),'this.slIdentifiedscene.getScene()');

        this.renderer.render(this.selectionScene.getScene(), this.camera);
        // render highlights
        this.renderer.render(this.highlightScene.getScene(), this.camera);

        this.renderer.render(this.snap.markerScene, this.camera);
        // render transient scene
        //this.renderer.render(this.transientScene.getScene(), this.camera)
        this.renderer.render(this.transientSceneWasm.getScene(), this.camera);
        // TDiff.log()
    }

    /** @return {Iterable<{name:String, color:number}>} List of layer names. */
    GetLayers() {
        const result: any[] = [];
        for (const lyr of this.layers.values()) {
            result.push({
                name: lyr.name,
                displayName: lyr.displayName,
                color: lyr.color,
                // color: this._TransformColor(lyr.color)
            });
        }
        return result;
    }
    _showlayer(obj) {
        //{name,isVisible}
        if (obj.type == "all") {
            this.ShowALLLayer(obj.isVisible);
        } else {
            this.ShowLayer(obj.name, obj.isVisible);
        }
    }
    ShowLayer(name, show) {
        this._EnsureRenderer();
        const layer = this.layers.get(name);
        if (!layer) {
            return;
        }
        for (const obj of layer.objects) {
            obj.visible = show;
        }
        this.Render();
    }

    ShowALLLayer(show) {
        this._EnsureRenderer();
        let arr: any = [];
        for (const value of this.layers.values()) {
            arr.push(value);
        }
        if (!arr.length) {
            return;
        }
        arr.forEach((item) => {
            for (const obj of item.objects) {
                obj.visible = show;
            }
        });
        this.Render();
    }

    /** Reset the viewer state. */
    Clear() {
        this.dxfScene.clear();
        this.idScene.clear();
        //this.markerScene.clear()
        for (const layer of this.layers.values()) {
            layer.Dispose();
        }
        this.selectionScene.clear();
        this.highlightScene.clear();
        this.selectedScene.clear();
        this.slIdentifiedscene.clear();
        this.layers.clear();
        this.blocks.clear();
        this.dxfmats.clear();
        this._Emit("cleared");
        this.Render();
    }

    /** Free all resources. The viewer object should not be used after this method was called. */
    Destroy() {
        // console.log('DestroyDestroyDestroyDestroy');

        if (!this.HasRenderer()) {
            return;
        }
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        this.Clear();
        this._Emit("destroyed");
        this.dxfmats.dispose();
        // this.renderer.forceContextLoss()
        this.renderer.dispose();
        // this.renderer = null
    }
    SetViewToObj(center: THREE.Vector2, radius: number) {
        console.log(center, radius,'SetViewToObjSetViewToObj');
        
        if (radius <= Number.MIN_VALUE * 2) {
            radius = 1;
        }
        radius = radius * 10;
        this.SetView(center, radius)
    }
    olsetCadView(position, scale) {
        let width = Math.max(this.canvasWidth, this.canvasHeight)
        this.SetView(position, width, scale);
        this.controls.target = new Vector3(
            this.camera.position.x,
            this.camera.position.y,
            0
        );
        this.controls.update();
        this.Render();
    }
    getCenter() {
        let event = {
            offsetX: this.canvasWidth / 2,
            offsetY: this.canvasHeight / 2,
            target: this.domContainer
        }
        let centerPos = screen2wcs(event, this.camera)
        // console.log(globalDxf.wcsOff)
        return {
            x: centerPos.x + globalDxf.wcsOff.x,
            y: centerPos.y + globalDxf.wcsOff.y
        }
    }
    zoomTo(center, scale) {
        this.SetView(center, this.canvasWidth * scale)
    }
    get zoom() {
        return this.camera.zoom
    }
    SetView(center, width) {
        const aspect = this.canvasWidth / this.canvasHeight;
        const height = width / aspect;
        const cam = this.camera;
        // cam.left = -width / 2;
        // cam.right = width / 2;
        // cam.top = height / 2;
        // cam.bottom = -height / 2;
        // cam.zoom = 1;
        cam.left = -this.canvasWidth / 2
        cam.right = this.canvasWidth / 2
        cam.top = this.canvasHeight / 2
        cam.bottom = -this.canvasHeight / 2
        cam.zoom = width / this.canvasWidth;
        let { x, y } = globalDxf.wcsOff;
        cam.position.set(center.x - x, center.y - y, 1);
        this.controls.target.set(center.x - x, center.y - y, 0)
        cam.rotation.set(0, 0, 0);
        cam.updateMatrix();
        cam.updateProjectionMatrix();
        this._Emit("viewChanged");
    }

    /** Set view to fit the specified bounds. */
    FitView(minX, maxX, minY, maxY, padding = 0.1) {
        const aspect = this.canvasWidth / this.canvasHeight;
        let width = maxX - minX;
        const height = maxY - minY;
        const center = { x: minX + width / 2, y: minY + height / 2 };
        if (height * aspect > width) {
            width = height * aspect;
        }
        if (width <= Number.MIN_VALUE * 2) {
            width = 1;
        }
        this.SetView(center, width * (1 + padding));
    }

    /** Subscribe to the specified event. The following events are defined:
     *  * "loaded" - new scene loaded.
     *  * "cleared" - current scene cleared.
     *  * "destroyed" - viewer instance destroyed.
     *  * "resized" - viewport size changed. Details: {width, height}
     *  * "pointerdown" - Details: {domEvent, position:{x,y}}, position is in scene coordinates.
     *  * "pointerup"
     *  * "viewChanged"
     *  * "message" - Some message from the viewer. {message: string, level: string}.
     *
     * @param eventName {string}
     * @param eventHandler {function} Accepts event object.
     */
    Subscribe(eventName, eventHandler) {
        this._EnsureRenderer();
        this.canvas.addEventListener(EVENT_NAME_PREFIX + eventName, eventHandler);
    }

    /** Unsubscribe from previously subscribed event. The arguments should match previous
     * Subscribe() call.
     *
     * @param eventName {string}
     * @param eventHandler {function}
     */
    Unsubscribe(eventName, eventHandler) {
        this._EnsureRenderer();
        this.canvas.removeEventListener(
            EVENT_NAME_PREFIX + eventName,
            eventHandler
        );
    }

    //linkEntityWithGrips(entityId) {
    //    this.transientScene.setTransientEntityById(this.transaction.gpObj, entityId);
    //    this.selectedId = entityId;
    //}

    //public updateTransientScene(point){
    //    this.transientScene.updateTransientScene(point);
    //   this.transientScene.showPoint();
    //}

    //public cleanTransientScene(){
    //   this.transientScene.cleanTransientScene();
    //   this.rebuildScene();

    // keep the selections for continued editing
    //   this.selectionScene.AddObjById(this.transaction.gpObj, this.selectedId,{isEdit:true})
    //}
    //public endMoveGrips({x,y}){
    //	if(x==0&&y==0)return
    //   this.transientScene.endMoveGrips({x,y});
    //   this.rebuildScene();

    // keep the selections for continued editing
    //    this.selectionScene.AddObjById(this.transaction.gpObj, this.selectedId,{isEdit:true})
    //}
    // /////////////////////////////////////////////////////////////////////////////////////////////

    _EnsureRenderer() {
        if (!this.HasRenderer()) {
            throw new Error(
                "WebGL renderer not available. " +
                "Probable WebGL context loss, try refreshing the page."
            );
        }
    }

    _CreateControls() {
        const controls: any = (this.controls = new OrbitControls(
            this.camera,
            this.canvas
        ));
        controls.enableRotate = true;
        controls.mouseButtons = {
            LEFT: MOUSE.PAN,
            MIDDLE: MOUSE.DOLLY,
            RIGHT: MOUSE.ROTATE,
        };
        controls.touches = {
            ONE: TOUCH.PAN,
            TWO: TOUCH.DOLLY_PAN,
        };
        controls.zoomSpeed = 3;
        controls.mouseZoomSpeedFactor = 0.05;
        controls.target = new Vector3(
            this.camera.position.x,
            this.camera.position.y,
            0
        );
        let t = this
        controls.addEventListener("change", () => {
            this._Emit("viewChanged");
            this.reload(); //视图改变，需要重新加载
        });
        controls.addEventListener("wheelEvent", (e) => {
            t.dispatchEvent(Object.assign({}, e, { type: 'wheel' }))
        })
        controls.update();
        console.log(controls, "controls");
    }
    public getScreenProjSize() {
        let wid = (this.camera.right - this.camera.left) / this.camera.zoom;
        let hei = (this.camera.top - this.camera.bottom) / this.camera.zoom;
        Object.assign(globalDxf.view, {
            wid,
            hei,
        });
    }
    public reload() {
        this.getScreenProjSize();
        if (globalDxf.isLodOn) {
            this.lod.require(); //关闭lod
        }
        this.Render();
    }
    _Emit(eventName, data: any = null) {
        this.canvas.dispatchEvent(
            new CustomEvent(EVENT_NAME_PREFIX + eventName, { detail: data })
        );
    }

    _Message(message, level = MessageLevel.INFO) {
        this._Emit("message", { message, level });
    }

    _OnResize(entry) {
        this.SetSize(
            Math.floor(entry.contentRect.width),
            Math.floor(entry.contentRect.height)
        );
    }

    /** Ensure the color is contrast enough with current background color.
     * @param color {number} RGB value.
     * @return {number} RGB value to use for rendering.
     */
    _TransformColor(color) {
        if (!this.options.colorCorrection && !this.options.blackWhiteInversion) {
            return color;
        }
        /* Just black and white inversion. */
        const bkgLum = Luminance(this.clearColor);
        if (color === 0xffffff && bkgLum >= 0.8) {
            return 0;
        }
        if (color === 0 && bkgLum <= 0.2) {
            return 0xffffff;
        }
        if (!this.options.colorCorrection) {
            return color;
        }
        const fgLum = Luminance(color);
        const MIN_TARGET_RATIO = 1.5;
        const contrast = ContrastRatio(color, this.clearColor);
        const diff = contrast >= 1 ? contrast : 1 / contrast;
        if (diff < MIN_TARGET_RATIO) {
            let targetLum;
            if (bkgLum > 0.5) {
                targetLum = bkgLum / 2;
            } else {
                targetLum = bkgLum * 2;
            }
            if (targetLum > fgLum) {
                color = Lighten(color, targetLum / fgLum);
            } else {
                color = Darken(color, fgLum / targetLum);
            }
        }
        return color;
    }

}
