import { Box2, Matrix3 } from "three"
import { ClipCalculator } from "./ClipCalculator"

export class HatchCalculator {
    public boundaryLoops
    public style

    /**
     * Arrays of `Path` to use as boundary, and each `Path` is array of `Point`.
     *
     * @param {Vector2[][]} boundaryLoops
     * @param {HatchStyle} style
     */
    constructor(boundaryLoops, style) {
        this.boundaryLoops = boundaryLoops
        this.style = style
    }

    /**
     * Clip `line` using strategy defined by `this.style`
     *
     * @param {[Vector2, Vector2]} line Line segment defined by start and end points. Assuming start
     *  and end points lie out of the boundary loops specified in the constructor.
     * @returns {[Vector2, Vector2][]} clipped line segments
     */
    ClipLine(line) {
        return new ClipCalculator(this.boundaryLoops, this.style, line).Calculate()
    }

    /**
     * @param {Vector2} seedPoint Pattern seed point coordinates in OCS.
     * @param {?number} angle Pattern rotation angle in radians.
     * @param {?number} scale Pattern scale.
     * @return {Matrix3} Transformation from OCS to pattern space.
     */
    GetPatternTransform({seedPoint, angle, scale}) {
        const m = new Matrix3().makeTranslation(-seedPoint.x, -seedPoint.y)
        if (angle) {
            /* Matrix3.rotate() inverts angle sign. */
            m.rotate(angle)
        }
        if ((scale ?? 1) != 1) {
            m.scale(1 / scale, 1 / scale)
        }
        return m
    }

    /**
     * @param {Matrix3} patTransform Transformation from OCS to pattern space previously obtained by
     *      GetPatternTransform() method.
     * @param {?Vector2} basePoint Line base point coordinate in pattern space.
     * @param {?number} angle Line direction angle in radians, CCW from +X direction.
     * @return {Matrix3} Transformation from OCS to pattern line space. Line is started at origin
     *  and directed into position X axis direction.
     */
    GetLineTransform({patTransform, basePoint, angle}) {
        const m = patTransform.clone()
        if (basePoint) {
            m.translate(-basePoint.x, -basePoint.y)
        }
        if (angle) {
            /* Matrix3.rotate() inverts angle sign. */
            m.rotate(angle)
        }
        return m
    }

    /**
     * @param {Matrix3} transform Transformation from OCS to target coordinates space.
     * @return {Box2} Pattern AABB in target coordinate space.
     */
    GetBoundingBox(transform) {
        const box = new Box2()
        for (const path of this.boundaryLoops) {
            for (const v of path) {
                box.expandByPoint(v.clone().applyMatrix3(transform))
            }
        }
        return box
    }
}
