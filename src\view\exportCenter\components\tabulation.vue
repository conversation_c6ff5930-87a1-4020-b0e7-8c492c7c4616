<template>
    <div class="tabulation">
        <el-table :data="tableData" border style="width: 100%;" :max-height="maxHeight">
            <el-table-column prop="name" label="名称" align="center" height="100" />
            <el-table-column prop="status" label="状态" align="center"  height="100" />
            <el-table-column prop="" label="下载地址" align="center" height="100">
                <template #default="scope">
                    <el-button v-if="scope.row.status==='SUCCESS'" type="success" plain @click="skipSy(scope.row)">跳转</el-button>
                    <el-button v-if="scope.row.status==='SUCCESS'" type="success" plain @click="btnDownload(scope.row)">下载</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref,onMounted,nextTick,reactive } from 'vue'

  import {canvasStates} from '@/store/canvasStates'
  let {lsObj}=canvasStates()

  import { useRouter } from "vue-router";
  const router = useRouter();
  

  const emits = defineEmits(['handleClose3'])

  const props = defineProps({
    tableData:{
        type:Array,
        default:[]
    },
    maxHeight:{
        type:String,
        default:'500'
    }
  })
  onMounted(()=>{
    console.log(props.tableData,'子组件');
  })

  const btnDownload=(item:any)=>{
    fetch(item.resultDownloadUrl).then((res) =>
        res.blob().then((blob) => {
            const a = document.createElement('a')
            a.href = URL.createObjectURL(blob)
            //测试链接console.log(a.href)
            a.download =item.name+'.dxf'  // 下载文件的名字
            document.body.appendChild(a)
            a.click()
            setTimeout(() => {
                document.body.removeChild(a);
            }, 0);
        })
    )
  }

  const skipSy=(item:any)=>{
    lsObj.dxfurl=item.resultDownloadUrl
    router.push({
        path:'/',
    })
  }

  const cleanFn=()=>{
  }
  
  const handleClose=()=>{
      emits('handleClose3')
  }  
  
  defineExpose({
      cleanFn
  })
  </script>
  
  <style lang="scss" scoped>
  .tabulation {
    width: 100%;
    height: 100%;
    background-color: #3a4352;
    padding: 0 50px;
    :deep(.el-table tr) {
        height: 50px !important;
    }
  }
  </style>
  
  