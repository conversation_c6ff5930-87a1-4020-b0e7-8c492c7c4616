

import { GripPoint } from "./GripCommon";
import { CadMessageReply } from "../CadMessageReply";
import { KeyboardKey } from "dxf-viewer/src/controls";
import { DrawState } from "dxf-viewer/src/global/constant";
import { globalDxf } from "dxf-viewer/src";
import * as THREE from "three"


//夹点拖拽消息响应类
export class GripDragReply  extends CadMessageReply{
    public gPoint :GripPoint;//拖拽的夹点信息
    private GeoPoint: any;//将选中的夹点高亮

    constructor(viewer,gpt) {
        super(viewer)
        this.gPoint = gpt

        let pointGeom = new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(this.gPoint.gPoint.x(), this.gPoint.gPoint.y(), 0)]);
        let pointMat = new THREE.PointsMaterial({
            size: 16,
            color: 0x0000ff,//蓝色高亮显示正在拖拽的夹点
        });
        this.GeoPoint = new THREE.Points(pointGeom, pointMat);
    }
    //进入夹点拖拽命令
    public onEnter() : void{
        globalDxf.drawstate=DrawState.Drawing
        this.viewer.scene.add(this.GeoPoint)
    }
    //完成拖拽，退出命令
    public override onCancel() : void{
        super.onCancel()
        this.viewer.selectionScene.setIsDraging(false)
        this.viewer.scene.remove(this.GeoPoint)
    }
    //消息返回false表示可以往下路由，返回true表示在这个节点截止不再往下路由
    // 左键按下，拖拽完成，将共同操作放基类
    public override onLButtonDown( event ) : boolean{
        this.onCancel()
        this.viewer.selectionScene.setFinishDrag(true)
        this.viewer.rebuildScene()
        return true;
    }
    //鼠标移动
    public override onMouseMove( event ) : boolean{
        super.onMouseMove(event)
        return false;
    }
    //键盘按下
    public override onKeyDown(event) : boolean{
        if(event.key == KeyboardKey.Escape) {//取消
            this.onCancel()
        }
        return true;
    }    
}
    