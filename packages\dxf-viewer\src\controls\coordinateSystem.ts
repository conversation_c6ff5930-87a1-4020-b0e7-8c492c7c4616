import { Plane, Raycaster, Vector2, Vector3 } from "three";

interface IEventPos{
    target:any,
    offsetX:number,
    offsetY:number
}
/**
 * 屏幕坐标转物理坐标
 */
export const screen2wcs=(event:IEventPos,camera):Vector3=>{
    const mouse = new Vector2();
    const bounds =event.target.getBoundingClientRect();
    const x = event.offsetX;
    const y = event.offsetY;
    mouse.x = (x / bounds.width) * 2 - 1;
    mouse.y = -(y / bounds.height) * 2 + 1; 

    let worldPoint=new Vector3()
    const raycaster = new Raycaster();
    raycaster.setFromCamera(mouse, camera);

    const plane = new Plane(new Vector3(0,0,1)) //infinite plane
    raycaster.ray.intersectPlane ( plane, worldPoint )

    // let pos=new Vector3(mouse.x,mouse.y,1).unproject(camera)
    // pos.z=0
    // console.log(worldPoint,pos)
    return worldPoint
}
/**
 * 世界坐标转为屏幕坐标
 */
export const wcs2screen=(wcs:Vector3,camera,dom)=>{
    let screen=wcs.clone().project(camera)
    var halfWidth = dom.clientWidth / 2;
    var halfHeight = dom.clientHeight / 2;

    var result = {
        x: Math.round(screen.x * halfWidth + halfWidth),
        y: Math.round(-screen.y * halfHeight + halfHeight)
    };
    return result
}

/**
 * 根据屏幕坐标 (x, y) 获取 Three.js 世界坐标系下的坐标
 * @param {number} x - 屏幕X坐标（像素）
 * @param {number} y - 屏幕Y坐标（像素）
 * @param {THREE.Camera} camera - 当前使用的相机
 * @param {HTMLElement} domElement - 渲染器的 dom 元素（通常是 renderer.domElement）
 * @param {number} [z=0] - 指定深度（世界空间Z值）
 * @returns {THREE.Vector3} - 对应的世界坐标
 */
export const getWorldPositionOnZPlane = ({ x, y }, camera, domElement, targetZ = 0) => {
    const rect = domElement.getBoundingClientRect();
    // 归一化设备坐标 (NDC)，范围 [-1, 1]
    const ndcX = ((x - rect.left) / rect.width) * 2 - 1;
    const ndcY = -((y - rect.top) / rect.height) * 2 + 1;
    // 创建射线
    const raycaster = new Raycaster();
    raycaster.setFromCamera(new Vector2(ndcX, ndcY), camera);
    // 定义目标平面（Z=targetZ）
    const plane = new Plane(new Vector3(0, 0, 1), -targetZ); // ax + by + cz + d = 0
    // 求交点
    const intersection = new Vector3();
    const isIntersected = raycaster.ray.intersectPlane(plane, intersection);
    if (!isIntersected) {
        console.warn('射线未与平面相交');
        return null;
    }
    return intersection;
};
