import * as THREE from "three";

import { CadMessageReply } from "./CadMessageReply";
import { KeyboardKey } from "dxf-viewer/src/controls";
import { DrawState, globalDxf, pickupInfo, poptip } from "dxf-viewer";
import { singal } from "emitter";
import { DrawHelp } from "./drawHelp";
export class Offset extends DrawHelp {
  public MIds: any;
  public rect: any;
  public isPickObj: any; //是否需要pickobj
  public isSelectFirstPt: any; //是否已经点下第一点
  public firstPoint: any; //记录第一点坐标

  constructor(viewer, ids) {
    super(viewer);
    this.MIds = ids;
    this.isPickObj = false;
    this.isSelectFirstPt = false;
    this.init();
  }
  public async init() {
    await this.pick0({ tip: "请选择对象" });
    await this.pick1({ tip: "绘制第一个点" });
    await this.pick2({ tip: "绘制第二个点" });
    await this.pick3({ tip: "是否保留" });
    poptip.dispose()
  }
  public async pick0(opt) {
    poptip.tip(opt);
    if (this.MIds.length == 0) {
      this.isPickObj = true;
      await this.select();
    } else {
      await 1;
    }
  }
  public async pick1(opt) {
    //选择第一个点
    poptip.tip(opt);
    return new Promise((resolve) => {
      var func = (event) => {
        this.isSelectFirstPt = true;
        this.firstPoint = this.getEventPoint(event);
        singal.pickPoint.remove(func);
        resolve([]);
      };
      singal.pickPoint.add(func);
    });
  }
  public async pick2(opt) {
    poptip.tip(opt);
    return new Promise((resolve) => {
      var func = (event) => {
        this.addObjects(this.getEventPoint(event));
        singal.pickPoint.remove(func);
        resolve([]);
      };
      singal.pickPoint.add(func);
    });
  }
  public async pick3(opt){
    poptip.tip(opt)
    return new Promise((resolve)=>{
        this.onCancel()
        resolve([])  
       
    })
}

  //消息返回false表示可以往下路由，返回true表示在这个节点截止不再往下路由
  // 左键按下
  public override onLButtonDown(event: PointerEvent): boolean {
    singal.pickPoint.dispatch(event);
    return true;
  }
  public override onRButtonDown(event: PointerEvent): boolean {
    
    this.endSelect();
    return true;
  }
  //鼠标移动
  public override onMouseMove(event: PointerEvent): boolean {
    super.onMouseMove(event);
    if (this.isSelectFirstPt) {
      let secpt = this.getEventPoint(event);
      this.drawTempGraphic(secpt);
    }
    return true;
  }
  //键盘按下
  public override onKeyDown(event: KeyboardEvent): boolean {
    if (event.key == KeyboardKey.Escape) {
      //取消
      this.onCancel();
    } else if (event.key == KeyboardKey.Space) {
      this.endSelect();
    }
    return true;
  }
  //绘制临时图元
  private drawTempGraphic(secpoint): void {
    this.clearTempGeometry();
    let geometry1 = new THREE.BufferGeometry().setFromPoints([
      this.firstPoint,
      secpoint,
    ]);
    let material1 = new THREE.LineBasicMaterial({ color: "red" });
    let line1 = new THREE.Line(geometry1, material1);
    this.tempGeometry.push(line1);
    const deltaX = this.firstPoint.x - secpoint.x;
    const deltaY = this.firstPoint.y - secpoint.y;
    let dist = Math.sqrt(Math.pow(deltaX, 2) + Math.pow(deltaY, 2));
    this.MIds.forEach((id) => {
      let obj = this.cadCtx.getPBaseObjById(id);
      let newOne = obj.copyNewOne();
      let entityType = newOne.getObjType();
      if (entityType === "SGObjCurve") {
        let offsetCurve = sg.SGObjTool.convertToCurve(newOne);

        console.log(newOne, offsetCurve);
        let curve = offsetCurve.getCurve();
        let vec1 = new sg.Vector2d(
          secpoint.x - this.firstPoint.x,
          secpoint.y - this.firstPoint.y
        );
        let vec2 = curve.tangent(curve.getFirstParameter()).rightVector2d();
        if (vec1.dot(vec2) < 0) {
          dist = 0 - dist;
        }
        let newOffset = offsetCurve.getCurve().offsetCurve(dist);
        if (!newOffset) return;
        offsetCurve.setpCurve(newOffset);
        let points = offsetCurve.getCurve().getInterpolatePoints(50);

        let threeJsPoints: THREE.Vector3[] = [];
        for (let i = 0; i < points.size(); i++) {
          threeJsPoints.push(
            new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
          );
        }
        let geometry2 = new THREE.BufferGeometry().setFromPoints(threeJsPoints);
        let material2 = new THREE.LineBasicMaterial({ color: "green" });
        let line2 = new THREE.Line(geometry2, material2);
        this.tempGeometry.push(line2);
      } else if (entityType === "SGObjComBinCurve") {
        let polylineNew = sg.SGObjTool.convertToComBinCurve(newOne);
        let curve = polylineNew.getCurve().getBoundCurve(0);
        let vec1 = new sg.Vector2d(
          secpoint.x - this.firstPoint.x,
          secpoint.y - this.firstPoint.y
        );
        let vec2 = curve.tangent(curve.getFirstParameter()).rightVector2d();
        if (vec1.dot(vec2) > 0) {
          dist = 0 - dist;
        }
        let newOffset=polylineNew.getCurve().polyLineOffset(dist);
        polylineNew.setpCurve(newOffset);
        let points = polylineNew.getCurve().getInterpolatePoints(50);

        let threeJsPoints: THREE.Vector3[] = [];
        for (let i = 0; i < points.size(); i++) {
          threeJsPoints.push(
            new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
          );
        }
        let geometry2 = new THREE.BufferGeometry().setFromPoints(threeJsPoints);
        let material2 = new THREE.LineBasicMaterial({ color: "green" });
        let line2 = new THREE.Line(geometry2, material2);
        this.tempGeometry.push(line2);
      }
    });
    this.showTempGeometry();
  }
  //最后一步添加图元
  private addObjects(secpoint): void {
    let ma = new sg.Matrix3();
    let createObjs: any[] = [];
    ma.mirror(
      new sg.LineSegment2d(
        this.threePointToSgPoint(this.firstPoint),
        this.threePointToSgPoint(secpoint)
      )
    );
    const deltaX = this.firstPoint.x - secpoint.x;
    const deltaY = this.firstPoint.y - secpoint.y;
    let dist = Math.sqrt(Math.pow(deltaX, 2) + Math.pow(deltaY, 2));
    this.MIds.forEach((id) => {
      let obj = this.cadCtx.getPBaseObjById(id);
      let newOne = obj.copyNewOne();
      let entityType = newOne.getObjType();
      if (entityType === "SGObjCurve") {
        let offsetCurve = sg.SGObjTool.convertToCurve(newOne);
        let curve = offsetCurve.getCurve();
        let vec1 = new sg.Vector2d(
          secpoint.x - this.firstPoint.x,
          secpoint.y - this.firstPoint.y
        );
        let vec2 = curve.tangent(curve.getFirstParameter()).rightVector2d();
        if (vec1.dot(vec2) < 0) {
          dist = 0 - dist;
        }
        let newOffset = offsetCurve.getCurve().offsetCurve(dist);
        if (!newOffset) return;
        offsetCurve.setpCurve(newOffset);
        createObjs.push(offsetCurve);
      } else if (entityType === "SGObjComBinCurve") {
        let polylineNew = sg.SGObjTool.convertToComBinCurve(newOne);
        let curve = polylineNew.getCurve().getBoundCurve(0);
        let vec1 = new sg.Vector2d(
          secpoint.x - this.firstPoint.x,
          secpoint.y - this.firstPoint.y
        );
        let vec2 = curve.tangent(curve.getFirstParameter()).rightVector2d();
        if (vec1.dot(vec2) > 0) {
          dist = 0 - dist;
        }
        {
          let points = polylineNew.getCurve().getVertex();
          let n = points.size();
          for (let i = 0; i < n; i++) {
            let value = points.get(i);
          console.log(value.x(),value.y());
          console.log(polylineNew.getCurve().isClosed());
          }
        }
        console.log(dist);
        let newOffset=polylineNew.getCurve().polyLineOffset(dist);
        {
          let points = newOffset.getVertex();
          let n = points.size();
          for (let i = 0; i < n; i++) {
            let value = points.get(i);
          console.log(value.x(),value.y());
          }
          console.log(newOffset.isClosed());
        }
        
        polylineNew.setpCurve(newOffset);
        createObjs.push(polylineNew);
      }
    });
    this.viewer.transaction.add(createObjs);
  }

  private endSelect() {
    //按空格键，确定
    if (this.isPickObj) {
      //pick完成
      this.MIds = pickupInfo.pickupIdlist;
      if (this.MIds.length == 0) {
        console.log("未选取到对象,请重新选择");
      } else {
        singal.selectObj.dispatch();

        this.isPickObj = false;
      }
    } else if(this.isSelectFirstPt){
      //singal.pickPoint.dispatch();
    }
    else {
      this.onCancel();
    }
  }
}
