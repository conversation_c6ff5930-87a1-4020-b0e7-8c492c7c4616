<script setup lang="ts">
import { ref } from "vue";
import { useProjectStore } from "../store";
import { storeToRefs } from "pinia";
const useProject = useProjectStore();

const { projectInfo } = storeToRefs(useProject);
const emit = defineEmits(["uploadCAD", "handleUploadTIF"]);

const btnSvgList = ref([
  {
    isShowName: false,
    name: "设置",
    svg: "setting",
  },
  {
    isShowName: false,
    name: "尺子",
    svg: "chizi",
  },
  {
    isShowName: true,
    name: "CAD图纸",
    svg: "tuzhi",
    tooltip: "上传CAD图纸",
  },
  {
    isShowName: true,
    name: "航拍图像",
    svg: "tupian",
    tooltip: "上传TIFF航拍图像",
  },
  {
    isShowName: true,
    name: "图例设置",
    svg: "tuLi",
  },
  {
    isShowName: true,
    name: "识图结果",
    svg: "shibiejieguo",
  },
  {
    isShowName: true,
    name: "清单计算",
    svg: "jisuan",
  },
  {
    isShowName: true,
    name: "清单导出",
    svg: "qingdan",
  },
]);

// 点击事件
const handleClick = (item) => {
  switch (item.svg) {
    case "tuzhi":
      // 触发CAD图纸上传
      emit("uploadCAD");
      break;
    case "tupian":
      // 触发TIF图像上传
      emit("handleUploadTIF");
      break;
    case "tuLi":
      break;
    case "shibiejieguo":
      break;
    case "jisuan":
      break;
    case "qingdan":
      break;
    case "chizi":
      break;
    case "setting":
      break;
    default:
      break;
  }
};
</script>

<template>
  <div class="tab-panel-wrap">
    <div class="tab-panel-wrap-top">
      <div
        class="tab-panel-wrap-top-item"
        v-for="item in btnSvgList"
        :key="item.name"
        @click="handleClick(item)"
      >
        <el-tooltip
          v-if="item.tooltip"
          :content="item.tooltip"
          placement="bottom"
        >
          <div class="tab-panel-wrap-top-item-content">
            <svg-icon
              :iconClass="item.svg"
              w="25"
              h="25"
              class="icon zpointer"
              style="padding-top: 0px"
            ></svg-icon>
            <span v-if="item.isShowName" class="tab-panel-wrap-top-item-text">{{
              item.name
            }}</span>
          </div>
        </el-tooltip>
        <template v-else>
          <svg-icon
            :iconClass="item.svg"
            w="25"
            h="25"
            class="icon zpointer"
            style="padding-top: 0px"
          ></svg-icon>
          <span v-if="item.isShowName" class="tab-panel-wrap-top-item-text">{{
            item.name
          }}</span>
        </template>
      </div>
    </div>
    <div class="tab-panel-wrap-bottom">
      <div class="tab-panel-wrap-bottom-item">
        <span>项目名称：</span>
        <span>{{ projectInfo?.projectName }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.tab-panel-wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  height: 100px;
  background-color: #f3f3f3;
  .tab-panel-wrap-top {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .tab-panel-wrap-top-item {
      padding: 0 16px;
      height: 36px;
      line-height: 36px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &-content {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &-text {
        margin-left: 10px;
        font-size: 14px;
        color: #000;
      }
      &:hover {
        .icon {
          color: #007aff;
        }
        .tab-panel-wrap-top-item-text {
          color: #007aff;
        }
      }
    }
  }
  .tab-panel-wrap-bottom {
    flex: 1;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background-color: #000;
    outline: none;
    .tab-panel-wrap-bottom-item {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #fff;
      padding: 0 20px;
      .tab-panel-wrap-bottom-item-text {
        margin-left: 10px;
      }
    }
  }
}
:deep(.icon:hover) {
  background: transparent;
}
</style>
