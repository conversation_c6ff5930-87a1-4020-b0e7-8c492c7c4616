import * as THREE from 'three';

export const scene2Base64=(scene)=>{//,imgwidth,imgheight
        // 渲染器
        const renderTarget = new THREE.WebGLRenderer();
        renderTarget.setSize(100, 100);
        const aspect = renderTarget.domElement.width / renderTarget.domElement.height;
        // 将相机定位在选中的模型周围
        // const wrapBox = new THREE.Box3();
        const center = new THREE.Vector3();
        // 计算包围盒
        const wrapBox= boundingBox(scene)
        wrapBox.getCenter(center);

        // center.setZ(0)
        const size = wrapBox.getSize(new THREE.Vector3());
        const halfWidth = Math.max(size.x, size.y, 0) / 2;
        const halfHeight = halfWidth / aspect;
        console.log(halfWidth,halfHeight,'llllllllllll');
        
        //相机
        const camera = new THREE.OrthographicCamera(
            -halfWidth, halfWidth, halfHeight, -halfHeight, 0.1,1.5
        );
        camera.left *= 1.5;
        camera.right *= 1.5;
        camera.top *= 1.5;
        camera.bottom *= 1.5;

        camera.position.set(center.x, center.y, 1.5);
        camera.lookAt(center);
        camera.updateProjectionMatrix();

        renderTarget.render(scene, camera);
    
        const base64Image = renderTarget.domElement.toDataURL("image/png");
        renderTarget.forceContextLoss()
        renderTarget.dispose()
        return { imgUrl:base64Image }
}

function getMatrixFromInterleavedBufferAttribute(attribute) {
    const matrix = new THREE.Matrix3();
    const array = attribute.array;

    const offset = 0;
    matrix.set(
        array[offset + 0],  array[offset + 1], array[offset + 2],
        array[offset + 3], array[offset + 4], array[offset + 5],
        0, 0, 1
    );

    return matrix;
}

const boundingBox=(scene)=>{
    const wrapBox = new THREE.Box3();
    scene.children.forEach(item=>{
        const geomAttributes = item.geometry.attributes;
        const positionAttribute = geomAttributes.position;
        const count = positionAttribute.count;
        // 初始化包围盒
         const boundingBox = new THREE.Box3();
        // 找到所有顶点的最小和最大值
        if (item.geometry.isInstancedBufferGeometry) {
            const xform = getMatrixFromInterleavedBufferAttribute(geomAttributes.instanceTransform0);
            for (let i = 0; i < count; i++) {
                const x = positionAttribute.getX(i);
                const y = positionAttribute.getY(i);
                const z = 1;
                const posVec = new THREE.Vector3(x, y, z);
                posVec.applyMatrix3(xform);
                posVec.z = 0;
                boundingBox.expandByPoint(posVec);
            }
        } else {
            for (let i = 0; i < count; i++) {
                const x = positionAttribute.getX(i);
                const y = positionAttribute.getY(i);
                const z = 0;
                boundingBox.expandByPoint(new THREE.Vector3(x, y, z));
            }
        }        
        wrapBox.union(boundingBox)
    })
    return wrapBox
}

export const buff2base64=({width,height,buff})=>{
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    let da=new Uint8ClampedArray(width*height*4)
    for(let i=0;i<height*width*4;i++){
        da[i]=Math.floor(buff[i]*256)
        if(da[i]>0){
            da[i]+=200
        }
        if(i%4==3){
            da[i]=200
        }
    }
    let nda=new Uint8ClampedArray(width*height*4)
    for(let i=0;i<width;i++){
        for(let j=0;j<height;j++){
            for(let n=0;n<4;n++){
                nda[(j*width+i)*4+n]=da[((height-j)*width+i)*4+n]
            }
        }
    }
    let img=new ImageData(nda,width,height)
    ctx.putImageData(img,0,0,0,0,width,height)
    return canvas.toDataURL()
}



// return new Promise((resove, reacjet) => {
//     let ceshiObjCanvas = document.createElement("canvas");
//     ceshiObjCanvas.width = 800;
//     ceshiObjCanvas.height = 600;
//     let context:CanvasRenderingContext2D = ceshiObjCanvas.getContext("2d")!;
//     let img = new Image();
//     img.src = base64Image; // 替换为你的图片URL
//     img.onload = function () {
//         var scaleRatio = 2; // 缩放比例为原来的一半
//         var scaledWidth = img.width * scaleRatio;
//         var scaledHeight = img.height * scaleRatio;
//         var drawX = (ceshiObjCanvas.width - scaledWidth) / 2;
//         var drawY = (ceshiObjCanvas.height - scaledHeight) / 2;
//         context.drawImage(
//             img,
//             0,
//             0,
//             img.width,
//             img.height,
//             drawX,
//             drawY,
//             scaledWidth,
//             scaledHeight
//         );
//         const imgUrl = ceshiObjCanvas.toDataURL("image/png");
//         console.log(imgUrl, 'imgUrl');
//         resove({ imgUrl });
//     };
// });
