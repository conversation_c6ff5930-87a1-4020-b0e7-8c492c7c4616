import { defineStore } from 'pinia';


export const dxfviewerData = defineStore('dxfviewerData', ()=>{
    const viewerData=reactive({
        sketchViewers:null
    })
    const setSketchViewers=(data:any)=>{
        viewerData.sketchViewers=data
    }
    return {
        viewerData,
        setSketchViewers 
    }
});

// export const usedxfviewerDataStore = defineStore('dxfviewerData', {
//     state: () => ({
//         sketchViewers: null
//     }),
//     actions: {
//       SetUserInfo(data: any) {
//         this.sketchViewers = data
//       }
//     }
// })


