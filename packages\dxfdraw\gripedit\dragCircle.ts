import { GripPoint } from "./GripCommon";
import { GripDragReply } from "./GripDragReply";
import * as THREE from "three"
//生成直线夹点
export function createCircleGripPoints(obj){
    let resPoints:GripPoint[] = []
    let tpts = obj.selectPoints()
    let size = tpts.size()
    for(let i = 0 ; i < size ; i++){
        let temp = tpts.get(i)
        let strType = temp.getString()    
        let index = 0
        
        if(strType == 'center'){
            index =0
        }
        else if(strType == 'zeroPoint'){
            index =1
        }
        else if(strType == 'quaterPoint'){
            index =2
        }
        else if(strType == 'halfPoint'){
            index =3
        }
        else if(strType == 'threeQuatersPoint'){
            index =4
        }
        let ppt1 = temp.getPoint()
        let gpoint = new GripPoint(ppt1,obj.getDrawObjectId(),index)
        resPoints.push(gpoint)
    }
    return resPoints
}




//拖拽线段
export class DragCircleReply  extends GripDragReply{
    public oriLine:any
    public objCurve : any

    constructor(viewer,gpt) {
        super(viewer,gpt)
        let group = this.viewer.transaction.gpObj
        let obj = group.getPBaseObjById(this.gPoint.objId)
        this.objCurve = sg.SGObjTool.convertToCurve(obj)
        this.oriLine = this.objCurve.getCurve()
    }
    
    //消息返回false表示可以往下路由，返回true表示在这个节点截止不再往下路由
    // 左键按下
    public override onLButtonDown( event ) : boolean{
        let mousePt = this.getEventPoint(event)
        let pt = this.threePointToSgPoint(mousePt)
        let group = this.viewer.transaction.gpObj
        group.getTransactionManager().beginTransaction()
        if(this.gPoint.gripIndex == 0)
        {
            this.objCurve.setCircleCenter(pt)
        }
        else{
            this.objCurve.setCirclePoint(pt)
        }
        group.getTransactionManager().commit()
        super.onLButtonDown(event)
        return true;
    }
    //鼠标移动
    public override onMouseMove( event ) : boolean{
        this.clearTempGeometry()
        super.onMouseMove(event)
        let mousePt = this.getEventPoint(event)
        let material1 = new THREE.LineBasicMaterial({ color:'yellow'});
        let material2 = new THREE.LineBasicMaterial({ color:'grey'});
        let center=this.oriLine.getCenter();
        let radius=this.oriLine.getRadius();
        if(this.gPoint.gripIndex !=0)
        {
            const geometry = new THREE.BufferGeometry();
            geometry.setFromPoints([]);
            // 创建线条对象，使用LineLoop可以让首尾相连形成闭合的圆
            let mesh = new THREE.LineLoop(geometry, material1);
            let r=Math.sqrt((mousePt.x-center.x()) ** 2 + (mousePt.y-center.y()) ** 2 );
            let curve = new THREE.EllipseCurve(center.x(), center.y(),  r,r, 0, 2 * Math.PI, false,0);
            let vertices=curve.getPoints(64)     
            mesh.geometry.setFromPoints(vertices);
            this.tempGeometry.push(mesh)

            let pt3 = new THREE.Vector3(this.gPoint.gPoint.x(),this.gPoint.gPoint.y(),0)
            let geometry2 =new THREE.BufferGeometry().setFromPoints([mousePt,pt3]);          
            let line2 = new THREE.Line(geometry2, material2);
            this.tempGeometry.push(line2)
        }
        else 
        {//拖动线段中点，绘制临时图形  
            let pt3 = new THREE.Vector3(center.x(),center.y(),0)
            let geometry2 =new THREE.BufferGeometry().setFromPoints([mousePt,pt3]);          
            let line2 = new THREE.Line(geometry2, material2);
            this.tempGeometry.push(line2)

            const geometry = new THREE.BufferGeometry();
            geometry.setFromPoints([]);
            // 创建线条对象，使用LineLoop可以让首尾相连形成闭合的圆
            let mesh = new THREE.LineLoop(geometry, material1);
            let curve = new THREE.EllipseCurve(mousePt.x, mousePt.y,  radius,radius, 0, 2 * Math.PI, false,0);
            let vertices=curve.getPoints(64)     
            mesh.geometry.setFromPoints(vertices);
            this.tempGeometry.push(mesh)
        }
        this.showTempGeometry()
        return true
    }  
}
   

export function createCircleDragReplyByGripPoint(gpoint,viewer){

    let res = new DragCircleReply(viewer,gpoint)
    return res
}