import instance from '@/http/index'

// 获取API基础URL，从axios.js导入
import { API_URLS } from '../utils/axios.js';

const { VITE_ISOLAR_BUILD_URL } = API_URLS;
/**
 * 查询实物构建类
 * @returns 
 */
export function queryMaterialComponentTree() {
  return instance({
    url: VITE_ISOLAR_BUILD_URL + "/whitelist/queryMaterialComponentTree",
    method: 'get',
    // headers: {
    //   'Content-Type': 'application/json'
    // }
  })
}
/**
 * 查询图例详情
 * @param {*} data 
 * data = [
    {field: 'identifyItem',value: 'IDENTIFY',pattern: 'EQUAL'},
    {field: 'details',value: '',pattern: 'NotNull'},
    {field: 'projectCode',value: projectCode.value,pattern: 'EQUAL'},
  ];
  params = {
      materialComponentCode: '',
  }
 * @returns 
 */
export function queryCadMetaDetail(data, params) {
  return instance({
    url: VITE_ISOLAR_BUILD_URL + "/whitelist/v2/queryCadMetaDetail",
    headers: {
      'Content-Type': 'application/json'
    },
    method: 'post',
    data,
    params,
  })
}


// cadMeta2MaterialComponentAttributes
// export function cadMeta2MaterialComponentAttributes(data) {
//   return instance({
//     url: VITE_ISOLAR_BUILD_URL + "/whitelist/cadMeta2MaterialComponentAttributes",
//     method: 'post',
//     data,
//     headers: {
//       'Content-Type': 'application/json'
//     }
//   })
// }





