
import { DynamicScene } from "dxf-viewer/src/scene/DynamicScene";
import { DynamicSceneType } from "dxf-viewer/src/constant"
import { createDragReplyByGripPoint, createGripPointsById } from "./GripCommon";
import { pickupInfo } from "dxf-viewer/src";

//重写选择scene，需对夹点进行管理，针对不同的夹点响应不同的行为
export class SelectScene extends DynamicScene{
    private gripPoints : any // 所有图元夹点数组
    private isDraging : any//是否在拖拽进行中，false表示可以选中夹点拖拽，true表示正在拖拽不允许选中夹点
    private isFinishDrag : any//是否刚完成拖拽
    public dragId : any
    public isPinchpoint:boolean=true //是否夹点编辑功能

    constructor(viewer){
        super(viewer,DynamicSceneType.Selection)
        this.gripPoints = []
        this.isDraging = false
        this.isFinishDrag = false
        this.dragId = -1
    }


    public setIsDraging(b){
        this.isDraging = b
    }
    public getIsDraging(): boolean{
        return this.isDraging
    }

    public setFinishDrag(b){
        this.isFinishDrag = b
    }
    public getFinishDrag(): boolean{
        return this.isFinishDrag
    }
    public highlightDragObj() : void{
        pickupInfo.isSelected = true
        pickupInfo.pickupIdlist.push(this.dragId)
        this.AddObjById(this.viewer.transaction.gpObj, this.dragId,{isEdit:true})
        this.buildscene()
        this.viewer.Render()
    }

    //点击了左键检查是否pick到了夹点
    //先用纯几何方法判断是否选中了夹点
    public onLButtonDown(pt){
        if(!this.isPinchpoint) return false
        let camera=this.viewer.camera
		const pixelSize = (camera.right - camera.left)/this.viewer.canvas.getBoundingClientRect().width/camera.zoom;
        const eps = pixelSize*15;
        if(!this.isDraging){
            let sgpt = new sg.Point2d(pt.x,pt.y)
            this.gripPoints.forEach(pttemp=>{
                if(sgpt.distanceTo(pttemp.gPoint)<eps){
                    this.isDraging = true
                    this.isFinishDrag = false
                    this.dragId = pttemp.objId
                    let pReply = createDragReplyByGripPoint(pttemp,this.viewer)
                    pReply.onEnter()//进入拖拽响应                   
                    return true
                }
            })
        }
        return false
    }

    override async AddObjById(gp, id, opt={color:'',isEdit:false}){
        if(this.isPinchpoint) {
            let gpt = createGripPointsById(id,gp)
            gpt.forEach(temp=>{
                this.gripPoints.push(temp)
            })
        }else {
            opt.isEdit=false
        }
        super.AddObjById(gp,id,opt)
    }
    override async addObjsByIds(gp,ids,opt={color:'',isEdit:false}){
        if(this.isPinchpoint) {
            ids.forEach(id=>{
                let gpt = createGripPointsById(id,gp)
                gpt.forEach(temp=>{
                    this.gripPoints.push(temp)
                }) 
            })
        }else {
            opt.isEdit=false
        }
        opt.color=opt.color?opt.color:''
        super.addObjsByIds(gp,ids,opt)
    }

    override clear(){
        super.clear()
        this.gripPoints = []
        this.isDraging = false
    }

}


