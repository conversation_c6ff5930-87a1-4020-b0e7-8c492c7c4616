/** Regex for parsing special characters in text entities. */
const SPECIAL_CHARS_RE = /(?:%%([dpcouDPCOU%]))|(?:%%([0-9]{3}))|(?:\\U\+([0-9a-fA-F]{4}))|(.)/g

/**
 * Parse special characters in text entities and convert them to corresponding unicode
 * characters.
 * https://knowledge.autodesk.com/support/autocad/learn-explore/caas/CloudHelp/cloudhelp/2019/ENU/AutoCAD-Core/files/GUID-518E1A9D-398C-4A8A-AC32-2D85590CDBE1-htm.html
 * @param {string} text Raw string.
 * @return {string} String with special characters replaced.
 */
export function ParseSpecialChars(text) {
    const charcodes = new Array<number>()
    text.replaceAll(SPECIAL_CHARS_RE, (match, p1, p2, p3, p4) => {
        if (p1 !== undefined) {
            switch (p1) {
            case "d":
            case "D":
            {
                const char = "\xb0"
                charcodes.push(char.charCodeAt(0))
                return char
            }
            case "p":
            case "P":
            {
                const char = "\xb1"
                charcodes.push(char.charCodeAt(0))
                return char
            }
            case "c":
            case "C":
            {
                const char = "\u2205"
                charcodes.push(char.charCodeAt(0))
                return char
            }
            case "o":
            case "O":
                /* Toggles overscore mode on and off, not implemented. */
                return ""
            case "u":
            case "U":
                /* Toggles underscore mode on and off, not implemented. */
                return ""
            case "%":
            {
                const char = "%"
                charcodes.push(char.charCodeAt(0))
                return char
            }
            }
        } else if (p2 !== undefined) {
            const code = parseInt(p2, 10)
            if (isNaN(code)) {
                return match
            }
            const char = String.fromCharCode(code)
            charcodes.push(code)
            return char
        }else if(p3  !== undefined) {
            const code = parseInt(p3, 16)
            if (isNaN(code)) {
                return match
            }
            const char = String.fromCharCode(code)
            charcodes.push(code)
            return char
        }else if(p4  !== undefined) {
            charcodes.push(p4.charCodeAt(0))
            return p4
        }
        console.log("这里不应该被执行")
        return match
    })
    return charcodes
}
