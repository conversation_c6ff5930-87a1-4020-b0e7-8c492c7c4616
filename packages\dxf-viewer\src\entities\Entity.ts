import { INDEXED_CHUNK_SIZE } from "../constant"
import { getLineTypeIdx } from "../viewer/DxfMaterials"

/** Internal entity representation. DXF features are decomposed into these simpler entities. Whole
 * entity always shares single material.
 */
export class Entity {//new Entity
    /** @param type {number} See Entity.Type
     * @param vertices {{x, y}[]}
     * @param indices {?number[]} Indices for indexed geometry.
     * @param layer {?string}
     * @param color {number}
     * @param lineType {?number}
     * @param shape {Boolean} true if closed shape.
     */
    public type
    public vertices:number[]
    public indices:number[]|undefined
    public layer:string
    public color
    public rgb:number[]
    public colorMod:number
    public lineType:number|undefined
    public lineScale:number
    public shape
    public entityId:number
    public blockId:number
    public get renderId(){
        return this.entityId
    }
    public get mrgb(){
        return [this.colorMod,...this.rgb]
    }
    public get lineStyleId(){
        return getLineTypeIdx(this.lineType)
    }
    constructor({type, vertices, indices=undefined, layer = '', rgb=[255,255,255], color, colorMod=2, lineType = 0,lineScale=1, shape = false, entityId = 0, blockId=-1}) {
        this.type = type
        this.vertices = vertices
        this.indices = indices
        this.layer = layer
        this.color = color
        this.rgb=rgb
        this.colorMod = colorMod
        this.lineType = lineType
        this.lineScale = lineScale
        this.shape = shape
        this.entityId = entityId
        this.blockId = blockId
    }

    *_IterateVertices(startIndex, count) {
        for (let idx = startIndex; idx < startIndex + count; idx++) {
            yield [this.vertices[idx*2],this.vertices[idx*2+1]]
        }
    }

    *_IterateEntityIds(startIndex, count) {
        for (let idx = startIndex; idx < startIndex + count; idx++) {
            yield this.renderId
        }
    }
    *_IterateLineStyleIds(startIndex, count) {
        for (let idx = startIndex; idx < startIndex + count; idx++) {
            yield this.lineStyleId
        }
    }
    *_IterateLineScales(startIndex, count) {
        for (let idx = startIndex; idx < startIndex + count; idx++) {
            yield this.lineScale
        }
    }
    *_IterateEntityColors(startIndex, count) {
        for (let idx = startIndex; idx < startIndex + count; idx++) {
            yield this.mrgb
        }
    }

    /** Split line into chunks with at most INDEXED_CHUNK_SIZE vertices in each one. Each chunk is
     * an object with the following properties:
     *  * "verticesCount" - length of "vertices"
     *  * "vertices" - iterator for included vertices.
     *  * "indices" - iterator for indices.
     *  * "entityIds" - iterator for entity id.
     *  Closed shapes are handled properly.
     */
    *_IterateLineChunks() {
        const verticesCount = this.vertices.length/2
        if (verticesCount < 2) {
            return
        }
        const t = this
        /* chunkOffset == verticesCount for shape closing vertex. */
        for (let chunkOffset = 0; chunkOffset <= verticesCount; chunkOffset += INDEXED_CHUNK_SIZE) {

            let count = verticesCount - chunkOffset
            let isLast=true
            if (count > INDEXED_CHUNK_SIZE) {
                count = INDEXED_CHUNK_SIZE
                isLast = false
            } 
            if (isLast && this.shape && chunkOffset > 0 && count === INDEXED_CHUNK_SIZE) {
                /* Corner case - required shape closing vertex does not fit into the chunk. Will
                * require additional chunk.
                */
                isLast = false
            }
            if (chunkOffset === verticesCount && !this.shape) {
                /* Shape is not closed and it is last closing vertex iteration. */
                break
            }

            let vertices, indices, chunkVerticesCount, entityIds, entityColors,lineStyleIds,lineScales
            if (count < 2) {
                /* Either last vertex or last shape-closing vertex, or both. */
                if (count === 1 && this.shape) {
                    /* Both. */
                    vertices = (function*() {
                        yield [t.vertices[chunkOffset*2],t.vertices[chunkOffset*2+1]]
                        yield [t.vertices[0],t.vertices[1]]
                    })()
                    entityIds = (function*(){
                        yield t.renderId
                        yield t.renderId
                    })()
                    lineStyleIds = (function*(){
                        yield t.lineStyleId
                        yield t.lineStyleId
                    })()
                    lineScales = (function*(){
                        yield t.lineScale
                        yield t.lineScale
                    })()
                } else if (count === 1) {
                    /* Just last vertex. Take previous one to make a line. */
                    vertices = (function*() {
                        yield [t.vertices[(chunkOffset-1)*2],t.vertices[(chunkOffset-1)*2+1]]
                        yield [t.vertices[chunkOffset*2],t.vertices[chunkOffset*2+1]]
                    })()
                    entityIds = (function*(){
                        yield t.renderId
                        yield t.renderId
                    })()
                    lineStyleIds = (function*(){
                        yield t.lineStyleId
                        yield t.lineStyleId
                    })()
                    lineScales = (function*(){
                        yield t.lineScale
                        yield t.lineScale
                    })()
                } else {
                    /* Just shape-closing vertex. Take last one to make a line. */
                    vertices = (function*() {
                        yield [t.vertices[(chunkOffset-1)*2],t.vertices[(chunkOffset-1)*2+1]]
                        yield [t.vertices[0],t.vertices[1]]
                    })()
                    entityIds = (function*(){
                        yield t.renderId
                        yield t.renderId
                    })()
                    lineStyleIds = (function*(){
                        yield t.lineStyleId
                        yield t.lineStyleId
                    })()
                    lineScales = (function*(){
                        yield t.lineScale
                        yield t.lineScale
                    })()
                }
                indices = _IterateLineIndices(2, false)
                chunkVerticesCount = 2
            } else if (isLast && this.shape && chunkOffset > 0 && count < INDEXED_CHUNK_SIZE) {
                /* Additional vertex to close the shape. */
                vertices = (function*() {
                    yield* t._IterateVertices(chunkOffset, count)
                    yield [t.vertices[0],t.vertices[1]]
                })()
                entityIds = (function*(){
                    yield* t._IterateEntityIds(chunkOffset, count)
                    yield t.renderId
                })()
                lineStyleIds = (function*(){
                    yield* t._IterateLineStyleIds(chunkOffset, count)
                    yield t.lineStyleId
                })()
                lineScales = (function*(){
                    yield* t._IterateLineScales(chunkOffset, count)
                    yield t.lineStyleId
                })()
                indices = _IterateLineIndices(count + 1, false)
                chunkVerticesCount = count + 1
            } else {
                vertices = this._IterateVertices(chunkOffset, count)
                entityIds = this._IterateEntityIds(chunkOffset, count)
                
                lineStyleIds= this._IterateLineStyleIds(chunkOffset, count)
                lineScales= this._IterateLineScales(chunkOffset, count)
                entityColors= this._IterateEntityColors(chunkOffset, count)
                indices = _IterateLineIndices(count, isLast && chunkOffset === 0 && this.shape)
                chunkVerticesCount = count
            }
            yield {
                verticesCount: chunkVerticesCount,
                vertices,
                indices,
                entityIds,
                entityColors,
                lineStyleIds,
                lineScales
            }
        }
    }
}



function* _IterateLineIndices(verticesCount, close) {
    for (let idx = 0; idx < verticesCount - 1; idx++) {
        yield idx
        yield idx + 1
    }
    if (close && verticesCount > 2) {
        yield verticesCount - 1
        yield 0
    }
}