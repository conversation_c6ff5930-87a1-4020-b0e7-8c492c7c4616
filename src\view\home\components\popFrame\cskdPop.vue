<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="charactersPop">
      <el-dialog
          :model-value="cskdPopVisible"
          title="测试卡顿"
          width="500"
      >
          <div style="margin-top: 20px;font-size: 16px;color:#fff">
            <el-select v-model="ty" placeholder="类型" style="width: 350px">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select v-model="nu" placeholder="数量" style="width: 350px;margin-top: 15px;">
              <el-option
                v-for="item in options2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="btnSave">确认</el-button>
            <el-button @click="btnCancel">关闭</el-button>
          </div>
          </template>
      </el-dialog>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import {mitts,btnFnObj} from 'sgdraw/mittBus'

  const emits = defineEmits(['btnSavecskdPop','btnCancelcskd'])
  const props = defineProps({
    cskdPopVisible:{
        type: Boolean,
        default:false
     }
  })

  const ty = ref('')
  const nu = ref('')

  const options = [
    {
      value: 'straightLine',
      label: '直线',
    },
    {
      value: 'polyline',
      label: '多段线',
    },
  ]
  const options2 = [
    {
      value: 1000,
      label: '1000',
    },
    {
      value: 10000,
      label: '10000',
    },
    {
      value: 100000,
      label: '100000',
    },
  ]

  const btnSave=()=>{
    if(!ty.value) return
    if(!nu.value) return
    let obj={
      ty:ty.value,
      nu:nu.value,
    }
    emits('btnSavecskdPop',obj)
    
  }

  const btnCancel=()=>{
   emits('btnCancelcskd')
  }



  const cleanFn=()=>{
    ty.value=''
    nu.value=''
  }

  defineExpose({
      cleanFn
  })

</script>

<style lang="scss" scoped>
.charactersPop {
  :deep(.el-dialog) {
    background-color: #3a4352;
  }
  :deep(.el-dialog__body) {
      padding:0 !important;
  }
  :deep(.el-dialog__headerbtn) {
    width: 16px !important;
    height: 26px !important;
    top:1px;
    
  }
  :deep(.el-dialog__header) {
    padding:0 !important;
    padding-bottom:0;
    
    .el-dialog__title {
      font-size:16px;
      color: #fff !important;
    }
  }
  .sq {
    display: flex;
    align-items: center;
    .btn {
      width: 8px;
      height: 8px;
      background-color: blue;
    }
  }
}
</style>
  
  