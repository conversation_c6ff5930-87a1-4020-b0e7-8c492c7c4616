<template>
    <div class="commandInput col fb">
      <div class="row h20 f13 ">
        <svg-icon iconClass="cmd" w=20 h=20  style="margin-top: -2px;"></svg-icon>
        <span style="min-width: 30px;">命令:</span>
        <!-- <el-input style="height: 20px;background-color: beige;" v-model="cmdstr"></el-input> -->
        <cmdInput></cmdInput>
      </div>
      <div style="margin: 3px;"></div>
      <div class="row fb f15">
        <div class="col">
          <div >{{ globalDxf.wcsmouse.x.toFixed(5) }},  {{ globalDxf.wcsmouse.y.toFixed(5) }}</div>
          <div >{{ globalDxf.mouse.x.toFixed(5) }},  {{ globalDxf.mouse.y.toFixed(5) }}</div>
        </div>
        <div class="col">
          <div >{{ globalDxf.statistic.hatch }};{{ globalDxf.statistic.text }};{{ globalDxf.statistic.mtext }}</div>
        </div>
        <div class="row">
          <span>当前操作: </span>
          <span class="red ml10" >{{ globalDxf.operation }}</span>
        </div>
        <div class="row">
          <span>模式: </span>
          <span class="red ml10" >{{ globalDxf.drawstate }}</span>
        </div>
        <div class="row">
          <span>高亮: </span>
          <span class="red ml10" >{{ globalDxf.blockId }}:{{ globalDxf.highlId }}</span>
        </div>
        <div class="row">
          <span>视口: </span>
          <span class="red ml10" >{{ Math.round(globalDxf.view.wid) }} </span>
          <span class="ml10"> x</span>
          <span class="red ml10"> {{ Math.round(globalDxf.view.hei) }}</span>
        </div>
        <div class="row">
          <div class="auxbtn">栅格</div>
          <div class="auxbtn">正交</div>
          <div class="auxbtn">极轴</div>
          <div class="auxbtn" :style="stySnapOn" @click="switchSnap">对象捕捉</div>
          <div class="auxbtn" :style="styTrackOn" @click="switchTrack">对象追踪</div>
          <div class="auxbtn">DYN</div>
          <div class="auxbtn" :style="styLineWid" @click="switchLineWid">线宽</div>
          <div class="auxbtn" :style="styLodOn" @click="switchLod">Lod</div>
          <div class="auxbtn" :style="styPickBlock" @click="switchPick">块</div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import {globalDxf,HighLMod} from 'dxf-viewer'
  import cmdInput from './cmdInput.vue'
  const switchSnap=()=>{
    globalDxf.isSnapOn=!globalDxf.isSnapOn
  }
  const stySnapOn=computed(()=>{
    return {backgroundColor:(globalDxf.isSnapOn?'#618484a0':'#212832cc')}
  })
  const switchTrack=()=>{
    globalDxf.isTrackOn=!globalDxf.isTrackOn
  }
  const styTrackOn=computed(()=>{
    return {backgroundColor:(globalDxf.isTrackOn?'#618484a0':'#212832cc')}
  })
  const switchLineWid=()=>{
    globalDxf.isLineWidOn=!globalDxf.isLineWidOn
  }  
  const styLineWid=computed(()=>{
    return {backgroundColor:(globalDxf.isLineWidOn?'#618484a0':'#212832cc')}
  })
  const switchLod=()=>{
    globalDxf.isLodOn=!globalDxf.isLodOn
  }  
  const styLodOn=computed(()=>{
    return {backgroundColor:(globalDxf.isLodOn?'#618484a0':'#212832cc')}
  })
  const switchPick=()=>{
    globalDxf.highlMod=globalDxf.highlMod==HighLMod.Element?HighLMod.Block:HighLMod.Element;
  }  
  const styPickBlock=computed(()=>{
    return {backgroundColor:(globalDxf.highlMod==HighLMod.Element?'#618484a0':'#212832cc')}
  })
  const cmdstr=ref('')
  </script>
  
  <style lang="scss" scoped>
  .commandInput {
    background-color: #212832;
    color: white;
  }
  
  .auxbtn{
    width: fit-content;
    background-color: #212832;
    text-align: center;
    border: 1px dashed rgba($color: #618484a0, $alpha: 1.0);
    margin-left: 4px;
    padding: 0px 4px;
  }


  </style>
  
  