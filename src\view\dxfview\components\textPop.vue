<template>
    <div class="cad_dialogBody">
        <el-dialog
      :model-value="textpop"
      title="单行文字"
      width="430"
      class="textPop"
      :before-close="btnclosesizePop"
    >
    <div class="body">
        <div>文字参数</div>
        <div style="margin-top: 20px;">文字内容 <el-input v-model="value" style="width: 260px"/></div>
        <div style="margin-top: 10px;">旋转角度 <el-input v-model="rotationAngle" style="width: 160px"/></div>
    </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirm">确认</el-button>
          <el-button @click="btnclosesizePop">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    </div>

</template>

<script lang="ts" setup>
import { ref,onMounted,nextTick,reactive } from 'vue'
import {textBox} from 'dxf-viewer'
import { PubSub } from "emitter";

const textpop=ref(false)
const value=ref('')
const rotationAngle=ref(0)

const emits = defineEmits(['handleClose3'])
const props = defineProps({
    viewers:{
      type:Object,
    },
})
onMounted(()=>{
  PubSub.default.sub("textpop", openPop);//选择图元的图片
})

const openPop=()=>{
  textpop.value=true
}

const confirm=()=>{
  if(!value.value) return
  PubSub.default.pub('confirmPop',{type:'textpop',value:value.value,rotationAngle:rotationAngle.value})
  textpop.value=false
}

const btnclosesizePop=()=>{
    textpop.value=false
    // textBox.height=''
    rotationAngle.value=0
    // textBox.position=null
    value.value=''
}


defineExpose({

})
</script>

<style lang="scss" scoped>
.textPop {
}
</style>
  
  