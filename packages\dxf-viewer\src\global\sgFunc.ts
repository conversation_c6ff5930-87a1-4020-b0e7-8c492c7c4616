import { dxfZsObj } from "./indentify"

export const sgcolor2rgba=(sgcolor)=>{
    let r=sgcolor.getR()
    let g=sgcolor.getG()
    let b=sgcolor.getB()
    return `rgb(${r},${g},${b})`
}

export const sgcolor2Rgbs=(sgcolor)=>{
  let r=sgcolor.getR()/255
  let g=sgcolor.getG()/255
  let b=sgcolor.getB()/255
  return [r,g,b]
}

export const uploadFileAsText=(file)=> { //将文件解析成字符串
    return new Promise((resolve, reject) => {
        if (!file) {
            reject(new Error("No file selected."));
            return;
        }
        const reader = new FileReader();
        reader.onload = function(event) {
            // 文件读取成功，内容存储在event.target.result中
            resolve(event.target.result);
        };
        reader.onerror = function(error) {
            reject(error);
        };
        // 读取文件为文本
        reader.readAsText(file);
    });
}


export const filterByIds=(data, idss)=> {
    if(!data) return
    if(!idss) return
    return data.filter(item => {
        return item.ids.every(id => idss.includes(id));
    });
}
export const filterGroupsByIds=(groups=[], searchIds)=>{//ids数组包含数组searchIds中的任意一项则保留
    // 创建一个结果数组
    const result = [];
    const searchIdsSet = new Set(searchIds); // 将搜索ID转换为Set
  
    // 遍历groups数组
    groups.forEach(group => {
      // 创建一个新的graphGroups数组
      const filteredGraphGroups = group.graphGroups.map(graphGroup => {
        // 对于每个graphGroup，创建一个新的idList数组
        const filteredIdList = graphGroup.idList.filter(idItem => {
          const idSet = new Set(idItem.ids);
          // 检查两个Set是否有交集，如果有交集则保留
          return [...searchIdsSet].some(id => idSet.has(id));
        });
  
        // 返回新的graphGroup对象
        return {
          ...graphGroup,
          idList: filteredIdList
        };
      }).filter(graphGroup => graphGroup.idList.length > 0); // 过滤掉没有idList的graphGroup
  
      // 如果filteredGraphGroups中有元素，说明找到了匹配项
      if (filteredGraphGroups.length > 0) {
        // 将匹配的graphGroups添加到新的group对象中，并将其推入结果数组
        result.push({
          materialComponentName: group.materialComponentName,
          graphGroups: filteredGraphGroups
        });
      }
    });
  
    return result;
}

export const backfilterGroupsByIds=(groups=[], searchIds)=>{//ids数组包含数组searchIds中的任意一项则不保留
    // 创建一个结果数组
    const result = [];
    const searchIdsSet = new Set(searchIds); // 将搜索ID转换为Set
  
    // 遍历groups数组
    groups.forEach(group => {
      // 创建一个新的graphGroups数组
      const filteredGraphGroups = group.graphGroups.map(graphGroup => {
        // 对于每个graphGroup，创建一个新的idList数组
        const filteredIdList = graphGroup.idList.filter(idItem => {
          const idSet = new Set(idItem.ids);
          // 检查两个Set是否有交集，如果有交集则不保留
          return ![...searchIdsSet].some(id => idSet.has(id));
        });
  
        // 返回新的graphGroup对象
        return {
          ...graphGroup,
          idList: filteredIdList
        };
      }).filter(graphGroup => graphGroup.idList.length > 0); // 过滤掉没有idList的graphGroup
  
      // 如果filteredGraphGroups中有元素，说明找到了匹配项
      if (filteredGraphGroups.length > 0) {
        // 将匹配的graphGroups添加到新的group对象中，并将其推入结果数组
        result.push({
          materialComponentName: group.materialComponentName,
          graphGroups: filteredGraphGroups
        });
      }
    });
  
    return result;
}

export const hasItBeenSelected=(data,idss)=>{
    if(!data) return
    if(!idss) return
    return idss.filter(id=>{
        return data.some(item=>item.ids.includes(id))
    })
}
export const hasItBeenSelectedObj=(data,idss)=> {
    if(!data) return
    if(!idss) return
    let arr:any=[]
    idss.forEach(id=>{
        data.forEach(item=>{
            if(item.ids.includes(id)){
                arr.push({
                    ids:[id],
                    // groupCode:item.groupCode,
                    // hashCode:item.hashCode,
                })
            }
        })
    })
    return arr
}

export const setSelectedIdsBoundingBox=(list)=>{
    let arrZB:any=[]
    let gp= sg.DxfImportManager.getDxf(dxfZsObj.drawingId)//图纸ID
    list.forEach((ite=>{
        let box=new sg.Rect2d()
        ite.idLIst.forEach(it=>{
            let obj=gp.getPBaseObjById(it)
            box.add(obj.getBoundingBox())
        })
        let czb=box.getMiddlePoint()//中心点坐标
        let lzb=box.getLeftTopPoint()//左上角
        let width=box.width()
        let heigth=box.heigth()

        arrZB.push({
            middlePoint:{x:czb.x(),y:czb.y()},
            leftTopPoint:{x:lzb.x(),y:lzb.y()},
            width,
            heigth,
            idLIst:ite
        })
    }))
    return arrZB
}


