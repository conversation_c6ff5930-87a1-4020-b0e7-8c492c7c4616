import { InstanceType } from "../constant"
export const vfs_linedash=(instanceType, dash=false,isDash=true)=>{
    const dashVertAtt=dash?`
        in uint id0;
        flat out uint vId0;
    `:``;
    const dashVertex=dash?`
        vId0=id0;
    `:``;
    const dashFragAtt=dash?`
        flat in uint vId0;
    `:``;
    const dashFrag=dash?`
        if(vId0!=entId){
            discard;
        }
    `:``;
    const dashStyleVertAtt=isDash?`
        uniform float scale;
        in uint linestyleId;
        in float linescale;
        flat out int vStyleId;
        in float lineDistance;
        out float vLineDistance;
    `:``;
    const dashStyleVertex=isDash?`
      	vStyleId = int(linestyleId);
        vLineDistance =  lineDistance / linescale;
    `:``;
    const dashStyleFragAtt=isDash?`
        uniform float dashOffset;
        uniform float lineStyle[100];
        uniform float dash[200];
        in float vLineDistance;
        flat in int vStyleId;
    `:``;
    const dashStyleFrag=isDash?`
        int off=int(lineStyle[vStyleId*3]);//偏移量
        int ndash=int(lineStyle[vStyleId*3+1]);
        float len=lineStyle[vStyleId*3+2];//
        float tmplen=0.0;

        for(int i=0; i<ndash; i++){
            float f=mod( vLineDistance + dashOffset, len);
            if(dash[off+i]<0.0){
                if(f>tmplen && f<tmplen+abs(dash[off+i])){
                    discard;
                }
            }
            tmplen+=abs(dash[off+i]);
        }
    `:``;
    const fullInstanceAttr = instanceType === InstanceType.FULL?
        `
        /* First row. */
        in vec3 instanceTransform0;
        /* Second row. */
        in vec3 instanceTransform1;
        /* Second row. */
        in vec4 instanceColor;
        out vec4 vInstanceColor;
        ` : "";
    const fullInstanceTransform = instanceType === InstanceType.FULL ?
        `
        pos.xy = mat2(instanceTransform0[0], instanceTransform1[0],
                        instanceTransform0[1], instanceTransform1[1]) * pos.xy +
                    vec2(instanceTransform0[2], instanceTransform1[2]);
        vInstanceColor=instanceColor;
        ` : "";

    const fullInstanceColorAttr= instanceType === InstanceType.FULL ?   `
    in vec4 vInstanceColor;
    ` : ``;
    const fullInstanceColor = instanceType === InstanceType.FULL ?   `
    int mod=int(vEntityColor.x);//
    int mod2=int(vInstanceColor.x);
    if(mod==2){//固定色
        vColor=vEntityColor.yzw/255.;
    }
    if(mod==1){
        vColor=layerColor;
        if(mod2==0){
            vColor=vInstanceColor.yzw/255.;
        }
        if(mod2==2){
            vColor=vInstanceColor.yzw/255.;
        }
    }
    if(mod==0){
        vColor=layerColor;
        // if(mod2==0){
        //     vColor=vInstanceColor.yzw/255.;
        // }
    }
    ` : `
    vColor=vEntityColor.yzw/255.;
    `;
    const vertex= `
        precision highp float;
        precision highp int;
        in vec2 position;
        in uint entityId;
        flat out uint entId;
        ${dashVertAtt}
        ${dashStyleVertAtt}
        in vec4 entityColor;
        out vec4 vEntityColor;
        ${fullInstanceAttr}

        uniform mat4 modelViewMatrix;
        uniform mat4 projectionMatrix;
        


        void main() {
            entId=entityId;
            ${dashVertex}
            ${dashStyleVertex}
            vEntityColor=entityColor;
            vec4 pos = vec4(position, 0.0, 1.0);
            ${fullInstanceTransform}
            gl_Position = projectionMatrix * modelViewMatrix * pos;
        }
        `
    const fragment= `
        precision highp float;
        precision highp int;
        uniform vec3 color;
        out vec4 fragColor;
        flat in uint entId;
        ${dashFragAtt}
        ${dashStyleFragAtt}
        in vec4 vEntityColor;
        uniform int neid;
        uniform uint eids[100];
        uniform vec3 layerColor;
        uniform int isHL;
        ${fullInstanceColorAttr}
        bool isInArray(uint element, uint arr[100], int arrlen) {
            for (int i = 0; i < arrlen; i++) {
                if (arr[i] == element) {
                    return true;  // 找到元素，返回 true
                }
            }
            return false;  // 如果未找到，返回 false
        }

        void main() {
            ${dashFrag}
            ${dashStyleFrag}   
            vec3 vColor=color;
            if(neid>0){
                if(!isInArray(entId, eids, neid)){
                    discard;
                }
            }
             
            ${fullInstanceColor}
            if(isHL==1){
                vColor=color;
            }
            fragColor = vec4(vColor, 1.0);
        }
        `
    return {vertex,fragment}
}
