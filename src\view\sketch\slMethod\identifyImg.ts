
import { pickupInfo,dxfZsObj ,globalDxf,splitBlockId,newAddLayer,
   hasItBeenSelectedObj,filterByIds, setSelectedIdsBoundingBox, 
   SlScopeType,DrawState} from 'dxf-viewer'
import { ElLoading ,ElMessage } from "element-plus";
import { ref,reactive,nextTick} from 'vue'
import { PubSub } from 'emitter';
import {getEntityRgba } from "dxf-viewer/src/scene"

export const graphicRecognitionObj=reactive({
  graphicRecognitionPop:false,
  regionalTypeList:[
    {
      label:'整张图纸',
      value:1,
    },
    {
      label:'矩形区域',
      value:2,
    },
    {
      label:'多边形区域',
      value:3,
    }
  ],
  graphicImage:'',
})

let dxfview:any=null
export class IdentifyImg {
    // public dxfview:any
    constructor(obj){
      dxfview=obj
    }
    public async identifyTheImages(){
      if(!pickupInfo.pickupIdlist.length) return
      if(pickupInfo.slcurrentstate !==SlScopeType.All && pickupInfo.slcurrentstate !==SlScopeType.Graphicelementimg) {
        if(!pickupInfo.identifyScope.length) return 
      }

      let {x,y}=globalDxf.wcsOff
      let plisyt=new sg.vector_Point2d()
      pickupInfo.identifyScope.forEach(item=>{
        plisyt.push_back(new sg.Point2d(item.x+x,item.y+y))
      })
      
     let arr= new sg.vector_int()
     pickupInfo.pickupIdlist.forEach(ite=> arr.push_back(ite))//选择的图元
     let arr2=new sg.vector_int()//排除的id

     if(pickupInfo.selectedID.length) {//已识别图元ID
      pickupInfo.selectedID.forEach(item=>arr2.push_back(item))
     }
    //  if(pickupInfo.customID.length) {//自定义图元ID
    //   pickupInfo.customID.forEach(item=>arr2.push_back(item))
    //  }
     let cs=new sg.ElementSelection(dxfZsObj.drawingId,arr,arr2,1)
     let hashCodestr= cs.getGeoString() //识别图元中，选择图元的生成的hashCodestr

      let cc= pickupInfo.slcurrentstate===SlScopeType.All || pickupInfo.slcurrentstate ===SlScopeType.Graphicelementimg?  
                         await new sg.CadElementMatchAlgor(cs)
                         :await new sg.CadElementMatchAlgor( plisyt,cs,1)  
                
      let se= cc.getResult()
      let cut=se.getResultCount()
      // console.log(cut,'cut');
      
      let arrID:any=[]
      let arrZB:any=[]
      let ceshi:any=[]
      for (let i=0;i<cut;i++){
      let id= se.getResult(i).getObjIds()//id
      let bw=sg.getElementObjRect(dxfZsObj.drawingId,se.getResult(i))//
      if(bw.isVoid()) continue
      let  czb=bw.getMiddlePoint()//中心点坐标
      let lzb=bw.getLeftTopPoint()//左上角
      let width=bw.width()
      let heigth=bw.heigth()
      let cut=id.size()
      let obj:any={
       idLIst:[],
       middlePoint:{x:czb.x(),y:czb.y(),z:0}
      }
      for (let j=0;j<cut;j++) {
       arrID.push(id.get(j))
       obj.idLIst.push(id.get(j))
      }

      arrZB.push({
        middlePoint:{x:czb.x(),y:czb.y()},
        leftTopPoint:{x:lzb.x(),y:lzb.y()},
        width,
        heigth,
        idLIst:obj
      })
       ceshi.push(obj)
      }
      pickupInfo.identifiedIds=ceshi //识别ids
      
      pickupInfo.hashCodestr=hashCodestr
      // console.log(pickupInfo.identifiedIds,'pickupInfo.identifiedIds');
      // console.log(pickupInfo.identifiedInfo,'pickupInfo.identifiedInf');
      // console.log(pickupInfo.hashCodestr,'pickupInfo.hashCodestr');
      pickupInfo.identifiedIds=ceshi.map((item,index)=>{
        item.id=index
        return item
      })
      
      pickupInfo.identifiedInfo=arrZB.map((item,index)=>{
        item.id=index
        return item
      }) //查看列表
      
      
      this.idEchodisplay('')
    }
    public idEchodisplay(color){
      dxfview.GetViewer().drawingDisplay()

      // console.log(pickupInfo.identifiedIds,'pickupInfo.identifiedIds');
      // let arr=[
      //     {
      //       idLIst:[388780004,388780003],
      //       middlePoint:{x:594459.870795 , y:4198155.330224, z: 0}
      //     },
      //     {
      //       idLIst:[388780010, 388780009],
      //       middlePoint:{x: 594433.6707949999, y: 4198155.330224, z: 0}
      //     },
      //     {
      //       idLIst:[388480004, 388480003],
      //       middlePoint:{x: 594539.7315309999, y: 4198135.330223998, z: 0}
      //     },
      //     {
      //       idLIst:[388480010, 388480009],
      //       middlePoint:{x: 594513.5315309998, y: 4198135.330223998, z: 0}
      //     }
      // ]
      // dxfview.GetViewer().slIdentifiedLSscene.createdLSGp(arr)
      // dxfview.GetViewer().Render()
    }

    public graphicelementAttribute(){
      if(pickupInfo.pickupIdlist.length !== 1) return {}
      let id=pickupInfo.pickupIdlist[0]
      let gp=dxfview.GetViewer().transaction.gpObj
      let {entityId,blockId}=splitBlockId(id)
      let entity
      if(blockId) {
        entity=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
      }else {
        entity=gp.getPBaseObjById(id)
      }
      let layername=entity?.getLayer()
      let color=getEntityRgba(entity)
      let lineType=entity.getLineType()
      return {
        layername,
        color,
        lineType
      }
    }
    public identifyAttribute(obj,scale){
      if(!(pickupInfo.pickupIdlist.length===1)) return
      console.log(pickupInfo.slcurrentstate,'pickupInfo.slcurrentstate');
      if(pickupInfo.slcurrentstate !==SlScopeType.All && pickupInfo.slcurrentstate !==SlScopeType.PeiDistinguish) {
        if(!pickupInfo.identifyScope.length) return 
      }
      let identifyresult:any=null
      if(pickupInfo.slcurrentstate===SlScopeType.All || pickupInfo.slcurrentstate ===SlScopeType.PeiDistinguish) {
        identifyresult=sg.getTheSameDrawObj( dxfZsObj.drawingId, pickupInfo.pickupIdlist[0], obj.isSameLayer, obj.isSameColor, obj.isSameLineType)
      }else {
        let {x,y}=globalDxf.wcsOff
        let plisyt=new sg.vector_Point2d()
        pickupInfo.identifyScope.forEach(item=>{
          plisyt.push_back(new sg.Point2d(item.x+x,item.y+y))
        })
        identifyresult=sg.getTheSameDrawObj_Range(dxfZsObj.drawingId,plisyt,pickupInfo.pickupIdlist[0], obj.isSameLayer, obj.isSameColor, obj.isSameLineType)
      }
      const size = identifyresult.size()
      let arr:any=[]
      let gparr:any=[]
      let trans= dxfview.GetViewer().transaction
      let gpwasm= dxfview.GetViewer().transaction.gpObj
      newAddLayer(gpwasm,pickupInfo.structureTypeName)
      for(let i =0; i< size; i++){
        const id = identifyresult.get(i)
        let {entityId,blockId}=splitBlockId(id)
        let sgCurve:any =null
        let newobj:any=null
        let objinfo={
          type:'length',
          length:0,
        }
        if(blockId) {
            newobj=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
        }else {
            newobj=gpwasm.getPBaseObjById(id).copyNewOne()
        }
        newobj.setColor(new sg.SGColor(236, 128, 141,255))
        const type = newobj.getObjType()
        if(type === 'SGObjCurve'){
            sgCurve = sg.SGObjTool.convertToCurve(newobj).getCurve()
        }else if(type === 'SGObjComBinCurve'){
            sgCurve=sg.SGObjTool.convertToComBinCurve(newobj).getCurve()
        }
        let fenid=newobj.getDrawObjectId()
        pickupInfo.selectedID.push(Number(fenid))
        const newid =fenid - Number(pickupInfo.bluePrintCount)
        
        objinfo.id='A' + newid
        objinfo.length=sgCurve.length()* Number(scale)
        arr.push(objinfo)
        newobj.setLayer(pickupInfo.structureTypeName)
        gparr.push(newobj)
      }
      pickupInfo.pickupIdlist = []
      trans.add(gparr)
      pickupInfo.identifyScope=[]
      return arr
    }

    public pickupResultClear(){
      pickupInfo.pickupFalg=false
      PubSub.default.pub("slidentifyclear");
      pickupInfo.identifiedIds=[]
      pickupInfo.identifiedInfo=[]
      pickupInfo.pickupOBjImgurl=''
      pickupInfo.hashCodestr=''
      pickupInfo.pickupIdlist=[]
      pickupInfo.identifyScope=[]
      dxfview.GetViewer().selectionScene.clear()
      dxfview.GetViewer().Render()
    }

    // private _editIdentifyTheImages(){//编辑状态 识图
    //   if(!pickupInfo.pickupIdlist.length) return

    //   let ele=hasItBeenSelectedObj(pickupInfo.otherSelectedIdObj.elements, pickupInfo.pickupIdlist)
    //   let lsentityId = pickupInfo.pickupIdlist.filter(item => !ele.includes(item));
    //   let gro=filterByIds(pickupInfo.otherSelectedIdObj.groups,lsentityId) 

    //   let newselectedIdList:any=[]

    //   ele.forEach(item=>{
    //     pickupInfo.otherSelectedIdObj.elements.forEach(ite=>{
    //       if(ite.hashCode===item.hashCode) {
    //         ite.ids.forEach(it=>newselectedIdList.push({idLIst:[it]}))
    //       }
    //     })
    //   })
    //   gro.forEach(item=>{
    //     pickupInfo.otherSelectedIdObj.groups.forEach(ite=>{
    //       if(ite.hashCode===item.hashCode) {
    //         newselectedIdList.push({
    //           idLIst:ite.ids,
    //         })
    //       }
    //     })
    //   })
    //   pickupInfo.identifiedInfo=setSelectedIdsBoundingBox(newselectedIdList)
    //   pickupInfo.identifiedIds=newselectedIdList
      
    //   this.idEchodisplay('green')
    // }
      // let states=dxfview.GetViewer().selector.sl_identify_imagesState
      // if(states===IdentifyState.Edit){
      //   this._editIdentifyTheImages()
      //   return 
      // }

    public destroyDxfViewer(){
      dxfview=null
    }
}