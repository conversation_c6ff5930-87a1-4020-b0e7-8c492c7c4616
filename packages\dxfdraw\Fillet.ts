import * as THREE from "three";

import { CadMessageReply } from "./CadMessageReply";
import { KeyboardKey } from "dxf-viewer/src/controls";
import { DrawState, globalDxf, pickupInfo ,poptip} from "dxf-viewer";
import { singal } from "emitter";
import { DrawHelp } from "./drawHelp";
export class Fillet extends DrawHelp {
  public fisrtObjID: any;
  public secondObjID: any;
  public fisrtPoint: any;
  public secondPoint: any;
  public radius: any;
  public state: any; //0
  constructor(viewer, ids) {
    super(viewer);
    this.radius = 2.0;
    globalDxf.drawstate = DrawState.Pick;
    this.isSnap = false;
    this.state=0;
    this.init();
  }
  public async init() {
    await this.pick0();
    await this.pick1();
    await this.pick3({ tip: "是否保留" });
    poptip.dispose();
  }
  public async pick0() {
    await poptip.tip({ tip: "请选择第一个对象" });
    await this.select();
    await this.setState(1);
  }
  public async setState(v) {
    this.state = v;
  }
  public async pick1() {
    await poptip.tip({ tip: "请选择第二个对象" });
    await this.select();
  }
  public async pick3(opt){
    poptip.tip(opt)
    return new Promise((resolve)=>{
        this.onCancel()
        resolve([])  
       
    })
}
  public override onLButtonDown(event: PointerEvent): boolean {
    let mousePt = this.getEventPoint(event);
    let pt = this.threePointToSgPoint(mousePt);
    if (this.state == 0) {
      this.fisrtPoint = pt;
    }else if (this.state == 1) {
      this.secondPoint = pt;
    }
    return true;
  }

  public override onRButtonDown(event: PointerEvent): boolean {
    this.endSelect();
    return true;
  }

  //键盘按下
  public override onKeyDown(event: KeyboardEvent): boolean {
    if (event.key == KeyboardKey.Escape) {
      //取消
      this.onCancel();
    } else if (event.key == KeyboardKey.Space) {
      this.endSelect();
    }
    return true;
  }
  //最后一步添加图元
  private addObjects(): void {
    let createObjs: any[] = [];
    let delIds: any[] = [];

    let fisrtObj = this.cadCtx.getPBaseObjById(this.fisrtObjID);
    let secondObj = this.cadCtx.getPBaseObjById(this.secondObjID);
    let entityType1 = fisrtObj.getObjType();
    let entityType2 = secondObj.getObjType();
    let Polyline1;
    let Polyline2;
    if (entityType1 === "SGObjCurve" ) {
      let curveObj1 = sg.SGObjTool.convertToCurve(fisrtObj);
      Polyline1 = new sg.PolyLine2d();
      Polyline1.addBoundCurve2d(curveObj1.copyNewOne().getCurve());

    }else if (entityType1 === "SGObjComBinCurve") {
      let objCombinCurve1 = sg.SGObjTool.convertToComBinCurve(fisrtObj);
      Polyline1=objCombinCurve1.copyNewOne().getCurve();
    }
    if (entityType2 === "SGObjCurve") {
      let curveObj2 = sg.SGObjTool.convertToCurve(secondObj);
      Polyline2 = new sg.PolyLine2d();
      Polyline2.addBoundCurve2d(curveObj2.copyNewOne().getCurve());
    }else if (entityType2 === "SGObjComBinCurve") {
      let objCombinCurve2 = sg.SGObjTool.convertToComBinCurve(secondObj);
      Polyline2=objCombinCurve2.copyNewOne().getCurve();
    }
    let newPolyLine=Polyline1.chamFering(Polyline2,1,this.fisrtPoint,this.secondPoint);
    
    let newCombinCurve=new sg.SGObjComBinCurve();
    newCombinCurve.setpCurve(newPolyLine);
    delIds.push(this.fisrtObjID);
    delIds.push(this.secondObjID);
    createObjs.push(newCombinCurve);
    this.viewer.transaction.del(delIds);
    this.viewer.transaction.add(createObjs);
  }

  private endSelect() {
    //按空格键，确定
    if (this.state == 0) {
      //pick完成
      if (pickupInfo.pickupIdlist.length == 0) {
        console.log("未选取到对象,请重新选择");
      } else {
        this.fisrtObjID = pickupInfo.pickupIdlist[0];
        this.state = 1;
        singal.selectObj.dispatch();
        return;
      }
    } else if (this.state == 1) {
      if (pickupInfo.pickupIdlist.length == 0) {
        console.log("未选取到对象,请重新选择");
      } else {
        this.secondObjID = pickupInfo.pickupIdlist[0];
        this.addObjects();
        singal.selectObj.dispatch();
        return;
      }
    } else {
      this.onCancel();
    }
  }

   //绘制临时图元
  // private drawTempGraphic(secpoint): void {
  //   this.clearTempGeometry();
  //   let geometry1 = new THREE.BufferGeometry().setFromPoints([
  //     this.firstPoint,
  //     secpoint,
  //   ]);
  //   let material1 = new THREE.LineBasicMaterial({ color: "red" });
  //   let line1 = new THREE.Line(geometry1, material1);
  //   this.tempGeometry.push(line1);

  //   let borderCurve = new sg.SGObjCurve(
  //     this.threePointToSgPoint(this.firstPoint),
  //     this.threePointToSgPoint(secpoint)
  //   );
  //   this.MIds.forEach((id) => {
  //     let obj = this.cadCtx.getPBaseObjById(id);
  //     let newOne = obj.copyNewOne();
  //     let entityType = newOne.getObjType();
  //     if (entityType === "SGObjCurve") {
  //       let extendCurve = sg.SGObjTool.convertToCurve(newOne);
  //       let newCurveObj = sg.SGCurveObjTool.extend(extendCurve, 1, borderCurve);
  //       let newCurveObj2 = sg.SGCurveObjTool.extend(
  //         extendCurve,
  //         0,
  //         borderCurve
  //       );
  //       if (newCurveObj) {
  //         let points = newCurveObj.getCurve().getInterpolatePoints(50);
  //         let threeJsPoints: THREE.Vector3[] = [];
  //         for (let i = 0; i < points.size(); i++) {
  //           threeJsPoints.push(
  //             new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
  //           );
  //         }
  //         let geometry2 = new THREE.BufferGeometry().setFromPoints(
  //           threeJsPoints
  //         );
  //         let material2 = new THREE.LineBasicMaterial({ color: "green" });
  //         let line2 = new THREE.Line(geometry2, material2);
  //         this.tempGeometry.push(line2);
  //       } else if (newCurveObj2) {
  //         let points = newCurveObj2.getCurve().getInterpolatePoints(50);
  //         let threeJsPoints: THREE.Vector3[] = [];
  //         for (let i = 0; i < points.size(); i++) {
  //           threeJsPoints.push(
  //             new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
  //           );
  //         }
  //         let geometry2 = new THREE.BufferGeometry().setFromPoints(
  //           threeJsPoints
  //         );
  //         let material2 = new THREE.LineBasicMaterial({ color: "green" });
  //         let line2 = new THREE.Line(geometry2, material2);
  //         this.tempGeometry.push(line2);
  //       }
  //     } else if (entityType === "SGObjComBinCurve") {
  //       let polylineNew = sg.SGObjTool.convertToComBinCurve(newOne);
  //       let newCurveObj = sg.SGCurveObjTool.extendComBinCurve(
  //         polylineNew,
  //         0,
  //         borderCurve
  //       );
  //       let newCurveObj2 = sg.SGCurveObjTool.extendComBinCurve(
  //         polylineNew,
  //         1,
  //         borderCurve
  //       );
  //       console.log(newCurveObj);
  //       console.log(newCurveObj2);
  //       if (newCurveObj) {
  //         let points = newCurveObj.getCurve().getInterpolatePoints(50);
  //         let threeJsPoints: THREE.Vector3[] = [];
  //         for (let i = 0; i < points.size(); i++) {
  //           threeJsPoints.push(
  //             new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
  //           );
  //         }
  //         let geometry2 = new THREE.BufferGeometry().setFromPoints(
  //           threeJsPoints
  //         );
  //         let material2 = new THREE.LineBasicMaterial({ color: "green" });
  //         let line2 = new THREE.Line(geometry2, material2);
  //         this.tempGeometry.push(line2);
  //       } else if (newCurveObj2) {
  //         let points = newCurveObj2.getCurve().getInterpolatePoints(50);
  //         let threeJsPoints: THREE.Vector3[] = [];
  //         for (let i = 0; i < points.size(); i++) {
  //           threeJsPoints.push(
  //             new THREE.Vector3(points.get(i).x(), points.get(i).y(), 0)
  //           );
  //         }
  //         let geometry2 = new THREE.BufferGeometry().setFromPoints(
  //           threeJsPoints
  //         );
  //         let material2 = new THREE.LineBasicMaterial({ color: "green" });
  //         let line2 = new THREE.Line(geometry2, material2);
  //         this.tempGeometry.push(line2);
  //       }
  //       // let anycurve2d = polylineNew.getCurve();
  //       // let curveCount = anycurve2d.curveCount();
  //       // points = anycurve2d.getInterpolatePoints(10 * curveCount);
  //     }
  //   });
  //   this.showTempGeometry();
  // }
}
