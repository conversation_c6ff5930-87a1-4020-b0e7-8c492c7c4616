import 'src/types'
import 'ol/ol.css'
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
const app = createApp(App)
import router from './router'
app.use(router)

import { initSGD } from 'sgdraw/sgdd/initSgdd'
initSGD()



// import { init_planegcs_module, GcsWrapper } from '@salusoft89/planegcs';
// import wasm_url from "@salusoft89/planegcs/dist/planegcs_dist/planegcs.wasm?url";
// async function init_gcs_wrapper() {
//    const mod = await init_planegcs_module({ locateFile: () => wasm_url });
//    const gcs_system_wasm = new mod.GcsSystem();
//    const gcs_wrapper = new GcsWrapper(gcs_system_wasm);
//    return gcs_wrapper;
// }
// init_gcs_wrapper().then(gcs_wrapper => {
//     window.gcs_wrapper = gcs_wrapper

//     // explicit de-allocation of the Wasm memory must be called
//     //gcs_wrapper.destroy_gcs_module();
//  });


import pinia from '@/store'
app.use(pinia)
import svgComponent from '@/svg/index.vue';
import 'virtual:svg-icons-register'
app.component('svg-icon',svgComponent);

app.use(ElementPlus, { size: 'small', zIndex: 3000 })

import 'tdesign-vue-next/es/style/index.css';

// import {AppBuilder} from 'cad'
// import { PubSub } from 'cad-core'
// let cad=new AppBuilder()
// cad.useCanvas()
// // .useIndexedDB()
// .build().then(()=>{
//     PubSub.default.pub('executeCommand','doc.new')
//     app.mount('#app')
// })

import {CadUI} from './npmPublish/sketch'
app.use(CadUI)


app.mount('#app')
// app.mount('#ceshimicroapp')

// 卸载应用
window.unmount = () => {
    app.unmount()
}