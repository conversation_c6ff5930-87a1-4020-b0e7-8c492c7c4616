<template>
  <div class="drawtoolbar">
    <div class="btnBox zpointer" v-for="(item, index) in toolbarDrawBtns">
      <el-tooltip placement="right" :content="item.tip">
        <div :class="''" style="width: 40px;height: 40px;" @click="createShape(item)">
          <svg-icon :iconClass="item.icon" w=40 h=40 style="padding-top:0px"></svg-icon>
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>

import { toolbarDrawBtns } from '@/store/toolbartips'
import { PubSub } from 'emitter';
import { mitts, mittEvents } from 'sgdraw/mittBus'
const createShape = (item) => {
  // PubSub.default.pub('executeCommand','create.'+item.name)
  if (item.name === 'screenshot') {
    mitts.emit(mittEvents.screenshot, item)
    return
  }
  PubSub.default.pub('executeCommand', item.name)
}

// const btncanvasType=(item)=>{
//   mitts.emit(btnFnObj.drwIcon,{type:item.name})
// }

</script>

<style lang="scss" scoped>
.drawtoolbar {
  flex: 0 0 auto;
  background-color: #3a4352;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap-reverse;

  border-top: 1px solid #4f4f4f;

  .btnBox {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    margin: 10px 5px;

    &:hover {
      background-color: #616975;
    }
  }

  .bgc {
    background-color: #4f4f4f;
  }
}
</style>