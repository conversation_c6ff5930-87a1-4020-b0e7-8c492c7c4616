import { TextBoxParagraphChunk } from "./textBoxParagraphChunk"
import { TextBoxParagraphLine } from "./textBoxParagraphLine"

export class TextBoxParagraph {
    public textBox
    public chunks
    public curChunk
    public alignment
    public lines
    constructor(textBox) {
        this.textBox = textBox
        this.chunks = []
        this.curChunk = null
        this.alignment = null
        this.lines = null
    }

    /** Feed character for current chunk. Spaces should be fed by FeedSpace() method. If space
     * character is fed into this method, it is interpreted as non-breaking space.
     */
    FeedChar(c) {
        const shape = this.textBox.charShapeProvider(c)
        if (shape === null) {
            return
        }
        if (this.curChunk === null) {
            this._AddChunk()
        }
        this.curChunk.PushChar(c, shape)
    }

    FeedSpace() {
        if (this.curChunk === null || this.curChunk.lastChar !== null) {
            this._AddChunk()
        }
        this.curChunk.PushSpace()
    }

    SetAlignment(alignment) {
        this.alignment = alignment
    }

    /** Group chunks into lines.
     *
     * @param {?number} boxWidth Box width. Do not wrap lines if null (one line is created).
     */
    BuildLines(boxWidth) {
        if (this.curChunk === null) {
            return
        }
        this.lines = []
        let startChunkIdx = 0
        let curChunkIdx = 0
        let curWidth = 0

        const CommitLine = () => {
            this.lines.push(new TextBoxParagraphLine(this,
                                                       startChunkIdx,
                                                       curChunkIdx - startChunkIdx,
                                                       curWidth))
            startChunkIdx = curChunkIdx
            curWidth = 0
        }

        for (; curChunkIdx < this.chunks.length; curChunkIdx++) {
            const chunk = this.chunks[curChunkIdx]
            const chunkWidth = chunk.GetWidth(startChunkIdx === 0 || curChunkIdx !== startChunkIdx)
            if (boxWidth !== null && boxWidth !== 0 && curWidth !== 0 &&
                curWidth + chunkWidth > boxWidth) {

                CommitLine()
            }
            chunk.position = curWidth
            curWidth += chunkWidth
        }
        if (startChunkIdx !== curChunkIdx && curWidth !== 0) {
            CommitLine()
        }
    }

    GetMaxLineWidth() {
        if (this.lines === null) {
            return 0
        }
        let maxWidth = 0
        for (const line of this.lines) {
            if (line.width > maxWidth) {
                maxWidth = line.width
            }
        }
        return maxWidth
    }

    ApplyAlignment(boxWidth, defaultAlignment) {
        if (this.lines) {
            for (const line of this.lines) {
                line.ApplyAlignment(boxWidth, defaultAlignment)
            }
        }
    }

    _AddChunk() {
        this.curChunk = new TextBoxParagraphChunk(this, this.textBox.fontSize, this.curChunk)
        this.chunks.push(this.curChunk)
    }
}