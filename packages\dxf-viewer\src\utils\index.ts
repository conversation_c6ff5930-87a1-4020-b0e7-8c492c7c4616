import { ElMessage } from "element-plus";
import jsPDF from 'jspdf';
type TYPE_DrawArea_Rect = {
  start: { x: number; y: number };
  end: { x: number; y: number };
  drawing: boolean;
  status: {
    message: string;
    code: string;
  };
};

interface ImageSize {
  width: number;
  height: number;
}

class DrawArea {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  rect: TYPE_DrawArea_Rect;
  initialPos: { x: number; y: number } | any;
  mousedownHandler: (e: MouseEvent) => void;
  mousemoveHandler: (e: MouseEvent) => void;
  mouseupHandler: (e: MouseEvent) => void;
  contextMenuHandler: (callback: () => void) => void;

  constructor(canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D) {
    this.canvas = canvas;
    this.ctx = ctx;
    this.rect = {
      start: { x: 0, y: 0 },
      end: { x: 0, y: 0 },
      drawing: false,
      status: {
        message: "",
        code: "",
      },
    };
    // 绑定事件处理函数,避免this指向问题
    this.mousedownHandler = this.mousedownFun.bind(this);
    this.mousemoveHandler = this.mousemoveFun.bind(this);
    this.mouseupHandler = this.mouseupFun.bind(this);
    this.contextMenuHandler = this.setContextMenuHandler.bind(this);

    this.initBackground();
  }
  initBackground() {
    this.ctx.fillStyle = "rgba(255,255,255,0.3)";
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }
  // 获取鼠标在canvas上的坐标,考虑滚动距离
  getMousePos(e: MouseEvent) {
    const rect = this.canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left - window.scrollX,
      y: e.clientY - rect.top - window.scrollY,
    };
  }

  // 绘制矩形
  drawRectangle() {
    // 清除之前的绘制内容
    this.ctx.save();
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.initBackground();

    const width = this.rect.end.x - this.rect.start.x;
    const height = this.rect.end.y - this.rect.start.y;
    this.ctx.beginPath();

    // 绘制时使用虚线,完成时使用实线
    if (this.rect.drawing) {
      this.ctx.setLineDash([5, 5]);
      this.canvas.style.cursor = "crosshair";
    } else {
      this.ctx.setLineDash([]);
      this.canvas.style.cursor = "default";
    }
    this.ctx.rect(this.rect.start.x, this.rect.start.y, width, height);
    // this.ctx.strokeStyle = "#000";
    this.ctx.strokeStyle = "#ffff00";
    this.ctx.lineWidth = 2;

    this.ctx.stroke();
    this.ctx.restore();
  }

  mousedownFun(e: MouseEvent) {
    if (e.button !== 0) return;
    const pos = this.getMousePos(e);
    this.rect.start = pos;
    this.rect.end = pos;
    this.rect.drawing = true;
    // 添加标记初始位置
    this.initialPos = pos;
  }

  mousemoveFun(e: MouseEvent) {
    if (!this.rect.drawing) return;
    this.rect.end = this.getMousePos(e);
    requestAnimationFrame(() => this.drawRectangle()); // 使用requestAnimationFrame优化性能
  }

  mouseupFun(e: MouseEvent) {
    if (e.button !== 0) return;
    if (!this.rect.drawing) return;
    const currentPos = this.getMousePos(e);
    // 判断是否发生了拖动 (可以设置一个很小的阈值，比如 100 像素)
    const hasMoved =
      Math.abs(currentPos.x - this.initialPos.x) > 100 ||
      Math.abs(currentPos.y - this.initialPos.y) > 100;

    if (hasMoved) {
      this.rect.end = currentPos;
      this.rect.drawing = false;
      this.rect.status = {
        message: "success",
        code: "200",
      };
      this.drawRectangle();
    } else {
      // 单击时不执行清除和重绘
      this.rect.drawing = false;
      this.rect.status = {
        message: "屏幕截图区域必须大于100像素",
        code: "400",
      };
    }
    // this.rect.end = currentPos;
    // this.rect.drawing = false;
    // this.drawRectangle();
  }

  openEvents() {
    this.canvas.addEventListener("mousedown", this.mousedownHandler);
    this.canvas.addEventListener("mousemove", this.mousemoveHandler);
    this.canvas.addEventListener("mouseup", this.mouseupHandler);
    // 添加鼠标离开canvas时的处理
    // canvas 元素默认不能接收键盘事件,需要添加 tabindex 属性并设置 focus
    this.canvas.setAttribute("tabindex", "1");
    this.canvas.focus();
    this.canvas.addEventListener("keydown", this.keydownHandler.bind(this));
  }

  closeEvents() {
    this.canvas.removeEventListener("mousedown", this.mousedownHandler);
    this.canvas.removeEventListener("mousemove", this.mousemoveHandler);
    this.canvas.removeEventListener("mouseup", this.mouseupHandler);
    // this.canvas.removeEventListener('mouseleave', this.mouseupHandler);
    // 移除canvas
    this.canvas.remove();
  }
  clearRect() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
  }

  keydownHandler(e: { key: string; }) {
    if (e.key === "Escape") {
      // this.clearRect();
      this.closeEvents();
    }
  }

  // 添加右键菜单回调函数
  setContextMenuHandler(callback: () => void) {
    this.canvas.addEventListener("contextmenu", (e: MouseEvent) => {
      e.preventDefault(); // 阻止默认右键菜单
      if (this.rect.status.code === "200") {
        callback(); // 执行自定义回调函数
      } else {
        ElMessage.error(this.rect.status.message);
        throw new Error(this.rect.status.message);
      }
    });
  }
}

class CanvasLayer {
  layer: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  constructor(canvas: HTMLCanvasElement, _opt: any) {
    // 创建一个临时图层用于交互绘制
    this.layer = document.createElement("canvas");
    this.layer.id = "canvasLayer";
    this.layer.className = `canvasLayer-${_opt.className}`;

    // 设置临时图层尺寸与主画布一致
    const { width, height } = canvas;
    this.layer.width = width;
    this.layer.height = height;
    // 启用透明通道
    const ctx = this.layer.getContext("2d", { alpha: true });
    if (!ctx) {
      throw new Error("Failed to get 2d context");
    }
    this.ctx = ctx;

    // 设置临时图层样式和定位
    Object.assign(this.layer.style, {
      position: "absolute",
      left: `${canvas.offsetLeft}px`,
      top: `${canvas.offsetTop}px`,
      zIndex: "1",
      pointerEvents: "auto", // 确保可以接收鼠标事件
    });
  }
}

class ImageToPdf {
  private dataUrl: string;

  constructor(dataUrl: string) {
    this.dataUrl = dataUrl;
  }
  getImageSizeFromBase64(base64: string): Promise<ImageSize> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.onerror = reject;
      img.src = base64;
    });
  }

  // 截图
  async export() {
    const imgData = this.dataUrl
    // 把图片放在pdf里面再下载
    const pdf = new jsPDF({
      orientation: "p",
      unit: "mm",
      format: "a4",
      putOnlyUsedFonts: true,
      floatPrecision: 16,
    });
    const { width, height } = await this.getImageSizeFromBase64(imgData);
    // 设置PDF页面宽度为210mm(A4纸宽度)
    const pdfWidth = 210;
    // 根据原始图片宽高比计算等比缩放后的高度
    const pdfHeight = (height * pdfWidth) / width;

    pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);
    pdf.save(`截图_${new Date().getTime()}.pdf`);

    // 打开打印对话框
    // pdf.output("dataurlnewwindow");
  }
}

function captureScreenshot(renderer: { render: (arg0: any, arg1: any) => void; domElement: CanvasImageSource; }, scene: any, camera: any, rect: { start: any; end: any; drawing?: boolean; status?: { message: string; code: string; }; }) {
  // 确保场景被渲染
  renderer.render(scene, camera);

  // 获取截图区域尺寸
  const { start, end } = rect;
  const width = Math.abs(end.x - start.x);
  const height = Math.abs(end.y - start.y);
  const x = Math.min(start.x, end.x);
  const y = Math.min(start.y, end.y);

  // 创建临时 canvas 并设置尺寸
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;

  const context = canvas.getContext('2d', { alpha: false }); // 禁用 alpha 通道以提高性能
  if (!context) {
    throw new Error('无法获取canvas上下文');
  }

  // 直接从渲染器的 domElement 复制指定区域到临时 canvas
  context.drawImage(
    renderer.domElement,
    x, y, width, height,  // 源图像裁剪区域
    0, 0, width, height   // 目标绘制区域
  );

  // 返回优化后的 PNG 格式图片数据
  return canvas.toDataURL('image/png', 0.9); // 设置适当的压缩比例
}

export { DrawArea, CanvasLayer, ImageToPdf, captureScreenshot };
// export default { DrawArea, CanvasLayer };
