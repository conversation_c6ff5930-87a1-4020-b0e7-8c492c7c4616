import instance from '../utils/axios.js'
// 获取API基础URL，从axios.js导入
import { API_URLS } from '../utils/axios.js';
// : { BASE_URL, UPLOAD_URL, VITE_BASE_MAP_BOX_URL, VITE_STANDARD_DATA_BASE_URL, VITE_UPLOAD_URL }
// 获取项目详情
export function apiGetProjectDetail(projectCode) {
    return instance({
        url: API_URLS.VITE_BASE_MAP_BOX_URL + `/v1/project/${projectCode}`,
        method: 'get',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
