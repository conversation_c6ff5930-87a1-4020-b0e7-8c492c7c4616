<template>
  <div class="commandDisplay">
    <!-- <el-input v-model="cmds"  style="width: 100%; height: 100%;" :autosize="{ minRows: 1, maxRows: 2 }" type="textarea" /> -->
    <textarea ref="textareacmds" class="console" id="textareacmds" cols="30" rows="10" v-model="cmds"
      readonly></textarea>
  </div>
</template>

<script setup>
import { PubSub } from 'emitter'
const cmds = ref(`terminal\/three> \n`)
const textareacmds = ref()

onMounted(() => {
  // setTimeout(() => {
  //   PubSub.default.pub('cmdMessage', { code: '', msg: 'hello' })
  //   PubSub.default.pub('cmdMessage', { code: '', msg: 'hello' })
  //   PubSub.default.pub('cmdMessage', { code: '', msg: 'hello' })
  //   PubSub.default.pub('cmdMessage', { code: '', msg: 'hello' })
  //   PubSub.default.pub('cmdMessage', { code: '', msg: 'hello' })
  //   PubSub.default.pub('cmdMessage', { code: '', msg: 'hello' })
  // }, 1000);

  // setInterval(() => {
  //   PubSub.default.pub('cmdMessage', { code: '', msg: 'terminal\/three\/outPut> ' + Math.random() * 10 })
  // }, 1000);

})
const recivemsg = ({ code, msg }) => {
  if (code == 'clear') {
    cmds.value = ''
  } else {
    cmds.value += msg + '\n'
  }
  nextTick(() => {
    if (textareacmds.value?.scrollHeight) {
      textareacmds.value.scrollTop = textareacmds.value.scrollHeight
    }
  })
}
// const btnFocus=()=>{
//   this.$nextTick(() => {
//     this.$refs.textareacmds.focus(); // 聚焦 <textarea>
    
//     // 设置光标位置到文本结尾
//     let textArea = this.$refs.textareacmds;
//     textArea.setSelectionRange(textArea.value.length, textArea.value.length);
//   });
// }
PubSub.default.sub('cmdMessage', recivemsg)

</script>

<style lang="scss" scoped>
.console {
  width: 100%;
  height: 100% !important;
  resize: none;
  outline: none;
  padding: 0;
  white-space: pre;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100% - 4px);
  background: rgb(var(--v-theme-console));
  color: rgb(var(--v-theme-on-console));
  padding-left: 10px;
  // padding: 10px;
  // margin: 10px;
  color: #fff;
  font-size: 16px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: var(--v-theme-console);
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: rgba($color: #fff, $alpha: .3);
    }
  }

  &::-webkit-scrollbar-thumb {
    cursor: pointer;
    background-color: rgba($color: #fff, $alpha: 0);

    &:hover {
      background-color: rgba($color: #fff, $alpha: .5);
    }
  }
}


.commandDisplay {
  background-color: #787878;
}
</style>
<style>
.el-textarea__inner {
  height: 100% !important;
  min-height: 60px !important;
}
</style>