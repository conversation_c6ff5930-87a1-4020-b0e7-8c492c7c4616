/** TEXT group attribute 72 values. */
export enum HAlign {
    LEFT= 0,
    CENTER= 1,
    RIGHT= 2,
    ALIGNED= 3,
    MIDDLE= 4,
    FIT= 5
}

/** TEXT group attribute 73 values. */
export enum VAlign{
    BASELINE= 0,
    BOTTOM= 1,
    MIDDLE= 2,
    TOP= 3
}

/** MTEXT group attribute 71 values. */
export enum MTextAlign{
    TOP_LEFT= 1,
    TOP_CENTER= 2,
    TOP_RIGHT= 3,
    MIDDLE_LEFT= 4,
    MIDDLE_CENTER= 5,
    MIDDLE_RIGHT= 6,
    BOTTOM_LEFT= 7,
    BOTTOM_CENTER= 8,
    BOTTOM_RIGHT= 9
}


/** Parses MTEXT formatted text into more convenient intermediate representation. The MTEXT
 * formatting is not well documented, the only source I found:
 * https://adndevblog.typepad.com/autocad/2017/09/dissecting-mtext-format-codes.html
 */

export enum MTextState {
    TEXT= 0,
    ESCAPE= 1,
    /* Skip currently unsupported format codes till ';' */
    SKIP_FORMAT= 2,
    /* For \pxq* paragraph formatting. Not found documentation yet, so temporal naming for now. */
    PARAGRAPH1= 3,
    PARAGRAPH2= 4,
    PARAGRAPH3= 5
}

export enum MTextEntityType {
    TEXT= 0,
    SCOPE= 1,
    PARAGRAPH= 2,
    NON_BREAKING_SPACE= 3,
    /** "alignment" property is either "r", "c", "l", "j", "d" for right, center, left, justify
     * (seems to be the same as left), distribute (justify) alignment.
     */
    PARAGRAPH_ALIGNMENT= 4
    /* Many others are not yet implemented. */
}

export enum TextBoxParagraphAlign{
    LEFT= 0,
    CENTER= 1,
    RIGHT= 2,
    JUSTIFY= 3,
    NULL=-1
}


