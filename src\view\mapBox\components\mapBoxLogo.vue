<template>
  <div class="header-logo">
    <header class="layout__header">
      <div class="tdesign-starter-header-layout">
        <div
          class="menu head-menu menu--light tdesign-starter-header-menu-fixed"
        >
          <div class="head-menu__inner">
            <div class="menu__logo">
              <!-- logo位 -->
              <svg-icon
                iconClass="mapBoxLogo"
                w="110"
                h="50"
                class="icon zpointer"
                style="padding-top: 0px"
              ></svg-icon>
            </div>
            <div class="menu-wrap">
              <div class="menu-wrap-left">
                <span class="header-logo-container">
                  <span class="logo-txt">iSolar-Build</span>
                </span>
              </div>
              <div class="menu-wrap-right">
                <div class="menu">
                  <div class="header-menu">
                    <div class="menu__item is-active">
                      <span class="menu__content">{{ projectName }}</span>
                      <el-icon :size="14"><ArrowDown /></el-icon>
                    </div>
                  </div>
                </div>
                <div class="menu__operations">
                  <div class="operations-container">
                    <el-icon :size="20"><UserFilled /></el-icon>
                    <span class="button__text"> 用户名 </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  </div>
</template>

<script setup>
import { ArrowDown, UserFilled } from "@element-plus/icons-vue";
const emit = defineEmits(["add", "import", "delete", "search"]);
import { mapBoxConfig } from "../config";
const { projectName } = mapBoxConfig;
</script>

<style scoped lang="scss">
.header-logo {
  width: 100%;
  height: 100%;
  .layout__header {
    width: 100%;

    .tdesign-starter-header-menu-fixed .head-menu__inner {
      padding-right: 20px;
      width: 100%;
      display: flex;
      height: 56px;

      .menu-wrap {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-left: 10px;
        .menu-wrap-left {
          display: flex;
          .logo-txt {
            font-family: PingFang SC;
            font-size: 20px;
            font-weight: 700;
            line-height: 28px;
            margin-left: 10px;
            margin-right: 10px;
            white-space: nowrap;
          }
        }
        .menu-wrap-right {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin-left: 10px;
          .menu {
            .header-menu {
              .menu__item {
                display: flex;
                align-items: center;
                justify-content: center;
                color: #f86804;
                cursor: pointer;
                .menu__content {
                  font-size: 14px;
                  font-weight: 500;
                  line-height: 20px;
                  margin-right: 5px;
                }
              }
            }
          }
          .menu__operations {
            .operations-container {
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              .button__text {
                font-size: 14px;
                margin-left: 10px;
              }
            }
          }
        }
      }
    }
  }
}

:deep(.icon:hover) {
  background-color: transparent;
}
</style>
