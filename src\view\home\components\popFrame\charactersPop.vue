<!-- eslint-disable vue/no-mutating-props -->
<template>
    <div class="charactersPop">
        <el-dialog
            :model-value="charactersPopVisible"
            title="单行文字"
            width="300"
        >
            <div style="margin-top: 20px;font-size: 12px;">
                <div style="color: #fff;">文字内容：<input v-model="content" style="width: 60%;"/> </div>
                <div style="margin-top: 10px;color: #fff;">旋转角度：<input v-model="rotation" type="number" style="width: 25%;"/> </div>
                <div style="color: #fff;margin-top: 10px;">字体大小：<input v-model="contentsize" style="width: 15%;"/>px </div>
            </div>
            <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="btnSave">确认</el-button>
              <el-button @click="btnCancellation">关闭</el-button>
            </div>
            </template>
        </el-dialog>
    </div>
  </template>
  
  <script setup>
    import { ref } from 'vue'
    import { Search,Watermelon,Pear,Mug,Dish,Apple } from '@element-plus/icons-vue'

    const emits = defineEmits(['btnCancellationPop','btnSaveCharactersPop'])
    const props = defineProps({
        charactersPopVisible:{
          type: Boolean,
          default:false
       }
    })
    let content=ref('测试')
    let rotation=ref(0)
    let contentsize=ref(20)
    const cleanFn=()=>{
      content.value='测试'
      rotation.value=0
      contentsize.value=20
    }
    const handleClose=()=>{
        emits('handleClose3')
    }
    
    //文字弹框
    const btnSave=()=>{
      let obj={
        content:content.value,
        rotation:-rotation.value,
        contentsize:contentsize.value,
      }
        emits('btnSaveCharactersPop',obj)
    }
    const btnCancellation=()=>{
      emits('btnCancellationPop')
    }

 
    defineExpose({
        cleanFn
    })

  
  </script>
  
  <style lang="scss" scoped>
  .charactersPop {
    :deep(.el-dialog) {
      background-color: #3a4352;
    }
    :deep(.el-dialog__body) {
        padding:0 !important;
    }
    :deep(.el-dialog__headerbtn) {
      width: 16px !important;
      height: 26px !important;
      top:1px;
      
    }
    :deep(.el-dialog__header) {
      padding:0 !important;
      padding-bottom:0;
      
      .el-dialog__title {
        font-size:16px;
        color: #fff !important;
      }
    }
  }
  </style>
  
  