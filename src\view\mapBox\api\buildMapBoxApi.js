import instance from '@/http/index'
import md5 from "js-md5";
import axios from "axios";
import { apiQueryUploadFileUrl } from "@/view/mapBox/api/buildingBlockApi.js";
// 获取API基础URL，从axios.js导入
import { API_URLS } from '../utils/axios.js';

//发起图纸导出任务
export function apiTask(data) {
    return instance({
        url: API_URLS.VITE_DIGITAL_COMPONENT_BASE_URL + "/v1/cad/task",
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//获取任务列表
export function apiTaskList(params) {
    return instance({
        url: API_URLS.VITE_DIGITAL_COMPONENT_BASE_URL + "/v1/cad/task",
        method: 'get',
        params,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//获取任务详情
export function apiTaskDetail(id) {
    return instance({
        url: API_URLS.VITE_DIGITAL_COMPONENT_BASE_URL + "/v1/cad/task/" + id,
        method: 'get',
        // headers:{
        //     'Content-Type':'application/json'
        // }
    })
}

// 上传文件到OSS
export const uploadfileFn = (files) => {
    console.log(files, "uploadfileFn");
    return new Promise((resove, reacjet) => {
        let params = {
            name: files[0].name,
            size: files[0].size,
            md5: "",
        };
        const reader = new FileReader();
        reader.onloadend = function (e) {
            // 从事件对象中获取已读取的文件内容（转换为字节数组）
            const content = e.target.result;
            // 使用js-md5计算文件内容的MD5散列值
            params.md5 = md5(content);
            apiQueryUploadFileUrl(params)
                .then((res) => {
                    if (res.code === 200) {
                        if (res.data.url) {
                            var axiosInstance = axios.create();
                            axiosInstance.interceptors.request.use((config) => {
                                // 删除Content-Type，仅针对PUT请求
                                if (config.method === "put") {
                                    config.headers["Content-Type"] = "";
                                }
                                return config;
                            });
                            axiosInstance
                                .put(res.data.url, files[0])
                                .then((item) => {
                                    let reader = new FileReader();
                                    reader.readAsDataURL(files[0]);
                                    reader.onload = (e) => {
                                        resove({ storageFileId: res.data.storageFileId });
                                    };
                                })
                                .catch((error) => {
                                    reacjet(error);
                                });
                        } else {
                            resove({ storageFileId: res.data.storageFileId });
                        }
                    }
                })
                .catch((error) => {
                    reacjet(error);
                });
        };
        reader.readAsArrayBuffer(files[0]);
    });
};

export { apiQueryUploadFileUrl };
