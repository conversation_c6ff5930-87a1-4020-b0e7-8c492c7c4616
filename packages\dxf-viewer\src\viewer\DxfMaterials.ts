import { Color } from "three"
import { BatchEntityType, InstanceType } from "../constant"
import { createSimpleColorIdMaterial, createSimplePointIdMaterial, crtLineDashMaterial, crtLineMaterial, crtPoinstMaterial } from "../glsl"
import { RBTree } from "../libs"
import { <PERSON><PERSON><PERSON>ey, MaterialKey } from "../batch"
import { globalDxf, units } from "../global"
import { initLineTypeNams} from 'dxf-viewer'

const LineTypes_BY={
    'Continuous':{val:[1],scale:1},
    'CENTER':{val:[2, -0.1, 0.1, -0.1],scale:7.5},//点划线
    'DASH':{val:[0.5, -0.5],scale:10},//虚线
    'DASHED':{val:[0.5, -0.5],scale:2.5},//虚线
    'DASHED2':{val:[0.5, -0.5],scale:0.5},//虚线
    'ACAD_ISO02W100':{val:[0.5, -0.5],scale:1.5},//虚线
    'ACAD_ISO04W100':{val:[3, -0.1, 0.1, -0.1],scale:1},//点划线
    'ACAD_ISO10W100':{val:[3, -0.1, 0.1, -0.1],scale:1000},//点划线
    'ACAD_ISO05W100':{val:[3, -0.1, 0.1, -0.1, 0.1, -0.1],scale:1},//双点划线
    'ACAD_ISO06W100':{val:[3, -0.1, 0.1, -0.1, 0.1, -0.1, 0.1, -0.1],scale:1},//
    'ACAD_ISO07W100':{val:[0.05, -0.95],scale:1},
    'ACAD_ISO08W100':{val:[0.8, -0.2],scale:1},
}
let LineTypes0:any={}
let lineTypeNams:any=null

export const initDxfLineTypesData_2=(gp)=>{
    let data:any={}
    let wasmdata=gp.getDxfBaseData()
    let size=wasmdata.lineTypeCount()
    for(let i=0;i<size;i++) {
        let obj=wasmdata.getLineType(i)
        let linetypeName =obj.getLinetypeName()
        if(linetypeName=='ByLayer' || linetypeName=='ByBlock') continue
        let dashes=getvlist(obj.getDashes())
        dashes=dashes.length?dashes:[1] //直线
        data[linetypeName]={val:dashes,scale:1}
    }
    LineTypes0=JSON.stringify(data) === '{}'?LineTypes_BY:data
    lineTypeNams=Object.keys(LineTypes0)
    initLineTypeNams(lineTypeNams)

    // console.log(LineTypes0,'LineTypes0');
    
    
}



const getvlist=(obj)=>{
    let size=obj.size()
    let arr:any=[]
    for(let i=0;i<size;i++) {
        let l=obj.get(i) || 0.2
        arr.push(l)
    }
    return arr
}

const css=(view)=>{
    console.log('ppppppppppppp');
    const width = view.clientWidth;
    const height = view.clientHeight
    let { minX, minY, maxX, maxY } = view.bounds;
    let l1=Math.abs(maxX-minX) 
    console.log(l1,'ppppppppppppp');
    console.log(l1/width,'mmmmmmmmmmmmm');

}

const getLineTypes=(lts)=>{
    let s=globalDxf.isMetric?1:units.foot;
    let linetypes={}
    Object.keys(lts).forEach((k,i)=>{
        // linetypes[k]=lts[k].val.absNormal(lts[k].scale*s*10)
        linetypes[k]=lts[k].val.absNormal(lts[k].scale)
    })
    return linetypes
}
export const getLineTypeIdx=(linestyle)=>{
    let idx=lineTypeNams.indexOf(linestyle);
    if(idx<0){
        globalDxf.miss.linestyles.add(linestyle)
        idx=0
    }
    return idx
}
export const getLineStyleUniforms=()=>{ 
    let LineTypes=getLineTypes(LineTypes0)
    let lineStyle:number[]=[]
    let dash:number[]=[]
    let off=0
    Object.keys(LineTypes).forEach((k,i)=>{
        let dashVal=LineTypes[k]
        let len=dashVal.absSum()
        lineStyle.push(off,dashVal.length,len)
        dash.push(...dashVal)
        off+=dashVal.length

    })
    return {lineStyle,dash}
}
export enum ERenderMode{
    Default='Default',
    Frameid='Frameid',
    Dyanmic='Dyanmic'
}


export class DxfMaterials{
    public materials
    public idMaterials

    constructor (){
        this.materials = new RBTree((m1, m2) => m1.key.Compare(m2.key))
        this.idMaterials = new RBTree((m1, m2) => m1.key.Compare(m2.key))

    }
    getMaterial(rendermod,key:BatchingKey,instancetype,inColor=null){
        // let material=materialFunc.call(this,color,instancetype)
        if(rendermod==ERenderMode.Default){
            if(key.IsPoint()){
                return crtPoinstMaterial(instancetype)
            }
            return crtLineDashMaterial(instancetype)
        }else if(rendermod==ERenderMode.Frameid){
            if(key.IsPoint()){
                return createSimplePointIdMaterial(instancetype)
            }
            return createSimpleColorIdMaterial(instancetype)
        }else{
            if(key.IsPoint()){
                return crtPoinstMaterial(instancetype)
            }
            return crtLineMaterial(instancetype)
        }
    }

    _GetSimpleColorMaterial(color, instanceType = InstanceType.NONE) {
        const key = new MaterialKey(instanceType, null, color, 0)
        let entry = this.materials.find({key})
        if (entry) {
            return entry.material
        }
        let material=this._CreateSimpleColorMaterialInstance(color, instanceType)
        entry = {
            key,
            material
        }
        this.materials.insert(entry)
        return entry.material
    }

    /** @param color {number} Color RGB numeric value.
     * @param instanceType {number}
     */
    _CreateSimpleColorMaterialInstance(color, instanceType = InstanceType.NONE) {
        const src = this.simpleColorMaterial[instanceType]
        /* Should reuse compiled shaders. */
        const m = src.clone()
        m.uniforms.color = { value: new Color(color) }
        return m
    }


    /** @param color {number} Color RGB numeric value.
     * @param size {number} Rasterized point size in pixels.
     * @param instanceType {number}
     */
    _CreateSimplePointMaterialInstance(color, size = 2, instanceType = InstanceType.NONE) {
        const src = this.simplePointMaterial[instanceType]
        /* Should reuse compiled shaders. */
        const m = src.clone()
        m.uniforms.color = { value: new Color(color) }
        m.uniforms.pointSize = { value: size }
        return m
    }

    public dispose(){
        this.clear()

    }

    public clear(){
        this.materials.clear()
        this.idMaterials.clear()
    }
}