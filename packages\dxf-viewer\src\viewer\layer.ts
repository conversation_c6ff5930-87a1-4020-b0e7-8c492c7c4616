export class Layer {
    public name
    public displayName
    public color
    public rgb
    public objects:Set<any>|null
    constructor(name, displayName, color,rgb) {
        this.name = name
        this.displayName = displayName
        this.color = color
        this.objects = new Set()
        this.rgb=rgb
    }

    PushObject(obj) {
        this.objects?.add(obj)
    }
    RemoveObject(obj){
        this.objects?.delete(obj)
    }
    Dispose() {
        for (const obj of this.objects!) {
            obj.geometry.dispose()
        }
        this.objects = null
    }
}