<template>
  <draggable v-if="graphicRecognitionObj.graphicRecognitionPop" @draggableClose="draggableClose">
    <template #title>
      <h3 style="text-align: center;">图元识别</h3>
    </template>

    <template #body>
      <div>
        <h4 style="color: black;">定义构件图元</h4>
        <div style="margin-top: 8px;">
          选择图元 <el-button size="default" type="warning" @click="btnSelectGraphicElements">选择图元</el-button>
        </div>
        <el-image style="width: 100px; height: 100px" :src="pickupInfo.pickupOBjImgurl" fit="fill" />
      </div>

      <div style="margin-top: 20px;">
        <h4 style="color: black;">选择识别范围</h4>
        <div style="margin-top: 8px;">
          区域类型 
          <el-select
            v-model="value"
            placeholder="Select"
            size="large"
            style="width: 240px"
            :disabled="!pickupInfo.pickupOBjImgurl"
            @change="btnIdentificationScope"
          >
            <el-option
              v-for="item in regionalTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>

      <div style="margin-top: 20px;">
        <h4 style="color: black;">识别结果</h4>
        <div style="margin-top: 8px;">
          <div v-if="issearch">
            <el-button type="warning" size="default" :disabled="!pickupInfo.pickupOBjImgurl" @click="distinguish">开始识别</el-button>
          </div>
          <div v-else>
            <span>共识别到 {{ pickupInfo.identifiedInfo.length || 0 }} 条结果</span>
            <el-button type="warning" @click="see">查看</el-button>
            <el-button type="warning" @click="clear">清除</el-button>
          </div>

        </div>
      </div>
    </template>
  </draggable>
</template>
  
<script lang="ts" setup>
  import { ref,onMounted,nextTick,reactive } from 'vue'
  import { ElLoading ,ElMessage } from "element-plus";
  import draggable from './draggable.vue'

  import {graphicRecognitionObj,} from '../slMethod/identifyImg'
  import { pickupInfo,SlScopeType} from 'dxf-viewer'
  import { PubSub } from 'emitter';


   const regionalTypeList=[
    {
      label:'整张图纸',
      value:SlScopeType.All,
    },
    {
      label:'矩形区域',
      value:SlScopeType.RectangularRange,
    },
    {
      label:'多边形区域',
      value:SlScopeType.PolygonRange,
    }
  ]
  const value=ref(SlScopeType.All)

  const emits = defineEmits(['mapRecognitionResultsSee'])

  const props = defineProps({
    identifyImgObj:{
      type:Object
    },
    measurementCategoryObj:{
      type:Object
    },
    sLcontrolObj:{
        type:Object
    },
  })

  const issearch=ref(true)

  onMounted(()=>{
  })
  
  const draggableClose=()=>{
    graphicRecognitionObj.graphicRecognitionPop=false
    props.identifyImgObj.pickupResultClear()
    issearch.value=true
    value.value=SlScopeType.All
    props.sLcontrolObj.setState(SlScopeType.Empty)
  }

  const btnSelectGraphicElements=()=>{
    props.sLcontrolObj.setState(SlScopeType.Graphicelementimg)
  } 
  const btnIdentificationScope=(value)=>{
    PubSub.default.pub("slidentifyclear");
    if(value ===SlScopeType.All) {
    }else {
      props.sLcontrolObj.setState(value)
    }
  }
  const distinguish=()=>{//开始识别
    props.identifyImgObj.identifyTheImages()
    issearch.value=false
  } 
  const clear=()=>{
    props.identifyImgObj.pickupResultClear()
    issearch.value=true
    value.value=SlScopeType.All
    props.sLcontrolObj.setState(SlScopeType.All)
  }

  const see=()=>{
    emits('mapRecognitionResultsSee')
  }

  defineExpose({

  })
</script>

<style lang="scss" scoped>
.close {
   position: absolute;
   right: 5px;
   top: 5px;
   cursor: pointer;
   padding:5px 10px;
   &:hover{
    background-color: #ecf5ff;
   }
 }
</style>
  