import {SlIdentifiedScene} from './slIdentifiedScene'
import { splitBlockId } from "dxf-viewer/src/libs"
import {dxfZsObj} from 'dxf-viewer'
import { <PERSON><PERSON>, RenderBatch } from "../batch"
import { BatchEntityType } from '../constant'
import { Matrix3, Scene, Vector2 } from "three"

export class SlIdentifiedLsScene extends SlIdentifiedScene{
    public identifiedLsGp
    constructor(viewer){
        super(viewer)
    }
    public createdLSGp(arr){
        let blockArr:any=[]
        if(!arr.length) return
        arr.forEach((item,index)=>{
            let bkName='识别图元临时块'
            let bk:any=new sg.SGBlock(bkName,new sg.Point2d(0,0),true)
            let czb3:any=null
            let scale2:any=null
            if(item.idLIst.length && index===0) {
                let vlist=new sg.vector_int
                let isblock=false
                let objlist:any=[]
                let bdx=new sg.Rect2d()

                item.idLIst.forEach(id=>{
                    let {entityId,blockId}=splitBlockId(id)
                    let obj:any=null
                    if(blockId) {
                        vlist.push_back(id)
                        isblock=true
                        obj= this.viewer.transaction.gpObj.getInsertBaseObjById(blockId,entityId)
                        let kobj1= this.viewer.transaction.gpObj.getInsertBlockById(blockId)
                        scale2=kobj1.getXScale()
                    }else {
                        obj= this.viewer.transaction.gpObj.getPBaseObjById(id)
                    }
                    const newobj=obj.copyNewOne()
                    let temprect = newobj.getBoundingBox()
                    bdx.add(temprect)
                    newobj.setColor(new sg.SGColor(0, 255, 255,255))
                    objlist.push(newobj)
                })
                let midpt = bdx.getMiddlePoint();
                if(isblock) {
                    let ele0 = new sg.ElementObj(vlist);
                    czb3 = sg.getElementObjRect(dxfZsObj.drawingId,ele0).getMiddlePoint()
                    let ma = new sg.Matrix3();
                    ma.move(czb3.x()-midpt.x(),czb3.y()-midpt.y())
                    objlist.forEach(element => {
                        element.transform(ma)
                    });
                }else {
                    czb3=midpt
                }
                objlist.forEach(element => {
                    bk.addBaseObj(element)
                });
            }
            let ibk=new sg.InsertSGBlock()
            let str:any=''
            let isblock2=false
            ibk.setInsertBlockName(bkName)
            item.idLIst.forEach(i=>{
                let {entityId,blockId}=splitBlockId(i)
                if(blockId) {
                    isblock2=true
                }
                str =str+i+'&&'
            })
            if(isblock2) {
                ibk.setInsertBlockScale(scale2,scale2)
            }
            console.log(czb3,'czb3czb3');
            
            ibk.setInsertBlockInsertPoint(new sg.Point2d(item.middlePoint.x-czb3.x(),item.middlePoint.y-czb3.y()))
            ibk.writeStringXData('id',str)
            blockArr.push({
                block:bk,
                InsertBlock:ibk
            })
        })
        blockArr.forEach(e=>{
          if(!this.identifiedGp.isExistBlockName(e.block)) {
            this.identifiedGp.addBlock(e.block)
          }
          this.identifiedGp.addInsertBlock(e.InsertBlock)
          this.identifiedGp.calSpInsertBlockObjList(e.InsertBlock)
        })
        this.gpErgodic(this.identifiedGp) 

        this.processEntityBygp(this.identifiedGp)
        this.generateBlock()
        this.loadBatch()
    }
    public createdLSsenn(arr){
        console.log(arr,'lssene');
        this.scene.clear()
        this.batches.clear()
        this.identifiedLsGp=new sg.SGBaseDrawingGroup()
        let objlist:any=[]
        arr.forEach(id=>{
            let {entityId,blockId}=splitBlockId(id)
            let obj:any=null
            if(blockId) {
                obj= this.viewer.transaction.gpObj.getInsertBaseObjById(blockId,entityId)
            }else {
                obj= this.viewer.transaction.gpObj.getPBaseObjById(id)
            }
            const newobj=obj.copyNewOne()
            objlist.push(newobj)
        })
        objlist.forEach(obj=>{
            // console.log(obj,'lssenelssene');
            this.identifiedLsGp.addBaseObj(obj)
        })
        this.gpErgodic(this.identifiedLsGp) 
        this.processEntityBygp(this.identifiedLsGp)
        this.generateBlock()
        this.loadBatch()
    }
    public override loadBatch(){
        
        console.log(this.scene,'this.scene');
        
        this.batches.each(b=>{
                this._LoadBatch(b)
            }
        )
    }
    public override _LoadBatch( batch) {
        if (batch.key.blockName !== null &&
            batch.key.geometryType !== BatchEntityType.BLOCK_INSTANCE &&
            batch.key.geometryType !== BatchEntityType.POINT_INSTANCE) {
            /* Block definition. */
            return
        }

        const objects = new Batch(this.viewer,  batch).CreateObjects()
        // debugger
        for (const obj of objects) {
            this.scene.add(obj)
        }
    }

}