import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color,SplineCurve, LineBasicMaterial, Vector3,Line as ThLine } from "three"

export class Bspline extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    // public color:string='white'
    public els:any=[]
    public draw:boolean=true

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        console.log(this.color,'this.color');
        
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init()
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        while (this.draw) {
            let p1=await this.lengthPick({
                tip:'指定下一个点',
                refPoint:this.pnts[this.pnts.length-1],
                length:'',
                radius:''
            })
            this.setV(p1)
        }
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    public override onRButtonDown( event:PointerEvent ) : boolean{
        this.draw=false
        this.complete()        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        // this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
        switch ( this.pnts.length) {
			case 1:
                this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
				break;
			default:
                var points = this.getvertices([...this.pnts,v]);
                this.mesh.geometry=new BufferGeometry().setFromPoints(points);
                break;
		}
    }
    
    complete(){
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        poptip.dispose()
        if(this.pnts.length<2) return
        const wasmVPlist=this.setWasmVPlist(this.pnts)
        let wasmBulge = new sg.vector_double()
        const v=new sg.Vector2d(1,0)
        let  pl=new sg.BSpline2d(wasmVPlist)
        let  plobj=new sg.SGObjCurve()
        plobj.setpCurve(pl)
        let [r,g,b]=this.color
        plobj.setColor(new sg.SGColor(r,g,b,255))
        plobj.setLineType(this.lineType)
        this.els.push(plobj)
        this.viewer.transaction.add(this.els)
        globalDxf.operation=GraphicType.None
        globalDxf.drawstate=DrawState.Pick
        globalDxf.isdraw = false
        this.isSnap=false
    }
    private setWasmVPlist(vList){
        let {x,y}=globalDxf.wcsOff
        const wasmVPlist= new sg.vector_Point2d()
        vList.forEach(item=>{
            wasmVPlist.push_back(new sg.Point2d(item.x+x, item.y+y))
        })
        return wasmVPlist
    }
    private getvertices(vList:any) {
        const curve = new SplineCurve(vList)
        let num=20*(vList.length+1)
        const points = curve.getPoints( num);
        return points
    }
}





// import * as THREE from "three"
// import {ElementStyle} from './ElementStyle'

// export class Bspline extends ElementStyle {
//     public vPlist:THREE.Vector3[]=[]
//     public Bspline:any
//     public count:number=0
//     public iscomplete:boolean=false

//     constructor() {
//         // 调用父类的构造函数以初始化继承的属性
//         super();
//         // 创建几何体
//         const geometry = new THREE.BufferGeometry().setFromPoints([]);
//         // 创建材质
//         const material = new THREE.LineBasicMaterial({color: 'red'});
//         // 创建线对象
//         this.Bspline = new THREE.Line(geometry, material);
//     }
//     setV(v:THREE.Vector3){
//         this.count++
//         this.vPlist.push(v)
//     }
//     getmove(v:THREE.Vector3){
//         if(!this.vPlist.length) return
//         switch ( this.count) {
// 			case 1:
//                 this.Bspline.geometry=new THREE.BufferGeometry().setFromPoints([...this.vPlist,v]);
//                 this.Bspline.material= new THREE.LineBasicMaterial({ color:this.color});
// 				break;
// 			default:
//                 var points = this.getvertices([...this.vPlist,v]);
//                 this.Bspline.geometry=new THREE.BufferGeometry().setFromPoints(points);
//                 this.Bspline.material= new THREE.LineBasicMaterial({ color:this.color});
//                 break;
// 		}

//     }
//     complete(){
//         this.iscomplete=true
//     }
    
//     getisitCompleted(){
//         return this.iscomplete
//     }

//     private getvertices(vList:any) {
//         const curve = new THREE.SplineCurve(vList)
//         let num=20*(vList.length+1)
//         const points = curve.getPoints( num);
//         return points
//     }
// }