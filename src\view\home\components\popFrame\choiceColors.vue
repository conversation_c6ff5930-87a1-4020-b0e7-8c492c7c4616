<template>
  <div class="choiceColors">
    <el-select  @change="onChangeColor" v-model="curColor"  style="width: 100px;margin:0 5px;">
    <el-option v-for="itm,i in colorlist" :key="i"
        :label="itm.label"
        :value="itm">
    <div style="display: flex;align-items: center;">
    <div class="colorbtn":style="getColorStyle(i)"></div>
    <div>{{ itm.label }}</div>
  </div>
  </el-option>
    <template #prefix>
        <div class="fb fc" >
            <div class="colorbtn" :style="getCurColorStyle"> </div>
            {{ curColor.label }}
        </div>
    </template>
</el-select>
</div>
</template>
  
<script setup>
import {
  colorlist,dxfColor,
  changeSelectedColor,
  changeSelectedColor_2,
} from 'dxf-viewer'
const curColor=dxfColor.curColor
const toRgb=([r,g,b])=>{
    return `rgb(${r},${g},${b})`
}
const getColorStyle=computed(()=>(idx)=>{
    return {"background-color":toRgb(colorlist[idx].color)}
}) 
const getCurColorStyle=computed(()=>{
    return {"background-color":toRgb(curColor.color)}
}) 

const onChangeColor=(e)=>{
    Object.assign(curColor,e)
    changeSelectedColor(e)
    changeSelectedColor_2(e)
}

onMounted(()=>{
})

</script>
  
<style lang="scss" scoped>
.colorbtn{
    width: 12px;
    height: 12px;
    border: 1px solid gray;
    margin-right: 5px;
}
.fc{
    align-items: center
}
.choiceColors {
  display: flex;
  align-items: center;
  background-color: #3a4352;
  padding: 5px 10px;
  :deep(.el-select__selection) {
    visibility: hidden;
  }
  .icon {
    &:hover {
      background-color:#616975;
    }
  }
}
:deep(.el-popper) {
  border: none;
}
:deep(.el-scrollbar) {
  background-color: #212832;
  .is-hovering {
    background-color: #787878;
  }
}
:deep(.el-select__wrapper){
  background-color: #212832;
  border: none;
  box-shadow: none;
}
</style>
  
  