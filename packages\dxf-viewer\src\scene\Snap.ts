﻿import { Scene, Vector3, Line, EllipseCurve, LineBasicMaterial, BufferGeometry, LineSegments } from "three"
import { globalDxf, screen2wcs } from ".."
import { splitBlockId } from "dxf-viewer/src/libs";
import {dxfZsObj} from 'dxf-viewer'
enum OsnapMode {
	kOsModeEnd = 1,  // Endpoint
	kOsModeMid = 2,  // Midpoint
	kOsModeCen = 3,  // Center
	kOsModeNode = 4,  // Node
	kOsModeQuad = 5,  // Quadrant
	kOsModeIntersec = 6,  // Intersection
	kOsModeIns = 7,  // Insertion point
	kOsModePerp = 8,  // Perpendicular
	kOsModeTan = 9,  // Tangent
	kOsModeNear = 10, // Nearest
	kOsModeApint = 11, // Apparent intersection
	kOsModePar = 12, // Parallel
	kOsModeStart = 13  // Startpoint
};

export enum OsnapModeFlag {
	kOsModeNone = 0,
	kOsModeEnd = 1 << (OsnapMode.kOsModeEnd - 1),
	kOsModeMid = 1 << (OsnapMode.kOsModeMid - 1),
	kOsModeCen = 1 << (OsnapMode.kOsModeCen - 1),
	kOsModeNode = 1 << (OsnapMode.kOsModeNode - 1),
	kOsModeQuad = 1 << (OsnapMode.kOsModeQuad - 1),
	kOsModeIntersec = 1 << (OsnapMode.kOsModeIntersec - 1),
	kOsModeIns = 1 << (OsnapMode.kOsModeIns - 1),
	kOsModePerp = 1 << (OsnapMode.kOsModePerp - 1),
	kOsModeTan = 1 << (OsnapMode.kOsModeTan - 1),
	kOsModeNear = 1 << (OsnapMode.kOsModeNear - 1),
	kOsModeApint = 1 << (OsnapMode.kOsModeApint - 1),
	kOsModePar = 1 << (OsnapMode.kOsModePar - 1),
	kOsModeStart = 1 << (OsnapMode.kOsModeStart - 1),
	kEnd = kOsModeStart << 1
};



export class Snap {
	static setSnap(snapOn: boolean) {
		Snap.m_snapOn = snapOn
	}

	static turnOnSnap() {
		Snap.m_snapOn = true
	}
	static turnOffSnap() {
		Snap.m_snapOn = false
	}
	static snapAll() {
		Snap.snapModes = OsnapModeFlag.kEnd - 1
		//Snap.snapModes = OsnapModeFlag.kOsModeMid
	}
	static snapClear() {
		Snap.snapModes = 0
	}
	static addSnapMode(snapMode: OsnapModeFlag) {
		Snap.snapModes |= snapMode
	}

	static removeSnapMode(snapMode: OsnapModeFlag) {
		Snap.snapModes = Snap.snapModes & ~snapMode
	}

	private static m_snapOn = true
	private static snapModes: number = 0
    private _markerScene: Scene
    private endPointMarker: Line
    private nearestMarker: Line
    private centerMarker: Line
    private nodeMarker: Line
    private midPointMarker: Line
    private quadrantMarker: Line
    private material:LineBasicMaterial
	//public iscatch:boolean=false
    static rectSize = 1
    constructor(private viewer) {
        this.material = new LineBasicMaterial({
            //color: 0x00a500
            color: 0x2d852d
        })
        this._markerScene = new Scene()
        this.addEndPointMarker()
        this.addNearestMarker()
        this.addCenterMarker()
        this.addNodeMarker()
        this.addMidPointMarker()
        this.addQuadrantMarker()
		Snap.snapAll()
    }

	public get markerScene() {
		return this._markerScene;
	}

	private addEndPointMarker() {
		const upLeft = new Vector3(- Snap.rectSize / 2, Snap.rectSize / 2, 0)
		const upRight = new Vector3(upLeft.x + Snap.rectSize, upLeft.y, 0)
		const bottomRight = new Vector3(upRight.x, upRight.y - Snap.rectSize, 0)
		const bottomLeft = new Vector3(bottomRight.x - Snap.rectSize, bottomRight.y, 0)
		const points: Vector3[] = []
		points.push(upLeft)
		points.push(upRight)
		points.push(bottomRight)
		points.push(bottomLeft)
		points.push(upLeft)
		const geometry = new BufferGeometry().setFromPoints(points)

		this.endPointMarker = new Line(geometry, this.material)
		this._markerScene.add(this.endPointMarker)
		this.endPointMarker.visible = false
	}

	private addNearestMarker() {
		const upLeft = new Vector3(- Snap.rectSize / 2, Snap.rectSize / 2, 0)
		const upRight = new Vector3(upLeft.x + Snap.rectSize, upLeft.y, 0)
		const bottomRight = new Vector3(upRight.x, upRight.y - Snap.rectSize, 0)
		const bottomLeft = new Vector3(bottomRight.x - Snap.rectSize, bottomRight.y, 0)
		const points: Vector3[] = []
		points.push(upLeft)
		points.push(upRight)
		points.push(bottomLeft)
		points.push(bottomRight)
		points.push(upLeft)
		const geometry = new BufferGeometry().setFromPoints(points)

		this.nearestMarker = new Line(geometry, this.material)
		this._markerScene.add(this.nearestMarker)
		this.nearestMarker.visible = false
	}

	private addCenterMarker() {
		const curve = new EllipseCurve(
			0, 0,            // ax, aY
			1, 1,           // xRadius, yRadius
			0, 2 * Math.PI,  // aStartAngle, aEndAngle
			false,            // aClockwise
			0                 // aRotation
		);
		const points = curve.getPoints(50);
		const geometry = new BufferGeometry().setFromPoints(points)
		this.centerMarker = new Line(geometry, this.material)
		this._markerScene.add(this.centerMarker)
		this.centerMarker.visible = false
	}

	private addNodeMarker() {
		const upLeft = new Vector3(- Snap.rectSize / 2, Snap.rectSize / 2, 0)
		const upRight = new Vector3(upLeft.x + Snap.rectSize, upLeft.y, 0)
		const bottomRight = new Vector3(upRight.x, upRight.y - Snap.rectSize, 0)
		const bottomLeft = new Vector3(bottomRight.x - Snap.rectSize, bottomRight.y, 0)
		const points: Vector3[] = []
		points.push(upLeft)
		points.push(bottomRight)

		points.push(upRight)
		points.push(bottomLeft)

		const geometry = new BufferGeometry().setFromPoints(points)

		this.nodeMarker = new LineSegments(geometry, this.material)
		this._markerScene.add(this.nodeMarker)
		this.nodeMarker.visible = false
	}

	private addMidPointMarker() {
		const upMid = new Vector3(0, Snap.rectSize / 2, 0)
		const bottomRight = new Vector3(Snap.rectSize / 2, -Snap.rectSize / 2, 0)
		const bottomLeft = new Vector3(-Snap.rectSize / 2, -Snap.rectSize / 2, 0)
		const points: Vector3[] = []
		points.push(upMid)
		points.push(bottomRight)
		points.push(bottomLeft)
		points.push(upMid)
		const geometry = new BufferGeometry().setFromPoints(points)

		this.midPointMarker = new Line(geometry, this.material)
		this._markerScene.add(this.midPointMarker)
		this.midPointMarker.visible = false
	}

	private addQuadrantMarker() {
		const up = new Vector3(0, Snap.rectSize / 2, 0)
		const bottom = new Vector3(0, -Snap.rectSize / 2, 0)
		const left = new Vector3(-Snap.rectSize / 2, 0, 0)
		const right = new Vector3(Snap.rectSize / 2, 0, 0)
		const points: Vector3[] = []
		points.push(up)
		points.push(right)
		points.push(bottom)
		points.push(left)
		points.push(up)
		const geometry = new BufferGeometry().setFromPoints(points)

		this.quadrantMarker = new Line(geometry, this.material)
		this._markerScene.add(this.quadrantMarker)
		this.quadrantMarker.visible = false
	}

	public showEndPointMarker(pos: Vector3, scale: number) {
		this.endPointMarker.position.set(pos.x, pos.y, pos.z)
		this.endPointMarker.scale.set(scale, scale, scale)
		this.endPointMarker.visible = true;
		this._markerScene.visible = true;
	}

	public showNearestMarker(pos: Vector3, scale: number) {
		this.nearestMarker.position.set(pos.x, pos.y, pos.z)
		this.nearestMarker.scale.set(scale, scale, scale)
		this.nearestMarker.visible = true;
		this._markerScene.visible = true;
	}

	public showCenterMarker(pos: Vector3, scale: number) {
		this.centerMarker.position.set(pos.x, pos.y, pos.z)
		this.centerMarker.scale.set(scale, scale, scale)
		this.centerMarker.visible = true;
		this._markerScene.visible = true;
	}

	public showNodeMarker(pos: Vector3, scale: number) {
		this.nodeMarker.position.set(pos.x, pos.y, pos.z)
		this.nodeMarker.scale.set(scale, scale, scale)
		this.centerMarker.position.set(pos.x, pos.y, pos.z)
		this.centerMarker.scale.set(scale / 3, scale / 3, scale / 3)

		this.nodeMarker.visible = true
		this.centerMarker.visible = true
		this._markerScene.visible = true
	}

	public showIntersectionMarker(pos: Vector3, scale: number) {
		this.nodeMarker.position.set(pos.x, pos.y, pos.z)
		this.nodeMarker.scale.set(scale, scale, scale)
		this.nodeMarker.visible = true
		this._markerScene.visible = true
	}

	public showMidPointMarker(pos: Vector3, scale: number) {
		this.midPointMarker.position.set(pos.x, pos.y, pos.z)
		this.midPointMarker.scale.set(scale, scale, scale)
		this.midPointMarker.visible = true;
		this._markerScene.visible = true;
	}

	public showQuadrantMarker(pos: Vector3, scale: number) {
		this.quadrantMarker.position.set(pos.x, pos.y, pos.z)
		this.quadrantMarker.scale.set(scale, scale, scale)
		this.quadrantMarker.visible = true;
		this._markerScene.visible = true;
	}

	public hideAllMarker() {
		this.endPointMarker.visible = false;
		this.nearestMarker.visible = false;
		this.centerMarker.visible = false;
		this.nodeMarker.visible = false;
		this.midPointMarker.visible = false;
		this.quadrantMarker.visible = false;
		this._markerScene.visible = false;
	}
	public showMarker(event) {
		if (!Snap.m_snapOn) return
		let worldPos = screen2wcs(event, this.viewer.camera)
		let ids = this.viewer.selector.getEntityIds(event.offsetX, event.offsetY, 5)
		const nearestPoints: any[] = []
		const snappoints: any[] = []
		const sgWorldPos = new sg.Point2d(worldPos.x + globalDxf.wcsOff.x, worldPos.y + globalDxf.wcsOff.y)
		let camera = this.viewer.camera
		const pixelSize = (camera.right - camera.left) / this.viewer.canvas.getBoundingClientRect().width / camera.zoom;
		const handleWidth = 20
		const rectSize = pixelSize * handleWidth
		if (!this.viewer.transaction)
			return
		const gp = this.viewer.transaction.gpObj
		const bSnapEnd = Snap.snapModes & OsnapModeFlag.kOsModeEnd
		const bSnapMid = Snap.snapModes & OsnapModeFlag.kOsModeMid
		const bSnapCen = Snap.snapModes & OsnapModeFlag.kOsModeCen
		const bSnapNode = Snap.snapModes & OsnapModeFlag.kOsModeNode
		const bSnapQuad = Snap.snapModes & OsnapModeFlag.kOsModeQuad
		const bSnapIntersec = Snap.snapModes & OsnapModeFlag.kOsModeIntersec
		const bSnapIns = Snap.snapModes & OsnapModeFlag.kOsModeIns
		const bSnapPerp = Snap.snapModes & OsnapModeFlag.kOsModePerp
		const bSnapTan = Snap.snapModes & OsnapModeFlag.kOsModeTan
		const bSnapNear = Snap.snapModes & OsnapModeFlag.kOsModeNear
		const bSnapApint = Snap.snapModes & OsnapModeFlag.kOsModeApint
		const bSnapPar = Snap.snapModes & OsnapModeFlag.kOsModePar
		const bSnapStart = Snap.snapModes & OsnapModeFlag.kOsModeStart

		for (const id of ids) {
			let {entityId,blockId}=splitBlockId(id)
			let entity
			if(blockId) {
				entity=sg.copyInsertBaseObj(dxfZsObj.drawingId,id)
			}else {
				const obj=gp.getPBaseObjById(id)
				if(!obj)
					continue
				entity = obj.copyNewOne()
			}

			const type = entity.getObjType()

			if (type === 'SGObjCurve') {
				const sgCurve = sg.SGObjTool.convertToCurve(entity)
				const curveType: string = sgCurve.getCurveType()
				if (curveType === 'LineSegment2d') {
					const selectPoints = sgCurve.selectPoints()
					const n = selectPoints.size()
					for (let i = 0; i < n; i++) {
						const typedPoint = selectPoints.get(i)
						const pt = typedPoint.getPoint()
						const type = typedPoint.getString()
						const point3d = new Vector3(pt.x(), pt.y(), 0)

						if (bSnapEnd &&(type === 'startPoint' || type === 'endPoint')) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
						} else if (bSnapMid && type === 'middlePoint') {
							snappoints.push([point3d, OsnapModeFlag.kOsModeMid])
						}
					}
					if (bSnapNear) {
						const sgrealCurve = sgCurve.getCurve()
						const sgLineSegment2d = sg.Geometry2dUtils.convertToLineSegment2d(sgrealCurve)
						const sgNearestPoint = sgLineSegment2d.getNearstPoint(sgWorldPos)
						const nearestPoint = new Vector3(sgNearestPoint.x(), sgNearestPoint.y(), 0)
						nearestPoints.push(nearestPoint)
					}

				} else if (curveType === 'Arc2d') {
					const selectPoints = sgCurve.selectPoints()
					const n = selectPoints.size()
					for (let i = 0; i < n; i++) {
						const typedPoint = selectPoints.get(i)
						const pt = typedPoint.getPoint()
						const type = typedPoint.getString()
						const point3d = new Vector3(pt.x(), pt.y(), 0)

						if (bSnapEnd && (type === 'startPoint' || type === 'endPoint')) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
						} else if (bSnapMid && type === 'middlePoint') {
							snappoints.push([point3d, OsnapModeFlag.kOsModeMid])
						} else if (bSnapCen && type === 'center') {
							snappoints.push([point3d, OsnapModeFlag.kOsModeCen])
						}
					}
					if (bSnapNear) {
						const arc = sgCurve.getCurve()
						const sgNearestPoint = arc.getNearstPoint(sgWorldPos)
						const nearestPoint = new Vector3(sgNearestPoint.x(), sgNearestPoint.y(), 0)
						nearestPoints.push(nearestPoint)
					}
				} else if (curveType === 'Circle2d' || curveType === 'Ellipse2d') {
					const selectPoints = sgCurve.selectPoints()
					const n = selectPoints.size()
					for (let i = 0; i < n; i++) {
						const typedPoint = selectPoints.get(i)
						const pt = typedPoint.getPoint()
						const type = typedPoint.getString()
						const point3d = new Vector3(pt.x(), pt.y(), 0)

						if (bSnapCen && type === 'center') {
							snappoints.push([point3d, OsnapModeFlag.kOsModeCen])
						} else if (bSnapQuad && (type === 'zeroPoint' || type === 'quaterPoint' || type === 'halfPoint' || type === 'threeQuatersPoint')) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeQuad])
						}
					}
					if (bSnapNear) {
						const circle = sgCurve.getCurve()
						const sgNearestPoint = circle.getFootPoint(sgWorldPos)
						const nearestPoint = new Vector3(sgNearestPoint.x(), sgNearestPoint.y(), 0)
						nearestPoints.push(nearestPoint)
					}
				} else if (curveType === 'BSpline2d') {
					const selectPoints = sgCurve.selectPoints()
					const n = selectPoints.size()
					for (let i = 0; i < n; i++) {
						const typedPoint = selectPoints.get(i)
						const pt = typedPoint.getPoint()
						const type = typedPoint.getString()
						const point3d = new Vector3(pt.x(), pt.y(), 0)

						if (bSnapEnd && type === 'startPoint') {
							snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
						} else if (bSnapMid && type === 'middlePoint') {
							snappoints.push([point3d, OsnapModeFlag.kOsModeMid])
						}
					}

				} else if (curveType === 'EllipseArc2d') {
					const selectPoints = sgCurve.selectPoints()
					const n = selectPoints.size()
					for (let i = 0; i < n; i++) {
						const typedPoint = selectPoints.get(i)
						const pt = typedPoint.getPoint()
						const type = typedPoint.getString()
						const point3d = new Vector3(pt.x(), pt.y(), 0)

						if (bSnapEnd && (type === 'startPoint' || type === 'endPoint')) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
						} else if (bSnapMid && type === 'middlePoint') {
							snappoints.push([point3d, OsnapModeFlag.kOsModeMid])
						} else if (bSnapCen && type === 'center') {
							snappoints.push([point3d, OsnapModeFlag.kOsModeCen])
						}
					}

				}
			} else if (type === 'SGObjAlignDim') {
				const sgAlignDim = sg.SGObjTool.convertToAlignDim(entity)
				const selectPoints = sgAlignDim.selectPoints()
				const n = selectPoints.size()
				for (let i = 0; i < n; i++) {
					const typedPoint = selectPoints.get(i)
					const pt = typedPoint.getPoint()
					const type = typedPoint.getString()
					const point3d = new Vector3(pt.x(), pt.y(), 0)

					if (bSnapEnd && (type === 'firstLineStartPoint' || type === 'secondLineStartPoint' || type === 'firstLineEndPoint' || type === 'secondLineEndPoint')) {
						snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
					} else if (type === 'textPos') {
						if (bSnapIns) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeIns])
						}
						if (bSnapNode) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeNode])
						}

					}
				}

			} else if (type === 'SGObjLinearDim') {//rotateddim
				const sgLinearDim = sg.SGObjTool.convertToLinearDim(entity)
				const selectPoints = sgLinearDim.selectPoints()
				const n = selectPoints.size()
				for (let i = 0; i < n; i++) {
					const typedPoint = selectPoints.get(i)
					const pt = typedPoint.getPoint()
					const type = typedPoint.getString()
					const point3d = new Vector3(pt.x(), pt.y(), 0)

					if (bSnapEnd && (type === 'firstLineStartPoint' || type === 'secondLineStartPoint' || type === 'firstLineEndPoint' || type === 'secondLineEndPoint')) {
						snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
					} else if (type === 'textPos') {
						if (bSnapIns) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeIns])
						}
						if (bSnapNode) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeNode])
						}
					}
				}
			} else if (type === 'SGObjRadialDim') {
				const sgRadialDim = sg.SGObjTool.convertToRadialDim(entity)
				const selectPoints = sgRadialDim.selectPoints()
				const n = selectPoints.size()
				for (let i = 0; i < n; i++) {
					const typedPoint = selectPoints.get(i)
					const pt = typedPoint.getPoint()
					const type = typedPoint.getString()
					const point3d = new Vector3(pt.x(), pt.y(), 0)

					if (bSnapEnd && type === 'definitionPoint') { //Chord Point
						snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
					} else if (type === 'dimPos') { //center
						//snappoints.push([point3d, SnapType.NODE])
					} else if (type === 'textPos') { //Text Position

					}
				}

			} else if (type === 'SGObjAngular2LDim') {
				const sgAngular2LDim = sg.SGObjTool.convertToAngular2LDim(entity)
				const selectPoints = sgAngular2LDim.selectPoints()
				const n = selectPoints.size()
				for (let i = 0; i < n; i++) {
					const typedPoint = selectPoints.get(i)
					const pt = typedPoint.getPoint()
					const type = typedPoint.getString()
					const point3d = new Vector3(pt.x(), pt.y(), 0)

					if (bSnapNode && (type === 'arcPoint' || type === 'textPos')) { //Arc Point
						snappoints.push([point3d, OsnapModeFlag.kOsModeNode])
					}
				}

			} else if (type === 'SGObjDiametricDim') {
				const sgDiametricDim = sg.SGObjTool.convertToDiametricDim(entity)
				const selectPoints = sgDiametricDim.selectPoints()
				const n = selectPoints.size()
				for (let i = 0; i < n; i++) {
					const typedPoint = selectPoints.get(i)
					const pt = typedPoint.getPoint()
					const type = typedPoint.getString()
					const point3d = new Vector3(pt.x(), pt.y(), 0)

					if (type === 'definitionPoint' || type === 'dimPos') { //Chord Point //Far chord Point
						if (bSnapEnd) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
						}
						if (bSnapIntersec) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeIntersec])
						}
						if (bSnapNode) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeNode])
						}

					} else if (type === 'textPos') {
						if (bSnapNode) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeNode])
						}
					}
				}
			} else if (type === 'SGObjOrdinateDim') {


			} else if (type === 'SGObjLeader') {


			} else if (type === 'SGObjText') {
				const text = sg.SGObjTool.convertToText(entity)
				const selectPoints = text.selectPoints()
				const n = selectPoints.size()
				for (let i = 0; i < n; i++) {
					const typedPoint = selectPoints.get(i)
					const pt = typedPoint.getPoint()
					const type = typedPoint.getString()
					const point3d = new Vector3(pt.x(), pt.y(), 0)

					if (type === 'insertPoint') {
						//endpoints.push(point3d)
						if (bSnapIns) {
							snappoints.push([point3d, OsnapModeFlag.kOsModeIns])
						}
					}
				}
			} else if (type === 'SGObjComBinCurve') {
				const polyline = sg.SGObjTool.convertToComBinCurve(entity)
				const selectPoints = polyline.selectPoints()
				const n = selectPoints.size()
				//let prevPoint = new Vector3()
				for (let i = 0; i < n; i++) {
					const typedPoint = selectPoints.get(i)
					const pt = typedPoint.getPoint()
					const pointtype = typedPoint.getString()
					const point3d = new Vector3(pt.x(), pt.y(), 0)
					//console.log(pointtype)
					if (bSnapEnd && pointtype === 'vertex') {
						snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
					}
					// if (bSnapMid && i > 0) {
					// 	const midPoint = new Vector3((prevPoint.x + point3d.x) / 2, (prevPoint.y + point3d.y) / 2, 0)
					// 	snappoints.push([midPoint, OsnapModeFlag.kOsModeMid])
					// }
					// prevPoint = point3d
					// else if(bSnapEnd && pointtype === 'endPoint'){
					// 	snappoints.push([point3d, OsnapModeFlag.kOsModeEnd])
					// }else if(bSnapMid && pointtype === 'midPoint'){
					// 	snappoints.push([point3d, OsnapModeFlag.kOsModeMid])
					// }
				}
				if (bSnapNear) {
					const polylineCurve = polyline.getCurve()
					const sgNearestPoint = polylineCurve.getNearstPoint(sgWorldPos)
					const nearestPoint = new Vector3(sgNearestPoint.x(), sgNearestPoint.y(), 0)
					nearestPoints.push(nearestPoint)
				}
			}
		}
		const n = ids.length
		if (bSnapIntersec) {
			for (let i = 0; i < n; i++) {
				const id1 = ids[i]
				let {entityId,blockId}=splitBlockId(id1)
				let entity1
				if(blockId) {
					entity1=sg.copyInsertBaseObj(dxfZsObj.drawingId,id1)
				}else {
					const obj=gp.getPBaseObjById(id1)
					if(!obj)
						continue
					entity1 = obj.copyNewOne()
				}

				if(!entity1){
					console.log(`未获取到对象 id=${id1}`)
					continue
				}
				const type1 = entity1.getObjType()

				for (let j = i + 1; j < n; j++) {
					const id2 = ids[j]
					let entity2
					if(blockId) {
						entity2=sg.copyInsertBaseObj(dxfZsObj.drawingId,id2)
					}else {
						const obj=gp.getPBaseObjById(id2)
						if(!obj)
							continue
						entity2 = obj.copyNewOne()
					}
					if(!entity2){
						console.log(`未获取到对象 id=${id2}`)
						continue
					}
					const type2 = entity2.getObjType()
					let points = undefined
					if (type1 === 'SGObjCurve' && type2 === 'SGObjCurve') {
						const sgCurve1 = sg.SGObjTool.convertToCurve(entity1)
						const sgCurve2 = sg.SGObjTool.convertToCurve(entity2)
						const curve1 = sgCurve1.getCurve()
						const curve2 = sgCurve2.getCurve()
						points = sg.intersectPoints_CC(curve1, curve2)
					} else if (type1 === 'SGObjCurve' && type2 === 'SGObjComBinCurve') {
						const sgCurve1 = sg.SGObjTool.convertToCurve(entity1)
						const sgCurve2 = sg.SGObjTool.convertToComBinCurve(entity2)
						const curve1 = sgCurve1.getCurve()
						const curve2 = sgCurve2.getCurve()
						points = sg.intersectPoints_CA(curve1, curve2)

					} else if (type1 === 'SGObjComBinCurve' && type2 === 'SGObjCurve') {
						const sgCurve1 = sg.SGObjTool.convertToComBinCurve(entity1)
						const sgCurve2 = sg.SGObjTool.convertToCurve(entity2)
						const curve1 = sgCurve1.getCurve()
						const curve2 = sgCurve2.getCurve()
						points = sg.intersectPoints_CA(curve2, curve1)

					} else if (type1 === 'SGObjComBinCurve' && type2 === 'SGObjComBinCurve') {
						const sgCurve1 = sg.SGObjTool.convertToComBinCurve(entity1)
						const sgCurve2 = sg.SGObjTool.convertToComBinCurve(entity2)
						const curve1 = sgCurve1.getCurve()
						const curve2 = sgCurve2.getCurve()
						points = sg.intersectPoints_AA(curve1, curve2)
					}
					if (points) {
						const n = points.size()
						for (let i = 0; i < n; i++) {
							const pt = points.get(i)
							const point3d = new Vector3(pt.x(), pt.y(), 0)
							snappoints.push([point3d, OsnapModeFlag.kOsModeIntersec])
						}
					}else{
						console.log(points)
					}

				}
			}
		}

		const wcs = new Vector3().addVectors(worldPos, new Vector3(globalDxf.wcsOff.x, globalDxf.wcsOff.y, 0))
		snappoints.forEach((v) => {
			v[0].subVectors(v[0], wcs)
		})
		snappoints.sort((a, b) => {
			return a[0].lengthSq() - b[0].lengthSq()
		})

		nearestPoints.forEach((v) => {
			v.subVectors(v, wcs)
		})
		nearestPoints.sort((a, b) => {
			return a.lengthSq() - b.lengthSq()
		})
		let snapPoint = undefined;
		let snapType = OsnapModeFlag.kOsModeNone;
		if (nearestPoints.length > 0 && snappoints.length > 0) {
			const snappoint = snappoints[0]
			if (snappoint[0].lengthSq() < rectSize * rectSize) {
				snapType = snappoint[1]
				snapPoint = snappoint[0]
			} else {
				snapType = OsnapModeFlag.kOsModeNear
				snapPoint = nearestPoints[0]
			}
		} else if (nearestPoints.length > 0) {
			snapPoint = nearestPoints[0]
			snapType = OsnapModeFlag.kOsModeNear
		} else if (snappoints.length > 0) {
			const snappoint = snappoints[0]
			snapType = snappoint[1]
			snapPoint = snappoint[0]
		}

		// const wasmline = sg.Geometry2dUtils.convertToLineSegment2d(curve)
		// const point = wasmline.startPoint()
		this.hideAllMarker()
		if (!snapPoint) {
			return undefined
		}
		const point3d = new Vector3().addVectors(snapPoint, worldPos)
		if (snapType === OsnapModeFlag.kOsModeEnd) {
			this.showEndPointMarker(point3d, rectSize)
		} else if (snapType === OsnapModeFlag.kOsModeNear) {
			this.showNearestMarker(point3d, rectSize)
		} else if (snapType === OsnapModeFlag.kOsModeCen) {
			this.showCenterMarker(point3d, rectSize)
		} else if (snapType === OsnapModeFlag.kOsModeNode) {
			this.showNodeMarker(point3d, rectSize)
		} else if (snapType === OsnapModeFlag.kOsModeMid) {
			this.showMidPointMarker(point3d, rectSize)
		} else if (snapType === OsnapModeFlag.kOsModeQuad) {
			this.showQuadrantMarker(point3d, rectSize)
		} else if (snapType === OsnapModeFlag.kOsModeIntersec) {
			this.showIntersectionMarker(point3d, rectSize)
		}
		return point3d
	}
}