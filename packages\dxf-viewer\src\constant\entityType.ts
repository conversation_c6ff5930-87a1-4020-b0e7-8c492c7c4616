export enum BatchEntityType{
    POINTS= 0,
    LINES= 1,
    INDEXED_LINES= 2,
    TRIANGLES= 3,
    INDEXED_TRIANGLES= 4,
    BLOCK_INSTANCE= 5,
    /** Shaped point instances. */
    POINT_INSTANCE= 6,
    DASH_LINES = 7,
    DASH_SEGMENTS=8,
}


/** Internal entity representation. DXF features are decomposed into these simpler entities. Whole
 * entity always shares single material.
 */
export enum EntityType{
    POINTS= 0,
    /** Each vertices pair defines a segment. */
    LINE_SEGMENTS= 1,//线段
    POLYLINE= 2,//
    DASHLINE= 3,//无序的虚线类型
    TRIANGLES= 4
}