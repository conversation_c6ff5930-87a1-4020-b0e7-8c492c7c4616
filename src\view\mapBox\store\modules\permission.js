import { defineStore } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';

import { getMenuList, getMenuList2, getUserInfo } from '@/api/permission';
import router, { fixedRouterList, homepageRouterList } from '@/router';
import routesConfig from '@/router/dynamicRoutesConfig';
import { store } from '@/store';
import { removeToken, setToken } from '@/utils/auth';
import { transformObjectToRoute } from '@/utils/route';

import { useUserStore } from './user';
import whiteList from './whiteList';

export const usePermissionStore = defineStore('permission', {
  state: () => ({
    whiteListRouters: ['/login'],
    whiteListRouterNames: ['403Page', ...whiteList], // 白名单路由名称
    routers: [],
    menuRouters: null,
    removeRoutes: [],
    asyncRoutes: [],
    flatData: [], // 扁平化数据->用于权限判断
  }),
  actions: {
    async initRoutes() {
      // const accessedRouters = this.asyncRoutes;
      // 在菜单展示全部路由
      // this.routers = [...homepageRouterList, ...accessedRouters, ...fixedRouterList];
      // 在菜单只展示动态路由和首页
      // this.routers = [...homepageRouterList, ...accessedRouters];
      // 在菜单只展示动态路由
      // this.routers = [...accessedRouters];
    },
    async loadBaseInfo() {
      const userInfo = await getUserInfo();
      const { userId, userName } = userInfo.data;
      // 设置用户信息
      useUserStore().setUserInfo({
        name: userName,
      });
      return userId;
    },
    // 通过接口调用动态路由
    async initMenuList(userId = '1148150057218256896') {
      const params = {
        userId,
      };
      // 获取所有有权限的路由
      const asyncMenuData = (await getMenuList2(params)).data.componentList;
      this.flatData = asyncMenuData;
      // 合并白名单
      this.whiteListRouterNames.forEach((item) => {
        asyncMenuData.push({ component: item });
      });
      const filteredMenuData = filterMenuData(this.routers, asyncMenuData); // isFirst = true
      this.menuRouters = filteredMenuData;
      this.routers = filteredMenuData;
      this.asyncRoutes = asyncMenuData;
    },
    async generateRoutes(permission = true) {
      try {
        const newRoutes = Object.assign(routesConfig);
        const userId = await this.loadBaseInfo();
        if (permission) {
          this.routers = transformObjectToRoute(newRoutes);
          await this.initMenuList(userId);
        } else {
          const allRouters = transformObjectToRoute(newRoutes);
          this.routers = allRouters;
          this.menuRouters = allRouters;
          this.asyncRoutes = allRouters;
        }
        return this.routers;
      } catch (error) {
        MessagePlugin.error({
          content: '获取用户信息失败，请联系管理员!',
          duration: 2000,
        });

        // 即使接口异常,也继续执行默认逻辑
        const newRoutes = Object.assign(routesConfig);
        const allRouters = transformObjectToRoute(newRoutes);
        this.routers = allRouters;
        this.menuRouters = allRouters;
        this.asyncRoutes = allRouters;
        return this.routers;
      }
    },
    async buildAsyncRoutes() {
      try {
        // 发起菜单权限请求 获取菜单列表
        const asyncRoutes = (await getMenuList()).list;
        this.asyncRoutes = transformObjectToRoute(asyncRoutes);
        await this.initRoutes();
        return this.asyncRoutes;
      } catch (error) {
        throw new Error("Can't build routes");
      }
    },
    async restoreRoutes() {
      // 不需要在此额外调用initRoutes更新侧边导肮内容，在登录后asyncRoutes为空会调用
      this.asyncRoutes.forEach((item) => {
        if (item.name) {
          router.removeRoute(item.name);
        }
      });
      this.asyncRoutes = [];
    },
  },
});

export function getPermissionStore() {
  return usePermissionStore(store);
}

function filterMenuData(menuData, fliterData, isFirst = false) {
  const fliterNames = fliterData.map((item) => item.component.toUpperCase());

  function filterItems(items, isFirstLevel) {
    return items.reduce((acc, item) => {
      if (fliterNames.includes(item.name.toUpperCase())) {
        acc.push(item);
      } else if (!isFirstLevel && item.children) {
        const filteredChildren = filterItems(item.children, false);
        if (filteredChildren.length > 0) {
          acc.push({
            ...item,
            children: filteredChildren,
          });
        }
      }
      return acc;
    }, []);
  }
  return filterItems(menuData, isFirst);
}
