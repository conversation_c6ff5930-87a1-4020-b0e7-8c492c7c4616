import { PubSub } from "emitter";
import { Dxf<PERSON>ie<PERSON> } from "../viewer";
import {
	Polyline,  Circle, Rect, RegularPolygon, RevCloud, Arc, Ellipse, Bspline,EllipticalArc,
	AlignedDimension, RotatedDimension, Text, Block, Line, Mirror,Extend,Trim,Offset,Fillet,
	Move,
} from '../../../dxfdraw'
import {Fence,RectangularRange,PolygonRange,Scale,Producepicture} from '../../../sldraw'
import { DrawState, globalDxf, pickupInfo } from "../global";
import { GraphicType } from "./constant";
import { ItemBoundingBox } from "./ItemBoundingBox";

export class GraphicContorls {
  public viewer: DxfViewer;
  public current: any;
  public itemboundingbox: ItemBoundingBox;
  constructor(viewer: DxfViewer) {
    this.viewer = viewer;
    this.itemboundingbox = new ItemBoundingBox(viewer);
    PubSub.default.sub("executeCommand", this.draw.bind(this));
  }

  public draw(type: GraphicType) {
    globalDxf.drawstate = DrawState.Drawing;
    globalDxf.operation = type;
    globalDxf.isdraw = true;
    this.current = undefined;
    console.log(type);
    switch (type) {
      case "Mirror":
        new Mirror(this.viewer, pickupInfo.pickupIdlist);
        globalDxf.isdraw = false;
        break;
      case "Line":
        new Line(this.viewer);
        break;
      case "Circle":
        new Circle(this.viewer);
        break;
      case "Extend":
        new Extend(this.viewer, pickupInfo.pickupIdlist);
        globalDxf.isdraw = false;
        break;
      case "Trim":
        new Trim(this.viewer, pickupInfo.pickupIdlist);
        globalDxf.isdraw = false;
        break;
      case "Offset":
        new Offset(this.viewer, pickupInfo.pickupIdlist);
        globalDxf.isdraw = false;
        break;
      case "Fillet":
        new Fillet(this.viewer, pickupInfo.pickupIdlist);
        globalDxf.isdraw = false;
        break;
      case "Polyline":
        // this.current = new Polyline()
        new Polyline(this.viewer);
        break;

	  case 'Rect':
	  	new Rect(this.viewer)
	  	break;
	  case 'Polygon':
	  	// this.current = new RegularPolygon()
	  	new RegularPolygon(this.viewer)
	  	break;
	  case 'RevCloud':
	  	new RevCloud(this.viewer)
	  	break;
	  case 'Arc':
	  	new Arc(this.viewer)
	  	break;
	  case 'Ellipse':
	  	// this.current = new Ellipse()
	  	new Ellipse(this.viewer)//测试绘制围栏
	  	break;
	  case 'Bspline':
	  	new Bspline(this.viewer)
	  	break;
	  case 'EllipticalArc':
	  	new EllipticalArc(this.viewer)
	  	break;
	  case 'Text':
	  	new Text(this.viewer)
	  	break;
	  case 'AlignedDimension':
	  	this.current = new AlignedDimension()
	  	break;
	  case 'RotatedDimension':
	  	this.current = new RotatedDimension()
	  	break;
	  case 'Fence'://算量绘制围栏
	  	new Fence(this.viewer)
	  	break;
	  case 'RectangularRange'://算量识别图元 绘制矩形范围框
	  	new RectangularRange(this.viewer)
	  	break;
	  case 'PolygonRange'://算量识别图元 绘制矩形范围框
	  	new PolygonRange(this.viewer)
	  	break;
	  case 'Scale'://算量比例尺
	  	new Scale(this.viewer)
	  	break;
	  case 'Producepicture'://算量出图
	  	new Producepicture(this.viewer)
	  	break;
	  case 'Block':
	  	this.current = new Block()
	  	this.viewer.scene.add(this.current['Block'])
	  	break;
	  case 'move':
		new Move(this.viewer)
		break;
	  default:
	  	this.setNoDrawState()
	  	return
		}
	}

	public showBoundingBox(obj) {//handleMouseMovePan
		this.itemboundingbox.zoomto(obj)
	};

  public setNoDrawState() {
    globalDxf.isdraw = false;
    globalDxf.operation = GraphicType.None;
  }
}
