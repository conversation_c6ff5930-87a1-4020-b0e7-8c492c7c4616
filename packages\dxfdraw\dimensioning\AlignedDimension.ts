import * as THREE from "three"
import {ElementStyle} from '../ElementStyle'
import {dimensionCount,triangleCount,charactersCount} from 'sgdraw/util/calculation'


export class AlignedDimension extends ElementStyle {
    public count:number=0
    public iscomplete:boolean=false
    public gp:any
    public viewer
    private xLine1Point = new THREE.Vector3();
    private xLine2Point = new THREE.Vector3();
    private dimLinePoint = new THREE.Vector3();

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super();
        this.viewer=viewer
    }
    setV(v:THREE.Vector3){
        this.count++
        //this.vPlist.push(v)
        switch ( this.count) {
			case 1:
                this.gp = new sg.SGObjCurve()
                this.xLine1Point = v;
                break;
            case 2:
                this.xLine2Point = v;
                this.dimLinePoint = v;
                this.gp = new sg.SGObjAlignDim()
                this.update()
                break;
            case 3:
                this.dimLinePoint = v;
                this.iscomplete=true
                this.viewer.transientSceneWasm.clear()
                break;
			default:
                break; 
		}
    }
    getmove(v:THREE.Vector3){
        // let r=this.vPlist[0].distanceTo(v);
        switch ( this.count) {
			case 1:
                this.xLine2Point = v;
                this.updateLine()
				break;
			case 2:
                this.dimLinePoint = v;
                this.update()
                break;
            case 3:
			default:
                break;
		}

    }
    complete(){
        if(this.count<3) return
        this.update()
    }
    
    private update() {
        const startPoint = new sg.Point2d(this.xLine1Point.x, this.xLine1Point.y)
        this.gp.setStartPoint(startPoint)
        const endPoint = new sg.Point2d(this.xLine2Point.x, this.xLine2Point.y)
        this.gp.setEndPoint(endPoint)
        const dimPoint = new sg.Point2d(this.dimLinePoint.x, this.dimLinePoint.y)
        this.gp.setDimensionPos(dimPoint)

        this.viewer.transientSceneWasm.clear()
        this.viewer.transientSceneWasm.draw(this.gp)
    }

    private updateLine() {
        const startPoint = new sg.Point2d(this.xLine1Point.x, this.xLine1Point.y)
        const endPoint = new sg.Point2d(this.xLine2Point.x, this.xLine2Point.y)
        const line = new sg.LineSegment2d(startPoint, endPoint)
        this.gp.setpCurve(line)
        this.viewer.transientSceneWasm.clear()
        this.viewer.transientSceneWasm.draw(this.gp)
    }

    getisitCompleted(){
        return this.iscomplete
    }
}

// import * as THREE from "three"
// import {ElementStyle} from '../ElementStyle'
// import {dimensionCount,triangleCount,charactersCount} from 'sgdraw/util/calculation'


// export class AlignedDimension extends ElementStyle {
//     public vPlist:THREE.Vector3[]=[]
//     public AlignedDimension:any
//     public count:number=0
//     public iscomplete:boolean=false
//     public gp:any=null

//     constructor() {
//         // 调用父类的构造函数以初始化继承的属性
//         super();
//         const geometry = new THREE.BufferGeometry();
//         // 生成圆形顶点数据
//         const vertices:any = [];
//         geometry.setFromPoints(vertices);
//         // 材质设置为线条的基本材质
//         const material = new THREE.LineBasicMaterial({ color:this.color});
//         this.AlignedDimension = new THREE.Line(geometry, material);

        
//         // this.dim.set(p1,p2,p3)
//     }
//     setV(v:THREE.Vector3){
//         this.count++
//         this.vPlist.push(v)
//         switch ( this.count) {
// 			case 3:
//                 this.iscomplete=true
//                 this.gp=new sg.SGObjAlignDim() 
//                 this.gp.set(
//                     new sg.Point2d(this.vPlist[0].x, this.vPlist[0].y),
//                     new sg.Point2d(this.vPlist[1].x, this.vPlist[1].y),
//                     new sg.Point2d(this.vPlist[2].x, this.vPlist[2].y)
//                 )
                
// 			default:
//                 break; 
// 		}
//     }
//     getmove(v:THREE.Vector3){
//         if(!this.vPlist.length) return
//         // let r=this.vPlist[0].distanceTo(v);
//         if(!this.vPlist.length) return
//         switch ( this.count) {
// 			case 1:
//                 this.AlignedDimension.geometry=new THREE.BufferGeometry().setFromPoints([...this.vPlist,v]);
//                 this.AlignedDimension.material= new THREE.LineBasicMaterial({ color:this.color});
// 				break;
// 			case 2:
//                 let vertices= this.getvertices([...this.vPlist,v])
//                 this.AlignedDimension.geometry.setFromPoints(vertices)
//                 this.AlignedDimension.material= new THREE.LineBasicMaterial({ color:this.color});
//                 break;
//             case 3:
// 			default:
//                 break;
// 		}

//     }
//     getScene(){
//         if(this.vPlist.length<3) return
//         let vertices= this.getvertices(this.vPlist)
//         this.AlignedDimension.geometry.setFromPoints(vertices)
//         this.AlignedDimension.material= new THREE.LineBasicMaterial({ color:this.color});

        
//     }
    
//     getisitCompleted(){
//         return this.iscomplete
//     }
//     private getvertices(vList:any) {
//         let OBj=dimensionCount(...vList)
//         let pointA=vList[0]
//         let pointB=vList[1]
//         let pointC=new THREE.Vector3(OBj.D.x,OBj.D.y,vList[0].z);
//         let pointD=new THREE.Vector3(OBj.E.x,OBj.E.y,vList[0].z);
//         const vertices=[
//             pointA,pointC,pointC,pointD,pointD,pointB
//         ]
//         return vertices
//     }

// }