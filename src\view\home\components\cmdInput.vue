<template>
    <el-input style="height: 20px;background-color: beige;" ref="cmdInput" @keyup="handleKeyup" v-model="cmdstr"></el-input>
</template>
  
  <script setup>
  import { PubSub } from 'emitter'
  import {viewCmdInput} from 'dxf-viewer'
  const cmdstr=ref('')
  const cmdInput = ref()
  
  onMounted(() => {
    PubSub.default.sub('cmdInput', btnFocus)
  })

  const btnFocus=()=>{
    nextTick(()=>{
        cmdInput.value.focus(); // 聚焦
    })
  }
  const handleKeyup=(event)=>{
    if (event.key === ' ' || event.key === 'Spacebar' ||  event.key === 'Enter') {
        viewCmdInput.CmdInputValue=cmdstr.value
        PubSub.default.pub('cmdMessage', { code: '', msg:cmdstr.value })
        cmdstr.value=''
    }
  }

  
  </script>
  
  <style lang="scss" scoped>
  .console {
    width: 100%;
    height: 100% !important;
    resize: none;
    outline: none;
    padding: 0;
    white-space: pre;
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100% - 4px);
    background: rgb(var(--v-theme-console));
    color: rgb(var(--v-theme-on-console));
    padding-left: 10px;
    // padding: 10px;
    // margin: 10px;
    color: #fff;
    font-size: 16px;
  
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      background-color: var(--v-theme-console);
    }
  
    &:hover {
      &::-webkit-scrollbar-thumb {
        background-color: rgba($color: #fff, $alpha: .3);
      }
    }
  
    &::-webkit-scrollbar-thumb {
      cursor: pointer;
      background-color: rgba($color: #fff, $alpha: 0);
  
      &:hover {
        background-color: rgba($color: #fff, $alpha: .5);
      }
    }
  }
  
  
  .commandDisplay {
    background-color: #787878;
  }
  </style>
  <style>
  .el-textarea__inner {
    height: 100% !important;
    min-height: 60px !important;
  }
  </style>