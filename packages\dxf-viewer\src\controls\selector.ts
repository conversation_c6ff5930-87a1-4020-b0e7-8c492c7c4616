
import { globalDxf, pickupInfo, showProps,dxflayers} from 'dxf-viewer'
import { getBuffIds, splitBlockId, validIdsFromSet } from '../libs/utils'
import { emitter, emittevnets } from '../../../emitter'
import { buff2base64,debounce } from '../libs'
import { screen2wcs } from "./coordinateSystem";


export class Selector {
    public dxfViewer:any
    public frameSelectSwitch:boolean=true

    constructor(obj){
        this.dxfViewer=obj
    }
    public selectByBox(st,en,event:PointerEvent){
        if(!this.frameSelectSwitch) return  this.dxfViewer.OnSelectModelToImage()
        // this.wasmGetEntityId(st,en)
        // let entityId=this.pickIdsByBox(st,en)
        let entityId= this.queryMethodId(st,en)
        entityId= this.idLayerFilter(entityId) || []

        if(entityId.length) {
            let newObj:any={
                color:'',
                entityId,
            }
            if(typeof globalDxf.highlFilter === 'function') {
                newObj=globalDxf.highlFilter(entityId,'green')
            }
            let color=newObj.color || ''
            if(!newObj.entityId.length) return
            pickupInfo.isSelected=true
            this.pickIdsToRender(newObj.entityId,event,color)
            
        }else {
            pickupInfo.isSelected=false
            let keep=event.shiftKey||event.ctrlKey
            if(!keep) {
                this.dxfViewer.highlightScene.clear()
                pickupInfo.pickupIdlist=[]
            }
        }
        
        this.dxfViewer.Render()
        this.dxfViewer.OnSelectModelToImage()
    }
    public clearSelect(){
		this.dxfViewer.selectionScene.clear()
		this.dxfViewer.highlightScene.clear()
		pickupInfo.pickupIdlist=[]
	}
    public beforeSelect(event:PointerEvent){
		let keep=event.shiftKey||event.ctrlKey
		if(!keep) {
			this.clearSelect()
		}
	}
    public OnSelect(event:PointerEvent){
        this.beforeSelect(event)
        let entityId = this.getEntityId(event.offsetX, event.offsetY)
        pickupInfo.isSelected=entityId>0
        if(entityId > 0) {
            entityId= this.idLayerFilter([entityId])[0] || 0
            let newObj:any={
                color:'',
                entityId:[entityId],
            }
            if(typeof globalDxf.highlFilter === 'function') {
                newObj=globalDxf.highlFilter([entityId],'green')
            }
            let color=newObj.color || ''
            if(!newObj.entityId.length) return
            this.pickIdsToRender(newObj.entityId,event,color)
        }
        this.dxfViewer.Render()
        this.dxfViewer.OnSelectModelToImage()
    }
    /** 高亮状态
     * 1 
     * @returns 
     */
    public async onMouseMoveHighL(x, y){
        this.dxfViewer.highlightScene.clear()
        console.log(x,y,'onMouseMoveHighL');
        
        let renderId=this.getEntityId(x, y)
        console.log('renderId',renderId);
        
        if(renderId>0) {
            renderId= this.idLayerFilter([renderId])[0] || 0
        }
        let {entityId,blockId}=splitBlockId(renderId)
        globalDxf.blockId=blockId
        globalDxf.highlId=entityId
        if(entityId>0)  {
            
            let newObj:any={
                color:'',
                entityId:[renderId],
            }
            if(typeof globalDxf.highlFilter === 'function') {
                newObj=globalDxf.highlFilter([renderId],'green')
            }
            let color=newObj.color || ''
            if(!newObj.entityId.length) return
            
            await this.dxfViewer.highlightScene.AddObjById(this.dxfViewer.transaction.gpObj,newObj.entityId,{color} )
        }
        this.dxfViewer.Render()
        // this.testIdRender(x,y)
    }


    private pickIdsToRender(ids,event:PointerEvent,color=''){
        let isEdit=false
        if(event.shiftKey){
            pickupInfo.pickupIdlist.push(...ids)
            pickupInfo.pickupIdlist=Array.from(new Set(pickupInfo.pickupIdlist))
        }else if(event.ctrlKey){
            this.dxfViewer.selectionScene.clear()
            if(ids.length) {
                let arr=pickupInfo.pickupIdlist.filter(item => !ids.includes(item));
                pickupInfo.pickupIdlist=arr
            }
        }else{
            isEdit=true
            pickupInfo.pickupIdlist=ids
            //this.dxfViewer.linkEntityWithGrips(ids[0]);
        }
        console.log(pickupInfo.pickupIdlist,'pickupInfo.pickupIdlist');
        this.dxfViewer.selectionScene.addObjsByIds(this.dxfViewer.transaction.gpObj, pickupInfo.pickupIdlist,{color,isEdit})
    }


    private getEntityId(x, y, tolerance = 2){
        let ids=this.getEntityIds(x,y,tolerance)
        console.log(ids,'getEntityIds');
        
        return ids.length>0?ids[0]:0
    }
    public testIdRender(x,y){
        if(!showProps.isBackIdRender)return
        let {renderer,idRenderTarget,idScene,canvasWidth,canvasHeight,camera}=this.dxfViewer
        const tolerance = 50
        const startX = Math.max(x - tolerance, 0)
        const startY= Math.max((this.dxfViewer.canvasHeight-y)- tolerance, 0)
        const width = 2 * tolerance + 1//Math.min(2 * tolerance + 1, this.width - startX)
        const height = 2 * tolerance + 1//Math.min(2 * tolerance + 1, this.height - startY)
        const idBufferSize = (tolerance * 2 + 1) * (tolerance * 2 + 1)
        
        renderer.setRenderTarget(idRenderTarget)
        renderer.render( idScene, camera );
        let idBufferData = new Float32Array(idBufferSize * 4);
        renderer.readRenderTargetPixels(
            idRenderTarget,
            startX,
            startY,
            width,
            height,
            idBufferData
        );
        renderer.setRenderTarget(null)
        emitter.emit(emittevnets.pickpixi,{
            width,height,
            img:buff2base64({width,height,buff:idBufferData})
        })
    }

    private getEntityIds(x, y, tolerance = 2){
        const startX = Math.max(x - tolerance, 0)
        const startY= Math.max((this.dxfViewer.canvasHeight-y)- tolerance, 0)
        const width = 2 * tolerance + 1//Math.min(2 * tolerance + 1, this.width - startX)
        const height = 2 * tolerance + 1//Math.min(2 * tolerance + 1, this.height - startY)

        const idBufferSize = (tolerance * 2 + 1) * (tolerance * 2 + 1)
        let idBufferData = new Float32Array(idBufferSize * 4);

        this.dxfViewer.renderer.readRenderTargetPixels(
            this.dxfViewer.idRenderTarget,
            startX,
            startY,
            width,
            height,
            idBufferData
        );
        let ids:any = getBuffIds(idBufferData)
        return ids
    }

    private pickIdsByBox(st, en, tolerance = 2) {
        let positive=(en.x-st.x)>0//判断正选反选
        let min={
            x:Math.floor(Math.min(st.x,en.x)) ,
            y:Math.floor(Math.min(st.y,en.y))
        }
        let max={
            x:Math.ceil(Math.max(st.x,en.x)),
            y:Math.ceil(Math.max(st.y,en.y))
        }
        let size={
            x:max.x-min.x,
            y:max.y-min.y
        }
        let tol=tolerance|2
        if(!positive){//正选+边框，反选-边框
            tol=0
        }
        let wid=size.x+tol*2
        let hei=size.y+tol*2
        const idBufferSize = wid * hei;
        let idBuff = new Float32Array(idBufferSize * 4);
        this.dxfViewer.renderer.readRenderTargetPixels(
            this.dxfViewer.idRenderTarget,
            min.x-tol,
            this.dxfViewer.canvasHeight - (max.y+tol),
            wid,
            hei,
            idBuff
        );
        let ins:Set<number> = new Set();//矩形内ids
        let outs:Set<number> = new Set();//矩形外圈ids
        let ins4:Set<number[]> = new Set();
        let outs4:Set<number[]> = new Set();
        for(let i=tol;i<size.x;i++){
            for(let j=tol;j<size.y;j++){
                let idx=i*size.y+j
                let [r,g,b,a]=idBuff.slice(idx*4,idx*4+4)
                ins4.add([r,g,b,a])
            }
        }
        ins=validIdsFromSet(ins4)
        if(positive){
            for(let i=0;i<wid;i++){
                for(let j=0;j<hei;j++){
                    if(i<tol||i>wid-tol||j<tol||j>hei-tol){
                        let idx=j*wid+i
                        let [r,g,b,a]=idBuff.slice(idx*4,idx*4+4)
                        outs4.add([r,g,b,a])
                    }
                }
            }
            outs=validIdsFromSet(outs4)
            for(let outId of outs){
                ins.delete(outId)
            }
        }
        let ids=Array.from(ins)
        return ids
    }
    private idLayerFilter(ids){
        if(!ids.length || ids[0]==0) return
        const layers=dxflayers.layers
        let hidelayers=layers.filter(item=>!item.isVisible).map(item=>item.name)
        let filterids:any=[]
        let gp=this.dxfViewer.transaction.gpObj
        ids.forEach(id=>{
            let {entityId,blockId}=splitBlockId(id)
            let entity
            if(blockId) {
                entity= gp.getInsertBaseObjById(blockId,entityId)
            }else {
                entity= gp.getPBaseObjById(id)
            }
            let layername=entity?.getLayer()
            if(!hidelayers.includes(layername)) {
                filterids.push(id)
            }
        })
        return filterids
    }
    private queryMethodId(st,en) {
        const zoom=this.dxfViewer.camera.zoom
        const width = this.dxfViewer.canvas.clientWidth;
        const height = this.dxfViewer.canvas.clientHeight;
        let {_v1,_v2}=this.dxfViewer.viewControl.selectBoxHelp
        let l1=_v1.distanceTo(_v2);
        if(l1 / zoom >width*2) {
            return this.wasmGetEntityId(_v1,_v2)
        }else {
            return this.pickIdsByBox(st,en)
        }
    }

    private wasmGetEntityId(_v1,_v2){
        // console.log('执行了');
        let positive=(_v2.x-_v1.x)>0//判断正选反选
        let {x,y}=globalDxf.wcsOff
        let p1=new sg.Point2d(_v1.x+x,_v1.y+y)
        let p2=new sg.Point2d(_v2.x+x,_v2.y+y)
        let gp=this.dxfViewer.transaction.gpObj
        let arr2=new sg.vector_int()//排除的id
        if(pickupInfo.selectedID.length) {//已识别图元ID
        pickupInfo.selectedID.forEach(item=>arr2.push_back(item))
        }
        let ids=sg.getRectGroupIds(gp,p1,p2,arr2,positive)
        let size=ids.size()
        let lsarrIds:any=[]
        for (let i=0;i<size;i++) {
            lsarrIds.push(ids.get(i))
        }
        return lsarrIds
    }

}
