<template>
  <div class="home">

    <Head></Head>
    <TopToolbar></TopToolbar>
    <div class="canvasbody">
      <galleryFrame v-if="viewlayout.visBlockGallery"></galleryFrame>
      <codeEditor v-if="viewlayout.visCodeEditor"></codeEditor>
      <Drawtoolbar class="sidebar"></Drawtoolbar>
      <dxfview class="dxfview" ref="dxfviewref" @mouseleave="onMouseleave" @mouseenter="onMouseenter"></dxfview>
      <Drawhelptoolbar class="rightSidebar"></Drawhelptoolbar>
      <attributeBox v-if="attributeBarFalg"></attributeBox>
      <layerManagePop ref="refLayerManagePop"></layerManagePop>
      <tips></tips>
    </div>
    <commandDisplay class="commandDisplay"></commandDisplay>
    <commandInput class="commandInput"></commandInput>

    <div class="tragetrenderdom" v-if="showProps.isBackIdRender">
      <img class="alp" width="200" height="200" :src="pickpixi" />
      <!-- <img class="alp" width="400" height="400" :src="targetrender"/> -->
    </div>
  </div>
</template>



<script setup>
import { emitter, emittevnets } from 'emitter'
import Head from './components/head.vue'
import TopToolbar from './components/toptoolbar.vue'
import Drawtoolbar from './components/drawtoolbar.vue'
import Drawhelptoolbar from './components/drawhelptoolbar.vue'
import commandDisplay from './components/commandDisplay.vue'
import commandInput from './components/commandInput.vue'
import layerManagePop from './components/popFrame/layerManagePop.vue'
import galleryFrame from './components/galleryFrame.vue'
import codeEditor from './components/popFrame/codeEditor.vue'
import attributeBox from './components/attributeBox.vue'
import dxfview from '../dxfview/index.vue'
import tips from './components/tips.vue'
import { viewlayout, globalTip,attributeBarFalg,blockBox, showProps } from 'dxf-viewer'

import { canvasStates } from '@/store/canvasStates'
let { lsObj } = canvasStates()

import { ElLoading } from "element-plus";
import { mitts, btnFnObj } from 'sgdraw/mittBus'



const tipconf = globalTip.tipconf
const refLayerManagePop = ref()
const targetrender = ref('')
const pickpixi = ref('')
const onMouseleave = () => {
  tipconf.valid = false
}
const onMouseenter = () => {
  tipconf.valid = true
}
emitter.on(emittevnets.targetrender, (v) => {
  targetrender.value = v.img
})
emitter.on(emittevnets.pickpixi, (v) => {
  pickpixi.value = v.img
})
mitts.on(btnFnObj.showLayerManagePop, () => { //图层弹框
  refLayerManagePop.value.show()
})
const dxfviewref = ref(null)

onMounted(() => {
  // console.log(dxfviewref.value,'dxfviewrefdxfviewrefdxfviewref');
  if (lsObj.dxfurl) {
    const loading = ElLoading.service({
      lock: true,
      text: "Loading",
      background: "rgba(0, 0, 0, 0.7)",
    });
    fetch(lsObj.dxfurl)
      .then(response => {
        if (!response.ok) {
          throw new Error("HTTP error " + response.status);
        }
        return response.blob();
      })
      .then(async file => {
        mitts.emit(btnFnObj.lsfn, file)
        loading.close();
      })
      .catch(error => {
        console.error('There was an error!', error);
      });
  }
})

</script>

<style lang="scss" scoped>
.home {
  width: 100vw;
  height: 100vh;
  background-color: hsl(218, 17%, 27%);
  overflow: hidden;

  .LayerManage {
    width: 100vw;
    height: 51px;
  }

  .canvasbody {
    width: 100vw;
    height: calc(100vh - 260px);
    display: flex;
    position: relative;
  }

  .commandDisplay {
    width: 100%;
    height: 70px;
  }

  .commandInput {
    width: 100%;
    height: 60px;
  }
}

.tragetrenderdom {
  position: absolute;
  right: 0px;
  top: 0px;
  // min-width: 600px;
  // min-height: 400px;

  display: flex;
}

.alp {
  background-color: rgba($color: #8d8d8d, $alpha: 0.5);
}
</style>