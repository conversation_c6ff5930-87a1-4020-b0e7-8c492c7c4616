<template>
<div class="colorpicker">
    <el-dialog :model-value="isVisible" title="选择颜色" width="600" :before-close="onCancel">
        <div class="bodybox">
            <el-tabs v-model="tabInx" class="demo-tabs" @tab-click="tabClick">
                <el-tab-pane label="索引颜色" name="byIndex"></el-tab-pane>
                    <div class="col">
                        <div class="row fb" v-for="ii,i in rows1" :key="ii">
                            <div v-for="jj,j in cols" :key="j"
                                    class="pickcolbtn box20"  :style="getBtnColorStyle(ii+j*10)"
                                    @mouseover="btnHover(ii+j*10)" @mouseleave="btnLeaver" @click="onPick(ii+j*10)">
                            </div>
                        </div>
                        <div class="split10"></div>
                        <div class="row fb" v-for="ii,i in rows2" :key="ii">
                            <div v-for="jj,j in cols" :key="j"
                                    class="pickcolbtn box20"  :style="getBtnColorStyle(ii+j*10)"
                                    @mouseover="btnHover(ii+j*10)" @mouseleave="btnLeaver" @click="onPick(ii+j*10)">
                            </div>
                        </div>
                    </div>
                    <div class="row fb" style="height: 24px;">
                        <div>{{ hcolor.idx }}</div>
                        <div>{{ hcolor.rgb }}</div>
                    </div>
                    <div class="row fb">
                        <div class="col">
                            <div class="row">
                                <div v-for="i,idx in color1" :key="idx"
                                class="pickcolbtn box24" :style="getBtnColorStyle(i)" @mouseover="btnHover(i)" 
                                @mouseleave="btnLeaver" @click="onPick(i)"></div>
                            </div>
                            <div class="row" >
                                <div v-for="i,idx in color1" :key="idx"
                                class="pickcolbtn box24" :style="getBtnColorStyle(i+249)" @mouseover="btnHover(i+249)" 
                                @mouseleave="btnLeaver" @click="onPick(i+249)"></div>
                            </div>
                        </div>
                        <div class="replacecolor">
                            <div class="oldcolor" :style="oldcolorstyle"></div>
                            <div class="newcolor" :style="newcolorstyle"></div>
                        </div>
                    </div>
                    
                <el-tab-pane label="真彩色" name="byPicker"></el-tab-pane>
            </el-tabs>

        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" class="btn" @click="onConfirm">确认</el-button>
                <el-button class="btn" style="background-color: #2e3440;color: #fff;" @click="onCancel">取消</el-button>
            </div>
        </template>
    </el-dialog>
</div>
</template>

<script setup>
import {CadColors} from 'dxf-viewer'

const tabInx=ref('byIndex')
const isVisible=ref(false)
const props = defineProps({
    color:{
       type: String,
       default:'rgb(0,255,0)'
    }
})
const emits=defineEmits(['colorPicked'])
const color1=[1,2,3,4,5,6]
const rows1=[17,15,13,11,9]
const rows2=[10,12,14,16,18]
const cols=new Array(24)
const getBtnColorStyle=computed(()=>(idx)=>{
    let [r,g,b]=CadColors[idx]
    return {"background-color":`rgb(${r},${g},${b})`}
}) 
const newcolorstyle=computed(()=>{
    return {"background-color":pickcolor.rgb}
}) 
const oldcolorstyle=computed(()=>{
    return {"background-color":props.color}
}) 
const pickcolor=reactive({
    idx:'',
    rgb:'',
    color:[]
})
const hcolor=reactive({
    idx:'',
    rgb:''
})
const onPick=(i)=>{
    let [r,g,b]=CadColors[i]
    pickcolor.color=[r,g,b]
    pickcolor.idx=`颜色索引值:${i}`
    pickcolor.rgb=`rgb(${r},${g},${b})`
}

const tabClick=(tab, event)=>{
    console.log(tab,event)
}
const onCancel = () => {
    isVisible.value=false
}
const onConfirm = () => {
    if(pickcolor.color.length>0 && pickcolor.rgb!=props.color){
        emits('colorPicked',pickcolor)
    }
    isVisible.value=false
}
const show=()=>{
    isVisible.value=true
    init()
}
const init=()=>{
    Object.assign(pickcolor,{    
        idx:'',
        rgb:'',
        color:[]
    })
    Object.assign(hcolor,{
        idx:'',
        rgb:'' 
    })
}
const btnHover=(i)=>{
    let [r,g,b]=CadColors[i]
    hcolor.idx=`颜色索引值:${i}`
    hcolor.rgb=`RGB:${r},${g},${b}`
}
const btnLeaver=()=>{
    hcolor.idx=''
    hcolor.rgb=''
}

defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.colorpicker {
    background-color: #3a4352;
}

.pickcolbtn{
    border: 3px solid rgb(178, 177, 177);
    margin: 1px;
    padding: 2px;
    inset: 1px;

}
.pickcolbtn:hover{
    border:4px solid rgb(0, 0, 0);
}


.t25{
    line-height: 30px;
    font-size: 20px;
}
.fb{
    justify-content:space-between;
}
.replacecolor{
    width: 120px;
    height: 120px;
    position: relative;
}
.oldcolor{
    width: 80px;
    height: 80px;
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 99998;
    border: 3px solid rgb(178, 177, 177);

}
.newcolor{
    width: 90px;
    height: 90px;
    position: absolute;
    left: 0px;
    bottom: 0px;
    z-index: 99999;
    border: 3px solid rgb(178, 177, 177);
}
</style>