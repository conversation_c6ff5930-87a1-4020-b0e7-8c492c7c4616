export enum GrayColorType{
    Include,//包含的设置灰色
    Exclude,//不包含的设置灰色
    None//设置灰色
}
export class DxfGrayManage{
    static instance: DxfGrayManage;
    static getInstance(){
        if (!this.instance) {
            this.instance = new DxfGrayManage();
        }
        return this.instance;
    }

    public includeIds:number[]=[]
    // public excludeIds:number[]=[]
    public graycolor:number[]=[15,15,15]
    public _grayType:GrayColorType=GrayColorType.None
    public set color(color){
        this.graycolor=color
    }
    public set grayType(graytype){
        this._grayType=graytype
    }
    constructor(){
        this.includeIds=[]
        this.graycolor=[15,15,15]
    }
    public getIdColor(id:number,mrgb:any){
        if(this._grayType==GrayColorType.None){
            return mrgb
        }else{
            let isGray=false
            if(this._grayType==GrayColorType.Include){
                isGray=this.includeIds.includes(id)
            }
            if(this._grayType==GrayColorType.Exclude){
                isGray=!this.includeIds.includes(id)
            }
            let [r,g,b]=this.graycolor
            return isGray?[mrgb[0],r,g,b]:mrgb
        }
    }
    public clear(){
        this.includeIds=[]
        // this.excludeIds=[]
    }
}