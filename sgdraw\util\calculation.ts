import {Vector2D} from '../Vector2D'
import {croppingPops} from 'sgdraw/util/interfaceList.ts'

export const straightLineSelected=(params:any)=>{
    const {m1,graphical}=params
    const {p1,p2}=graphical
    // 计算方向向量
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    // 计算垂直向量
    const dxp = m1.x - p1.x;
    const dyp = m1.y - p1.y;
    // 计算点到线的距离
    const distance = Math.abs(dx * dyp - dy * dxp) / Math.hypot(dx, dy);
    // 如果距离小于容差，则认为点在直线上
    if (distance < 2) {
        // 判断点是否在线段内
        const t = (dxp * dx + dyp * dy) / (dx * dx + dy * dy);
        if (t >= 0 && t <= 1) {
        return true;
        }
    }
    return false;
}

export const isPointInPolygon= (arr:any,point:any)=>{
    let ret=false
    for (let index = 0; index < arr.length; index++) {
        const p1=arr[index][0]
        const p2=arr[index][1]
        const p3=arr[index][2]
        if(intriangle(p1,p2,p3,point)) {
            ret =true
            break
        }
        
    }
    return ret
}
export const countWh=(p1:any,p2:any)=>{
    let width=p2.x-p1.x
    let height=p2.y-p1.y
    return {
        width,
        height,
    }
}

export const getCanvasPosition=(canvasDom:any,e:any,offset={x:0,y:0},scale=1)=>{
  const rect=canvasDom.getBoundingClientRect()
  let x=e.pageX-rect.left
//   let y=canvasDom.offsetHeight-(e.pageY-rect.top)
  let y=(e.pageY-rect.top)
  
  return {
      x:(x-offset.x) / scale,
      y:(y-offset.y) / scale,
  }
}

export const mousePositionFn=(canvasDom:any,e:any)=>{
    const rect=canvasDom.getBoundingClientRect()
    let x=e.pageX-rect.left
    // let y=canvasDom.offsetHeight-(e.pageY-rect.top)
    let y=(e.pageY-rect.top)
    return {
        x,
        y,
    }
}

//计算两点距离
export const getDistance=(p1:any,p2:any)=>{ 
  return Math.sqrt((p1.x-p2.x) ** 2 + (p1.y-p2.y) ** 2 )
}

function intriangle(p1:any,p2:any,p3:any,point:any) {
    const a=p2.copy().minus(p1)
    const b=p3.copy().minus(p2)
    const c=p1.copy().minus(p3)

    const u1=point.copy().minus(p1)
    const u2=point.copy().minus(p2)
    const u3=point.copy().minus(p3)

    const s1=Math.sign(a.cross(u1))
    let p=a.dot(u1)/a.len** 2
    if(s1===0 && p>=0 && p<=1) return true
    const s2=Math.sign(b.cross(u2))
    p=b.dot(u2)/b.len** 2
    if(s2===0 && p>=0 && p<=1) return true
    const s3=Math.sign(c.cross(u3))
    p=c.dot(u3)/c.len** 2
    if(s3===0 && p>=0 && p<=1) return true

    return s1=== s2 && s2===s3
}

export const dimensionCount=(p1:any,p2:any,p3:any)=>{
    let AC=new Vector2D(p1.y-p2.y,p2.x-p1.x)
    let AM=new Vector2D(p3.x-p1.x,p3.y-p1.y)
    let AD=AC.scale(AM.dot(AC) / AC.len ** 2)
    let D1=new Vector2D(
        AD.x+p1.x,
        AD.y+p1.y
    )
    let x =p2.x-p1.x+D1[0]
    let y=p2.y-p1.y+D1[1]
    let D={
        x:D1[0],
        y:D1[1]
    }
    let E={
        x,
        y,
    }
    return {
        D,
        E
    }
}
export const triangleCount=(p1,p2)=>{//标注三角坐标计算
    let D=new Vector2D(p1.x,p1.y)
    let E=new Vector2D(p2.x,p2.y)
    let DE=new Vector2D(p2.x-p1.x,p2.y-p1.y)
    let lDE=getDistance(p1,p2)
    
    let DEanticlockwise=new Vector2D(p1.y-p2.y,p2.x-p1.x)
    let DEclockwise=new Vector2D(p2.y-p1.y,p1.x-p2.x)

    let OGa=DEanticlockwise.normalize().scale(4)
    let OGc=DEclockwise.normalize().scale(4)

    let DO=DE.normalize().scale(16)
    let O={
        x:DO.x+D.x,
        y:DO.y+D.y
    }

    let DL=DE.normalize().scale(lDE-16)
    let L={
        x:DL.x+D.x,
        y:DL.y+D.y
    }

    let Ga={
        x:O.x+OGa.x,
        y:O.y+OGa.y,
    }
    let Gc={
        x:O.x+OGc.x,
        y:O.y+OGc.y,
    }
    let La={
        x:L.x+OGa.x,
        y:L.y+OGa.y,
    }
    let Lc={
        x:L.x+OGc.x,
        y:L.y+OGc.y,
    }
    return {
        Ga,
        Gc,
        La,
        Lc,
    }
}
export const charactersCount=(A,B,D,E,L:number=12)=>{//标注字体计算
    let AD=new Vector2D(D.x-A.x,D.y-A.y)
    let lAD=getDistance(A,D)+L

    let AG=AD.normalize().scale(lAD)

    let G={
        x:A.x+AG.x,
        y:A.y+AG.y, 
    }
    let H={
        x:B.x+AG.x,
        y:B.y+AG.y, 
    }

    let radian = Math.atan2(B.y - A.y,B.x - A.x);
    let value=getDistance(G,H).toFixed(2) +''
    let pointP ={
        x: (G.x + H.x) / 2,
        y: (G.y + H.y) / 2,
    }
    return {
        pointP,
        value,
        radian,
    }
}
export const guidesCount=(p1,m1,l=40)=>{//辅助线坐标计算
    let O=new Vector2D(p1.x,p1.y)
    let M=new Vector2D(m1.x,m1.y)
    let r=Math.atan2(m1.y-p1.y,m1.x-p1.x)
    let lOM2=getDistance(m1,p1)
    // console.log( r,'角度');
    let om1:any,M2:any;
    
    M2={
        x:lOM2+O.x,
        y:O.y,
    }
    if(r<0) {
        om1=new Vector2D(m1.y-p1.y,p1.x-m1.x)
    }else if(r>0) {
        om1=new Vector2D(p1.y-m1.y,m1.x-p1.x)
    }else {
        om1=new Vector2D(m1.y-p1.y,p1.x-m1.x)
        M2={
            x:O.x-lOM2,
            y:O.y,
        }
    }
    let OF1=om1.normalize().scale(l)
    let MF2=om1.normalize().scale(l)
    
    let F1={
        x:OF1.x+O.x,
        y:OF1.y+O.y,
    }
    let F2={
        x:MF2.x+M.x,
        y:MF2.y+M.y,
    }
    return {
        F1,
        F2,
        M2,
        r,
        lOM2,
    }
}
export const angleConversion=(angle)=>{//角度转换字符串
    
    let degree =angle * 180 / Math.PI; // 将弧度转换为180度范围内的角度值

    // 确保结果在0到180度之间
    degree =Math.abs(((degree + 180) % 360) - 180);
    // 将角度值转换为字符串
    let degreeStr = degree.toFixed(2) + '°'; // 保留两位小数

    return degreeStr
}
export const distance=(obj:any,L:number)=>{
    const dx = obj.p2.x - obj.p1.x;
    const dy = obj.p2.y - obj.p1.y;
    // 计算垂直向量
    const dxp = obj.m1.x - obj.p1.x;
    const dyp = obj.m1.y - obj.p1.y;
    // 计算点到线的距离
    const distance = Math.abs(dx * dyp - dy * dxp) / Math.hypot(dx, dy);
    // 如果距离小于容差，则认为点在直线上
    if (distance < L) {
        // 判断点是否在线段内
        const t = (dxp * dx + dyp * dy) / (dx * dx + dy * dy);
        if (t >= 0 && t <= 1) {
         return true;
        }
    }
    return false;
}
export const copylineCount=(p1,p2,G,m1)=>{
    let AB={
        x:p2.x-p1.x,
        y:p2.y-p1.y,
    }
    let BG={
        x:G.x-p2.x,
        y:G.y-p2.y,
    }
    let D={
        x:m1.x-BG.x,
        y:m1.y-BG.y,
    }
    let C={
        x:D.x-AB.x,
        y:D.y-AB.y
    }
    return {
        C,
        D,
    }
}
export const charactersRangeCount=(ctx,p1,value,angle:number=0,size:number=20)=> {//计算文字范围
    ctx.font = `${size}px SimSun`; // 设置字体大小和类型
    let metrics = ctx.measureText(value);
    let lp={
        x:p1.x-metrics.width / 2,
        y:p1.y+size /2
    }
    let rt={
        x:p1.x+metrics.width / 2,
        y:p1.y-size /2
    }
    return {
        lp,
        rt
    }

}
export const isPointInsideRectangle=(m1,p1,p3)=>{//计算点是否在矩形内
    return (m1.x >= p1.x && m1.x <= p3.x && m1.y <= p1.y && m1.y >= p3.y)
}
export const copywenzi=(p1,G,m1)=>{ //文字复制
 let AB={
    x:G.x-p1.x,
    y:G.y-p1.y
 }
 let m2={
    x:m1.x-G.x+p1.x,
    y:m1.y-G.y+p1.y
 }
 return m2
}
export  const getCenterPos=(x1,y1,x2,y2,x3,y3)=>{
    var a=2*(x2-x1);
    var b=2*(y2-y1);
    var c=x2*x2+y2*y2-x1*x1-y1*y1;
    var d=2*(x3-x2);
    var e=2*(y3-y2);
    var f=x3*x3+y3*y3-x2*x2-y2*y2;
    var x=(b*f-e*c)/(b*d-e*a);
    var y=(d*c-a*f)/(b*d-e*a);
    let O={
        x,
        y,
    }
    let r=getDistance({x:x1,y:y1},{x,y})
    var angleOA = Math.atan2(y1-y,x1-x) 
    var angleOB = Math.atan2(y3-y,x3-x)
    if (angleOA < 0) {
        angleOA += 2 * Math.PI;
      }
      if (angleOB < 0) {
        angleOB += 2 * Math.PI;
      }
    return {
        O,
        r,
        angleOA,
        angleOB,
    }
}
export const isPointLeftOfLine=(A, B, C)=> {//判断点C在AB的左侧还是右侧
    // 解构点的坐标
    let ax=A.x
    let ay=A.y
    let bx=B.x
    let by=B.y
    let xc=C.x
    let yc=C.y

    // 计算向量AB和AC的叉积
    const crossProduct = (bx - ax) * (yc - ay) - (by - ay) * (xc - ax);

    // 根据叉积的符号判断并返回结果
    return crossProduct > 0 ? 'left' : crossProduct < 0 ? 'right' : 'mid';
}

export const calculateThirdPointOfEquilateralTriangle=(A, B)=> {
    const x1 = A.x;
    const y1 = A.y;
    const x2 = B.x;
    const y2 = B.y;
  
    // 计算 AB 边长
    const distanceAB = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  
    // 单位向量，沿45度角方向
    const unitVector = [1 / Math.sqrt(2), 1 / Math.sqrt(2)];
  
    // 放大单位向量至 AB 边长，得到指向 C 点的方向向量
    const directionVector = [
      distanceAB * unitVector[0],
      distanceAB * unitVector[1],
    ];
  
    // 计算 C 点坐标
    const x3 = x2 + directionVector[0];
    const y3 = y2 + directionVector[1];
  
    return { x: x3, y: y3 };
}

export const measureTextBounds=(text,size=20)=> {//文字边界框
    // 创建一个临时的离屏Canvas上下文
    const canvas = document.createElement('canvas');
    document.body.appendChild(canvas);
    const context:any = canvas.getContext('2d');
    // 设置所需的字体样式
    context.font = `${size}px SimSun`// 默认字体样式，可根据需要传入参数
    // 使用measureText()获取文字尺寸
    const metrics = context.measureText(text);
    document.body.removeChild(canvas);
    // 返回一个包含文字宽度和估计高度的对象
    return metrics.width
}

export const croppingdRraw=(params:croppingPops)=>{//裁剪区域绘制
    let {path,type,optslist}=params
    if(type==='LineSegment2d') {
        if(optslist.i===0) {
            path.moveTo(optslist.p1.x,optslist.p1.y)
        }else {
            path.lineTo(optslist.p1.x,optslist.p1.y)
        }
    }else if(type==='Arc2d') {
    }
}


