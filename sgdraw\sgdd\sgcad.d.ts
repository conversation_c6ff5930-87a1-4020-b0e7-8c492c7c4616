declare module sg{
    class Point2d{
        constructor();
        constructor(x:number,y:number);
        constructor(p:Point2d);
        x():number;
        y():number;
        set(x:number,y:number):Point2d;
        distanceTo(p:Point2d):number;
        transform(m:Matrix3):Point2d;
        transformed(m:Matrix3):Point2d;
        getDistancePoint(v:Vector2d,d:number):Point2d;
        getPoint()
    }
    class Matrix3{
        constructor();
        constructor(m:Matrix3);
        isNull():void;
        isUnitary():void;
        setUnitary():void;
        move(x:number,y:number):void;
        rotate(pt:Point2d,theta:number):void;
        mirror(obj:any):void;
        scale(s:Matrix3):void;
        invMatrix(i:Matrix3):boolean;
        transform_pt(p:Point2d):Point2d;
        transform_vec(p:Vector2d):Point2d;
        multiply(m:Matrix3):Matrix3;
        isEqual(m:Matrix3):boolean;
        copyFrom(m:Matrix3):void;
    }
    class Curve2d {
        getFirstParameter():number
        getLastParameter():number
        evalPoint(n:number):Point2d
        evalTangent(p:Point2d):Vector2d
        tangent(t:number):Vector2d
        isPointOnCurve(p:Point2d,eps:number):boolean
        transform(mat:Matrix3):void
        reverse():void
        getBBox():Rect2d
        
    }
    class BoundCurve2d extends Curve2d {
        startPoint():Point2d
        endPoint():Point2d
        length():number
    }
    class LineSegment2d extends BoundCurve2d {
        constructor()
        constructor(l1:LineSegment2d)
        constructor(p1:Point2d,p2:Point2d)
        getCurveType():'LineSegment2d'
    }
    class Arc2d extends BoundCurve2d {
        constructor()
        constructor(p1:Point2d,p2:Point2d,p3:Point2d)
        constructor(m:Point2d,num1:number,num2:number,num3:number)
        getCurveType():'Arc2d'
        getCenter()
        getRadius()
        getStartAngle()
        getEndAngle()
        isAntiClock()
        getInterpolatePoints(n:number)
    }
    class BSpline2d extends BoundCurve2d {
        constructor()
        constructor(plist: vector_Point2d)
        constructor(b1:BSpline2d)
        getCurveType():'BSpline2d'
        getInterpolatePoints(n:number)
    }
    class Circle2d extends Curve2d {
        constructor()
        constructor(p1:Point2d,num1:number)
        constructor(c1:Circle2d)
        getCenter():Point2d
        getRadius():number
        getCurveType():'Circle2d'
        getInterpolatePoints(n:number)
    }
    class Line2d extends Curve2d {
        constructor()
        constructor(p1:Point2d,p2:Point2d)
        constructor(l1:Line2d)
        getDir():Vector2d
        getLoction():Point2d
        getCurveType():'Line2d'
    }
    class Ellipse2d extends Curve2d {
        constructor()
        constructor(p1:Point2d,v:Vector2d,num1:number,num2:number)
        constructor(e1:Ellipse2d)
        getCenter():Point2d
        getXDir():Vector2d
        getMajorRadius():number
        getMinorRadius():number
        getCurveType():'Ellipse2d'
        getInterpolatePoints(n:number)
    }
    class EllipseArc2d extends BoundCurve2d{
        constructor()
        constructor(p1:Point2d,v:Vector2d,num1:number,num2:number,num3:number,num4:number,isAntiClock:boolean)
        constructor(e1:EllipseArc2d)
        getCurveType():'EllipseArc2d'
        getInterpolatePoints(n:number)
    }
    class AnyCurve2d{
        constructor()
        startPoint():Point2d
        endPoint():Point2d
        length():number
        transform(mx:Matrix3):void
        curveCount()
        getBoundCurve(n:number)
    }
    class PolyLine2d extends AnyCurve2d{
        constructor()
        constructor(plist: vector_Point2d,bulge:vector_double,isColsed:boolean)
    }
    class SGColor {
        constructor()
        constructor(n1:number,n2:number,n3:number,n4:number)
        getR():number
        getG():number
        getB():number
        getA():Number
        getMode()
    }
    class SGDrawBaseObj {
        constructor()
        constructor(std1:string,sg:SGColor,f1:number,std2:object,f2:number)
        setObjProp(std1:string,color:SGColor,linewidth:number,std2:string,linetypeScale:number):void
        getLayer():string
        getColor():SGColor
        getLineWidth():number
        getLineType():string
        getLineTypeScale():number
        setLayer(std:string):void
        setColor(sg:SGColor):void
        setLineWidth(width:number):void
        setLineType(std:string):void
        setLineTypeScale(n1:number):void
        getBoundingBox():void
    }
    class SGObjText {
        constructor()
        constructor(p1:Point2d,std:string,d:number,TextAlign:SGObjText,d2:number,d3:number)
        getObjType():void
        getBoundingBox():void
        transform():void
        getAlignPoint():Point2d
        setAlignPoint(p1:Point2d):void
        setText(str:string):void
        getAngle():number
        setAngle(angle:number):void
        getAlignment():SGObjText
        setAlignment(str:SGObjText):void
        getHeight():number
        setHeight(height:Number):void
        getWidFactor():number
        setWidFactor(widFactor:number):void
        getTextStyle():string
        setTextStyle(str:string):void
        getText():string
        getInsertPoint()
        setInsertPoint(p1:Point2d)
        getRenderInfo()//优化,拿到所有数据
    }
    class SGObjDim extends SGDrawBaseObj{
        constructor()
        getDimensionPos():Point2d
        setDimensionPos(p1:Point2d):void
        getTextPos():Point2d
        setTextPos(p1:Point2d):void
        getType():number
        setType(n:number):void
        getAttachment():number
        setAttachment(n:number):void
        getLineSpacingStyle():number
        setLineSpacingStyle(n:number):void
        getLineSpacingFactor():number
        setLineSpacingFactor(n:number):void
        getAngle():number
        setAngle(n:number):void
        getLinearFactor():number
        setLinearFactor(n:number):void
        getDimScale():number
        setDimScale(n:number):void
        getText():string
        setText(text:string):void
        getStyle():string
        setStyle(str:string):void
    }
    class SGObjAlignDim extends SGObjDim{
        constructor()
        constructor(p1:Point2d,p2:Point2d)
        getObjType():void
        getBoundingBox():void
        transform():void
        getStartPoint():Point2d
        setStartPoint(p1:Point2d):void
        setEndPoint(p2:Point2d):void
        getEndPoint():Point2d
        getDimensionText(n1:number):string
        set(p1:Point2d,p2:Point2d,p3:Point2d):void//对齐标注设置标注点
    }
    class SGObjLinearDim extends SGObjDim{
        constructor()
        constructor(p1:Point2d,p2:Point2d,n1:number,n2:number)
        getObjType():void
        getBoundingBox():void
        transform():void
        getStartPoint():Point2d
        setStartPoint(p1:Point2d):void
        setEndPoint(p2:Point2d):void
        getEndPoint():Point2d
        getAngle():number
        setAngle(n:number):number
        getOblique():void
        setOblique():void
    }
    class SGObjRadialDim extends SGObjDim {
        constructor()
        constructor(p:Point2d,n:number)
        getObjType(s:SGDrawBaseObj):boolean
        getBoundingBox(s:SGObjRadialDim):void
        transform(m:Matrix3):void
        getDefinitionPoint():void
        setDefinitionPoint():void
        getLeader():void
        setLeader():void
    }
    class SGObjDiametricDim extends SGObjDim{
        constructor()
        constructor(p:Point2d,n:number)
        getObjType(s:SGDrawBaseObj):boolean
        getBoundingBox(s:SGObjRadialDim):void
        transform(m:Matrix3):void
        getDefinitionPoint():void
        setDefinitionPoint():void
        getLeader():void
        setLeader():void
    }
    class SGObjLeader extends SGDrawBaseObj{
        getObjType(s:SGDrawBaseObj):boolean
        getBoundingBox(s:SGObjRadialDim):void
        transform(m:Matrix3):void
    }
    class SGObjCurve extends SGDrawBaseObj{
        constructor()
        constructor(p1:Point2d,p2:Point2d)
        constructor(l1:LineSegment2d)
        getObjType():void
        getBoundingBox():void
        transform():void
        getCurveType():'SGObjCurve'
        getLine():LineSegment2d
        getCurve()
        setpCurve(l1:LineSegment2d)
        getRenderPoints(n:number)
    }
    class SGObjComBinCurve extends SGDrawBaseObj{
        constructor()
        getObjType():void
        getBoundingBox():void
        transform():void
        setpCurve(AnyCurve2d):void//设置曲线
        getCurve():AnyCurve2d
        getDrawObjectId():void
        getRenderPoints(n:number)
    }
    class SGObjHatch extends SGDrawBaseObj{
        constructor()
        getObjType():void
        getBoundingBox():void
        transform():void
        getHatchPoints():Point2d
        getHatchLines():Point2d
        calDrawingData():Point2d
        getOutLoop():AnyCurve2d
        getInnerLoopSize():number
        getInnerLoop(idx:number):AnyCurve2d
    }
    class SGObjTool{
        static convertToText(sg:SGDrawBaseObj):SGObjText
        static convertToCurve(sg:SGDrawBaseObj):SGObjCurve
        static convertToAlignDim(sg:SGDrawBaseObj):SGObjAlignDim
        static convertToLinearDim(sg:SGDrawBaseObj):SGObjLinearDim
        static convertToPoint(sg:SGDrawBaseObj):Point2d
        static convertToComBinCurve(sg:SGDrawBaseObj):SGObjComBinCurve
        static convertToRadialDim(sg:SGDrawBaseObj)
        static convertToAngular2LDim(sg:SGDrawBaseObj)
        static convertToDiametricDim(sg:SGDrawBaseObj)
        static convertToOrdinateDim(sg:SGDrawBaseObj)
        static convertToLeader(sg:SGDrawBaseObj)
        static convertToMText(sg:SGDrawBaseObj)
        static convertToHatch(sg:SGDrawBaseObj)
    }
    class DxfImportManager{
        constructor()
        static deleteDxf(id:string)//
        static getDxf(id:string):SGBaseDrawingGroup//传值为空不做缓存，传值不会空则缓存
        static deleteAll()//清除wasm缓存
    }
    class SGBaseDrawingGroup {
        constructor()
        constructor(sg:SGBaseDrawingGroup)
        getScale():void
        setScale():void
        isEmpty():boolean
        saveToString():String
        loadFromString(str:string):void
        addText(obj:SGObjText):void
        addCurve(obj:SGObjCurve):void
        addAlignDim(obj:SGObjAlignDim):void
        getBaseObj(index:number):SGDrawBaseObj
        count():number 
        insertBlockCount():number
        getInsertBlock(idx:number)
        getBlockByInsert(i)
        addBaseObj(sg:SGObjCurve)
        getObjType(id:number):number//根据id判断选择的类型：1表示图元，2：表示插入块
        getInsertBlockById(id:number):InsertSGBlock//通过id获取块
        getPBaseObjById(id:number):SGDrawBaseObj//通过id获取对象
        getDxfBaseData()//获取图层，线型等对象
        copyNewOne()//克隆对象
        appendCutOffString(str:string)//加载字符串dxf
        serializeToStringCo(id:number)//导出
        initTransactionManager()//绑定undoredo
        getTransactionManager()
        deleteBaseObjByIds(ids:any)
        addBlock(sg:SGBlock)
        addInsertBlock(sg:InsertSGBlock)
        calSpInsertBlockObjList(sg:InsertSGBlock)
        isExistBlockName(sg:SGBlock)
    }
    class SGLayer{
        constructor()
        setName(str:string)
        setColor(sg:SGColor)
    }
    class SGLinetypeData{
        constructor()
    }
    class DxfBaseData{
        constructor()
        public layerCount
        public getLayer(index:number):SGLayer
        public addLayer(layer:SGLayer)//更新机制，添加图层后group中图层会被替换
        public deleteLayer(layer:SGLayer)//删除图层
        public lineTypeCount
        public getLineType(index:number):SGLinetypeData
    }
    class SGDxfImport {
        constructor()
        constructor(std:string)
        getDrawGroup():SGBaseDrawingGroup
        getString():string
        getBlockCount(blockName:string,layer:string):number
        importDxf():void
        // getDrawBaseObj():any
    }
    class SGDxfOutPut {
        constructor()
        constructor(std:string,sg:SGBaseDrawingGroup)
        // addDrawBaseObj(baseObjGp:SGBaseDrawingGroup):void
        outputDxf():void
    }
    class SGBlock {
        constructor(blockName:string,p1:Point2d,isVirtual:boolean)
        getBoundingBox()
        isVirtual():boolean
        count():number
        getBaseObj(idx:number):SGDrawBaseObj
        getName():string
        addBaseObj(sg:SGObjCurve)
    }
    class InsertSGBlock {
        constructor()
        getReferredBlockName()
        getInsertPoint()
        getRotateAngle()
        getDrawObjectId():number
        setInsertBlockName(blockName:string)
        setInsertBlockInsertPoint(p1:Point2d) //插入点
    }
    class Geometry2dUtils {
        static convertToArc2d(c:Curve2d):Arc2d
        static convertToCircle2d(c:Curve2d):Circle2d
        static convertToEllipse2d(c:Curve2d):Ellipse2d
        static convertToEllipseArc2d(c:Curve2d):EllipseArc2d
        static convertToBSpline2d(c:Curve2d):BSpline2d
        static convertToPolyLine2d(c:Curve2d):PolyLine2d
        static convertToLineSegment2d(c:Curve2d):LineSegment2d
        
    }
    class Rect2d{
        public add(rect:Rect2d)
        public getMiddlePoint()
        public getLeftTopPoint()
        public width()
        public heigth()
    }
    class Vector2d{
        constructor()
        constructor(p1:number,p2:number)
        constructor(v:Vector2d)
        x():number
        y():number
        set(x:number,y:number):Vector2d
        transform(m:Matrix3):void
        transformed(m:Matrix3):Vector2d
        dot(v:Vector2d):number
        length():number
        normalize():Vector2d
        leftVector2d():Vector2d
        rightVector2d():Vector2d
        angle():number
        isOpposite(o:Vector2d):boolean
        isParallel(o:Vector2d):boolean
        isSameDir(o:Vector2d):boolean
    }
    class vector_Point2d {
        push_back(p1:Point2d):void
    }
    class vector_double {
        push_back(n:number):void
    }
    class vector_int {
        push_back(n:number):void
        size():number
        get(n:number):number
    }
    class vector_string {
        push_back(n:string):void
        size():string
        get(n:string):string
    }
    
    function getRectDrawingGroup(id:string, leftTop:Point2d, rightBot:Point2d);
    function getRangeDrawingGroup(id:string, electRange:Point2d[]);
    function getShareBuffer();
    function getShareBufferLen();
    function freeShareBuffer();
    function isBlockUsed(name:string):boolean
}    
