/**
 * useProjectBuild.js 使用示例
 * 展示如何使用优化后的项目构建数据管理 Hook
 */

import { ref, onMounted, watch } from 'vue'
import useProjectBuild from './useProjectBuild.js'

/**
 * 基础使用示例
 */
export function useProjectBuildBasic() {
  const olBoxRef = ref(null)
  
  const {
    // 状态
    CBComponent,
    FixedComponent,
    CB_Fixed_Count,
    loading,
    error,
    projectCode,
    
    // 计算属性
    totalComponents,
    hasData,
    
    // 方法
    initialize,
    exportExcel,
    reset,
    setProjectCode
  } = useProjectBuild(olBoxRef)

  // 组件挂载后初始化
  onMounted(async () => {
    if (olBoxRef.value) {
      await initialize()
    }
  })

  return {
    CBComponent,
    FixedComponent,
    CB_Fixed_Count,
    loading,
    error,
    projectCode,
    totalComponents,
    hasData,
    initialize,
    exportExcel,
    reset,
    setProjectCode
  }
}

/**
 * 带配置选项的使用示例
 */
export function useProjectBuildWithOptions() {
  const olBoxRef = ref(null)
  
  const {
    CBComponent,
    FixedComponent,
    loading,
    error,
    initialize,
    exportExcel,
    setProjectCode
  } = useProjectBuild(olBoxRef, {
    projectCode: 'PX20250724000001' // 自定义项目代码
  })

  // 监听项目代码变化，自动重新加载数据
  const currentProjectCode = ref('PX20250724000001')
  
  watch(currentProjectCode, async (newCode) => {
    if (newCode) {
      setProjectCode(newCode)
      await initialize()
    }
  })

  return {
    CBComponent,
    FixedComponent,
    loading,
    error,
    currentProjectCode,
    initialize,
    exportExcel
  }
}

/**
 * Vue 组件中的完整使用示例
 */
export function useProjectBuildInComponent() {
  const olBoxRef = ref(null)
  const exportStatus = ref('')
  
  const projectBuild = useProjectBuild(olBoxRef, {
    projectCode: 'PX20250724000003'
  })

  // 初始化数据
  const initializeData = async () => {
    try {
      await projectBuild.initialize()
      console.log('数据初始化成功')
    } catch (error) {
      console.error('初始化失败:', error.message)
    }
  }

  // 导出Excel
  const handleExportExcel = async () => {
    if (!projectBuild.hasData.value) {
      exportStatus.value = '没有可导出的数据'
      return
    }

    try {
      exportStatus.value = '正在导出...'
      await projectBuild.exportExcel()
      exportStatus.value = '导出成功'
    } catch (error) {
      exportStatus.value = `导出失败: ${error.message}`
    }
  }

  // 切换项目
  const switchProject = async (newProjectCode) => {
    try {
      projectBuild.reset()
      projectBuild.setProjectCode(newProjectCode)
      await projectBuild.initialize()
    } catch (error) {
      console.error('切换项目失败:', error.message)
    }
  }

  // 重新加载数据
  const reloadData = async () => {
    try {
      projectBuild.reset()
      await projectBuild.initialize()
    } catch (error) {
      console.error('重新加载失败:', error.message)
    }
  }

  return {
    // 状态
    ...projectBuild,
    exportStatus,
    
    // 方法
    initializeData,
    handleExportExcel,
    switchProject,
    reloadData
  }
}

/**
 * 错误处理示例
 */
export function useProjectBuildWithErrorHandling() {
  const olBoxRef = ref(null)
  const errorLog = ref([])
  
  const projectBuild = useProjectBuild(olBoxRef)

  // 错误处理包装器
  const withErrorHandling = (fn, actionName) => {
    return async (...args) => {
      try {
        await fn(...args)
        // 清除之前的错误
        const errorIndex = errorLog.value.findIndex(e => e.action === actionName)
        if (errorIndex > -1) {
          errorLog.value.splice(errorIndex, 1)
        }
      } catch (error) {
        const errorInfo = {
          action: actionName,
          message: error.message,
          timestamp: new Date().toISOString()
        }
        
        // 更新或添加错误信息
        const existingIndex = errorLog.value.findIndex(e => e.action === actionName)
        if (existingIndex > -1) {
          errorLog.value[existingIndex] = errorInfo
        } else {
          errorLog.value.push(errorInfo)
        }
        
        throw error
      }
    }
  }

  return {
    ...projectBuild,
    errorLog,
    
    // 包装后的方法
    initialize: withErrorHandling(projectBuild.initialize, 'initialize'),
    exportExcel: withErrorHandling(projectBuild.exportExcel, 'exportExcel'),
    loadCadMetaDetail: withErrorHandling(projectBuild.loadCadMetaDetail, 'loadCadMetaDetail')
  }
}

/**
 * 性能监控示例
 */
export function useProjectBuildWithPerformance() {
  const olBoxRef = ref(null)
  const performanceMetrics = ref({})
  
  const projectBuild = useProjectBuild(olBoxRef)

  // 性能监控包装器
  const withPerformanceMonitoring = (fn, actionName) => {
    return async (...args) => {
      const startTime = performance.now()
      
      try {
        const result = await fn(...args)
        const endTime = performance.now()
        
        performanceMetrics.value[actionName] = {
          duration: endTime - startTime,
          success: true,
          timestamp: new Date().toISOString()
        }
        
        return result
      } catch (error) {
        const endTime = performance.now()
        
        performanceMetrics.value[actionName] = {
          duration: endTime - startTime,
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        }
        
        throw error
      }
    }
  }

  return {
    ...projectBuild,
    performanceMetrics,
    
    // 监控后的方法
    initialize: withPerformanceMonitoring(projectBuild.initialize, 'initialize'),
    exportExcel: withPerformanceMonitoring(projectBuild.exportExcel, 'exportExcel'),
    loadCadMetaDetail: withPerformanceMonitoring(projectBuild.loadCadMetaDetail, 'loadCadMetaDetail')
  }
}

/**
 * 数据验证示例
 */
export function validateProjectBuildData(projectBuildInstance) {
  const validationResults = {
    CBComponent: [],
    FixedComponent: [],
    CB_Fixed_Count: []
  }

  // 验证组件数据格式
  const validateComponentData = (data, componentName) => {
    const errors = []
    
    if (!Array.isArray(data)) {
      errors.push(`${componentName} 不是数组格式`)
      return errors
    }

    data.forEach((row, index) => {
      if (!Array.isArray(row)) {
        errors.push(`${componentName}[${index}] 不是数组格式`)
        return
      }

      if (row.length !== 7) {
        errors.push(`${componentName}[${index}] 列数不正确，期望7列，实际${row.length}列`)
      }

      // 验证数量字段是否为数字
      const quantity = row[4]
      if (quantity !== undefined && isNaN(Number(quantity))) {
        errors.push(`${componentName}[${index}] 数量字段不是有效数字: ${quantity}`)
      }
    })

    return errors
  }

  // 执行验证
  validationResults.CBComponent = validateComponentData(
    projectBuildInstance.CBComponent, 
    'CBComponent'
  )
  
  validationResults.FixedComponent = validateComponentData(
    projectBuildInstance.FixedComponent, 
    'FixedComponent'
  )
  
  validationResults.CB_Fixed_Count = validateComponentData(
    projectBuildInstance.CB_Fixed_Count, 
    'CB_Fixed_Count'
  )

  // 计算总错误数
  const totalErrors = Object.values(validationResults)
    .reduce((sum, errors) => sum + errors.length, 0)

  return {
    isValid: totalErrors === 0,
    totalErrors,
    details: validationResults
  }
}
