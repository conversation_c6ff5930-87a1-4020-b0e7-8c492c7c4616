import { DrawHelp } from "./drawHelp"
import { globalConf, globalDxf, globalTip, poptip,GraphicType,DrawState } from "dxf-viewer/src"
import { singal } from "emitter"
import { BufferGeometry, Color, LineBasicMaterial, Vector3,Line as ThLine } from "three"

export class Line extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any//临时对象
    // public color:string='white'


    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        // 定义线的两个端点
        const geometry = new BufferGeometry().setFromPoints([]);
        // 创建线的材质
        console.log(this.color,'this.color');
        
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 使用几何体和材质创建线
        this.mesh = new ThLine(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init()
        console.log(this.viewer.renderer,'Line');
        
    }
    public async init(){
        let p0=await this.pointPick({
            tip:"指定第一个点",
            pos:'',
        })
        this.setV(p0)
        let p1=await this.lengthPick({
            tip:'指定第二个点',
            refPoint:this.pnts[this.pnts.length-1],
            length:'',
            radius:''
        })
        this.setV(p1)
        this.complete()
    }
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length==0){
            globalTip.tipconf.inputPosX=globalDxf.mouse.x
            globalTip.tipconf.inputPosY=globalDxf.mouse.y
        }
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }

    setV(v:Vector3){
        this.pnts.push(v)
    }
    public drawTempGraphic(v:Vector3){
        this.mesh.geometry=new BufferGeometry().setFromPoints([...this.pnts,v]);
    }
    
    complete(){
        let {x,y}=globalDxf.wcsOff
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        // this.mesh.geometry.dispose()
        // this.mesh.material.dispose()
        // this.mesh=null
        poptip.dispose()
        if(this.pnts.length<2) return
        let els:any[]=[]
        this.pnts.forEach((item,index)=>{
            if(index>0) {
                let p1=new sg.Point2d(this.pnts[index-1].x+x, this.pnts[index-1].y+y)
                let p2=new sg.Point2d(item.x+x, item.y+y)
                let l=new sg.LineSegment2d(p1,p2)
                let cv=new sg.SGObjCurve()
                let [r,g,b]=this.color
                cv.setColor(new sg.SGColor(r,g,b,255))
                // console.log(this.lineType,'this.lineTypethis.lineType');
                cv.setLineType(this.lineType)
                cv.setpCurve(l)
                els.push(cv)
                p1.delete()
                p2.delete()
            }
        })
        console.log(this.viewer.transaction);
        this.viewer.transaction.add(els)

        globalDxf.operation=GraphicType.None
        globalDxf.isdraw = false
        this.isSnap=false
        setTimeout(()=>{
            globalDxf.drawstate=DrawState.Pick
        })
        // this.onCancel()
    }
}

