import opentype from "opentype.js"

export const uploadFileAsText=(file)=> {
    return new Promise((resolve, reject) => {
        if (!file) {
            reject(new Error("No file selected."));
            return;
        }
        const reader = new FileReader();
        reader.onload = function(event:any) {
            // 文件读取成功，内容存储在event.target.result中
            resolve(event.target.result);
        };
        reader.onerror = function(error) {
            reject(error);
        };
        // 读取文件为文本
        reader.readAsArrayBuffer(file);
    });
}

export const createFontFetchers=(urls, progressCbk):any[]=> {
    function CreateFetcher(url) {
        return async function() {
            if (progressCbk) {
                progressCbk("font", 0, null)
            }
            const data = await fetch(url).then(response => response.arrayBuffer())
            if (progressCbk) {
                progressCbk("prepare", 0, null)
            }
            return opentype.parse(data)
        }
    }
    const fetchers:any[] = []
    for (const url of urls) {
        fetchers.push(CreateFetcher(url))
    }
    return fetchers
}