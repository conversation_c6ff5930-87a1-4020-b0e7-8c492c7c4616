<template>
  <div class="mapBox" @mousemove="mouseMoveCoord">
    <div class="view">
      <DxfViewer ref="mapViewers"> </DxfViewer>
      <div class="mapboxviewer" ref="mapboxviewer" v-show="visMAP" style="pointer-events: none"></div>
      <div class="control_btns mapbox">
        <span class="btn" style="margin-right: 10px" @click="onLoadCadMap">
          加载项目
        </span>
        <div v-show="true" style="display: flex; margin-right: 10px">
          <span class="btn" @click="extractData"> 提取数据 </span>
          <span class="btn" @click="collision"> 测试 </span>
          <span class="btn" @click="toMap"> 对齐地图 </span>
          <span class="btn" @click="toCAD"> 对齐CAD </span>
          <span class="btn" @click="moveCAD">
            {{ btnSet.checkView ? "开始校验" : "校验完成" }}
          </span>
          <span class="btn" @click="csbtnlist"> 列表 </span>
          <span class="btn" @click="exportList"> 导出清单数据 </span>
        </div>
      </div>
      <div class="control_btns cad" style="margin-top: 10px">
        <span class="btn1" @click="hideCAD">
          <el-checkbox v-model="btnSet.hideCAD" label="隐藏CAD" size="large" />
        </span>
        <span class="btn1" @click="hideMAP">
          <el-checkbox v-model="btnSet.hideMAP" label="隐藏地图" size="large" />
        </span>
        <span class="btn1" @click="hideTiff">
          <el-checkbox v-model="btnSet.hideTiff" label="隐藏航拍图" size="large" />
        </span>
        <span class="btn1" @click="hideTiffRes">
          <el-checkbox v-model="btnSet.hideTiffRes" label="隐藏影像识图" size="large" />
        </span>
        <span class="btn1" @click="hideCadRes">
          <el-checkbox v-model="btnSet.hideCadRes" label="隐藏CAD识图" size="large" />
        </span>
        <span class="btn1" @click="showDiff">
          <el-checkbox v-model="btnSet.showDiff" label="结果对比" size="large" />
        </span>
        <span class="btn1" @click="grayCAD">
          <el-checkbox v-model="btnSet.grayCAD" label="底图置灰" size="large" />
        </span>
        <span class="btn1" @click="ungrayCAD">
          <el-checkbox v-model="btnSet.ungrayCAD" label="反色" size="large" />
        </span>
      </div>
      <div class="control_btns llh">
        <span style="color: white"> 层级: </span><el-input style="width: 100px" v-model="map.zoom"> </el-input>
        <span style="color: white"> 经度: </span><el-input style="width: 100px" v-model="map.lng"> </el-input>
        <span style="color: white"> 纬度: </span>
        <el-input style="width: 100px" v-model="map.lat"> </el-input>
        <span class="btn" @click="locLngLat"> 定位 </span>
      </div>
    </div>
    <div class="resbox" v-if="resboxFlag">
      <h3 style="text-align: center; color: #fff; margin-bottom: 20px">
        施工统计
      </h3>
      <div class="information">
        <span>总数量：</span> <span>{{ resTotal }}</span>
      </div>
      <div class="information">
        <span>识别总数：</span> <span>{{ identifyNum }}</span>
      </div>
      <div class="information">
        <span>进度：</span> <span>{{ progress }}%</span>
      </div>
    </div>
    <div class="centercross"></div>
    <!-- 添加坐标信息显示容器 -->
    <div class="coordinate-display">
      X: {{ coordDisplay.x }} Y: {{ coordDisplay.y }} <br />
      经度: {{ coordDisplay.lng }} 纬度: {{ coordDisplay.lat }}
    </div>
  </div>
  <el-dialog v-model="csdialogVisible" title="Tips" width="500">
    <el-table :data="cstableData" style="width: 230px" border>
      <el-table-column type="index" width="50" />
      <el-table-column prop="id" label="id" width="180">
        <template #default="scope">
          <el-button type="primary" size="small" link @click.prevent="skipRow(scope.row)">
            {{ scope.row.id }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="csdialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="csdialogVisible = false">
          Confirm
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<!-- name: 'mapBox' -->
<script setup>
import DxfViewer from "./components/DxfViewer.vue";
import { apiUpdateProjectAttribute } from "./api/updateSysInfo";
import { mitts, btnFnObj } from "sgdraw/mittBus";
import { generateFileMD5 } from "@/util/index";
import { dxfZsObj, globalDxf, GrayColorType, screen2wcs } from "dxf-viewer";

// 地图同步
import {
  OpenlayerCad,
  getRedLinePAreas,
  olplistMovecamera,
  metaSearch,
} from "src/mapviewsync";
import { Transaction } from "dxf-viewer/src/global/transaction";
import { Vector2 } from "three";

// 导入fetchFileAsBlob函数
const fetchFileAsBlob = async (url) => {
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`HTTP 错误: ${response.status}`);
    const blob = await response.blob(); // 转换为 Blob 对象
    return blob;
  } catch (error) {
    console.error("请求失败:", error);
    throw error;
  }
};

const emits = defineEmits(["cadTexts", "exportList"]);

const coordDisplay = reactive({
  x: "0.000",
  y: "0.000",
  lng: "0.0000000",
  lat: "0.0000000",
});

const btnSet = reactive({
  hideCAD: true,
  hideMAP: true,
  hideTiff: true,
  hideTiffRes: true,
  hideCadRes: true,
  showDiff: false,
  grayCAD: true,
  ungrayCAD: true,
  checkView: true,
});

const dxfObj = reactive({
  layers: null,
  inputFile: null,
  isLocalFile: false,
  aboutDialog: false,
  urlDialog: false,
  inputUrl: null,
});
const mapViewers = ref(null);
const dxfUrl = ref(null);
const visMAP = ref(true);
const map = reactive({
  lng: 120,
  lat: 30,
  zoom: 1,
});
let cadmapviewer;
let cadapp;
const mapboxviewer = ref(null);
const resboxFlag = ref(false);
const resTotal = ref(0);
const identifyNum = ref(0);
const progress = ref(0);

const csdialogVisible = ref(false);
const cstableData = ref([]);

const projInf = {
  pid: "", //项目project ID
  urlCad: "maps/bbb.dxf",
  // urlTiff:'maps/map1',
  urlTiff:
    "https://ai-sh_lab.sungrow-re.com:3000/build-progress/file/splitImage/zhanhua",
  urlGeoJson: "",
  lnglat: [118.05, 37.54], //确定带号
  // lnglat: [118.059715270996094, 37.909835815429688], //确定带号
  pmin: {
    // mapcoord:{
    //   x:592960.8610,
    //   y:4198422.6456
    // },
    // position:{
    //   x:592960.8610,
    //   y:4198422.6456
    // }
  },
  pmax: {
    // mapcoord:{
    //   x:596533.0917,
    //   y:4196450.6613
    // },
    // position:{
    //   x:596533.0917,
    //   y:4196450.6613
    // }
  },
  // pmin pmax
};

mitts.on(btnFnObj.lsfn, async (file) => {
  resboxFlag.value = false;
  if (dxfUrl.value && dxfObj.isLocalFile) {
    URL.revokeObjectURL(dxfUrl.value);
  }
  let id = await generateFileMD5(file);
  dxfZsObj.drawingId = id;
  dxfObj.isLocalFile = true;
  dxfObj.inputFile = file;
  dxfUrl.value = URL.createObjectURL(file);
  globalDxf.filenam = "";
  await mapViewers.value?.Load(dxfObj.inputFile);
});

// 地图视图联动
const onLoadCadMap = () => {
  // console.log('加载地图视图联动组件...');
  resboxFlag.value = false;
  if (!cadmapviewer) {
    cadapp = mapViewers.value.GetViewer();
    cadmapviewer = new OpenlayerCad(mapboxviewer.value, cadapp);
    cadmapviewer.initMap(projInf);
  }
};
const extractData = async () => {
  // console.log(cadmapviewer.geoJson,'cadmapviewer.geoJson');

  let newda = await cadmapviewer.geoJson.features.map((e) => {
    return e.geometry.coordinates.map((pts) => {
      return pts.map((pt) => {
        let { x, y } = lnglat2wcs(...pt);
        // return {x:x+obj.x,y:y+obj.y}
        return { x, y };
      });
    });
  });
  // console.log(newda,'newda');

  let VPlList = new sg.vector_PolyLine2d();
  let wasmBulge = new sg.vector_double();
  newda.forEach((list) => {
    let pl = null;
    list.forEach((ptlist) => {
      let VPlist = new sg.vector_Point2d();

      ptlist.forEach((pts) => {
        VPlist.push_back(new sg.Point2d(pts.x, pts.y));
      });
      pl = new sg.PolyLine2d(VPlist, wasmBulge, false);
    });

    VPlList.push_back(pl);
  });
  return VPlList;
};
const lnglat2wcs = (lng, lat) => {
  return cadmapviewer.convertLnglat2Cad(lng, lat);
};

const locLngLat = () => {
  let { lng, lat } = map;
  let center = cadmapviewer.convertLnglat2Mapcoord(Number(lng), Number(lat));
  cadmapviewer.map.getView().setCenter(center);
  toMap();
};

const jumpTo = (lng, lat) => {
  map.lng = lng;
  map.lat = lat;
  locLngLat();
};

const showDiff = async () => {
  let sgBDG = Transaction.sgBDG;
  let VPlList = await extractData();
  let ppc = new sg.ProgressCheck(sgBDG, VPlList);
  let vstrlist = new sg.vector_string();
  let arrstr = ["Array_沾化-56-1", "Array_沾化-56-10513", "Array_沾化-28-1"];
  arrstr.forEach((str) => {
    vstrlist.push_back(str);
  });
  ppc.setPvBlocks(vstrlist);
  ppc.match();
  let total = ppc.getCADPvCount(); //总数
  let plist = ppc.getMatchCADIds();
  let size = plist.size();
  let ids = [];
  for (let i = 0; i < size; i++) {
    ids.push(plist.get(i));
    console.log(plist.get(i), "item");
  }
  let cad = mapViewers.value.GetViewer();
  cad.graymanage.includeIds = ids;
  resboxFlag.value = true;
  resTotal.value = total;
  identifyNum.value = size;
  let lsprogress = (size / total) * 100;
  progress.value = isNaN(lsprogress) ? 0 : lsprogress.toFixed(2);
};
const metaMatchNum = async (metaList) => {
  let sgBDG = Transaction.sgBDG;
  let VPlList = await extractData();
  let metaInfo = metaSearch({ sgBDG, VPlList, metaList });
  // console.log(metaInfo, "metaInfo");
  return metaSearch({ sgBDG, VPlList, metaList })
};
const exportList = () => {
  console.log('=============');
  emits('exportList')
}
const collision = async () => {
  let sgBDG = Transaction.sgBDG;
  let VPlList = await extractData();
  let col = new sg.RedLinePVCheck(sgBDG, "0428", VPlList); //sgBDG,'0428',VPlList
  col.match();

  let redLineAreas = col.getRedLineAreas();
  let redPlist = getRedLinePAreas(redLineAreas);
  function getLnglat(x, y) {
    let lsp = cadmapviewer.convertCad2Mapcoord(new Vector2(x, y));
    return cadmapviewer.convertCadLnglat([lsp.x, lsp.y]);
  }

  let lnglatlist = [];
  redPlist.forEach((item, index) => {
    let arr = item.map((vplist) => {
      return getLnglat(vplist[0], vplist[1]);
    });
    arr.push(arr[0]);
    lnglatlist.push({
      type: "Feature",
      properties: { id: index + 1 },
      geometry: {
        type: "LineString",
        coordinates: [arr],
      },
    });
  });
  let lsobj = {
    type: "FeatureCollection",
    features: lnglatlist,
  };
  cadmapviewer.redLineLayer(lsobj, "rgba(255, 0, 0, 0.5)");
  cadmapviewer._redLineAnimation = true;
  cstableData.value = [];
  function getIdx(idxs) {
    let idx = [];
    for (let i = 0; i < idxs.size(); i++) {
      idx.push(idxs.get(i));
    }
    return idx;
  }
  let idx1 = getIdx(col.getInRedAreaIndexs()); //红线里面 白色
  let idx2 = getIdx(col.getXRedAreaIndexs()); //红线相交  蓝色 列表
  let idx3 = getIdx(col.getOutRedAreaIndexs()); //外面  绿色  列表

  let ids1 = idx1.map(
    (idx) => cadmapviewer.geoJson.features[idx].properties.id
  );
  let ids2 = idx2.map(
    (idx) => cadmapviewer.geoJson.features[idx].properties.id
  );
  let ids3 = idx3.map(
    (idx) => cadmapviewer.geoJson.features[idx].properties.id
  );
  let points2 = idx2.map(
    (idx) => cadmapviewer.geoJson.features[idx].geometry.coordinates[0]
  );
  let points3 = idx3.map(
    (idx) => cadmapviewer.geoJson.features[idx].geometry.coordinates[0]
  );
  ids2.forEach((id, idx) => {
    let points = points2[idx];
    cstableData.value.push({
      id,
      points,
    });
  });
  ids3.forEach((id, idx) => {
    cstableData.value.push({
      id,
      points: points3[idx],
    });
  });
  console.log(cstableData, "cstableData");
  let idColorRanges = [
    { ids: ids1, color: "rgba(255, 255, 255, 0.5)" },
    { ids: ids2, color: "rgba(0, 0, 255, 0.5)" },
    { ids: ids3, color: "rgba(0, 255, 0, 0.5)" },
  ];

  cadmapviewer.setGeoJsonLayer(cadmapviewer.geoJson, idColorRanges);
  const animate = () => {
    if (!cadmapviewer._redLineAnimation) return;
    cadmapviewer._redLineLayer?.changed();
    requestAnimationFrame(animate);
  };
  animate();
};
const skipRow = (item) => {
  let points = item.points.map((item) => {
    let p = lnglat2wcs(item[0], item[1]);
    return { x: p.x, y: p.y };
  });
  olplistMovecamera({ plist: points, viewer: cadapp, cadmapviewer });
};
const csbtnlist = () => {
  csdialogVisible.value = true;
};
const hideMAP = () => {
  visMAP.value = !visMAP.value;
};
const hideCAD = () => {
  mapViewers.value.hideCAD();
};
const grayCAD = async () => {
  let cad = mapViewers.value.GetViewer();
  cad.graymanage.grayType = GrayColorType.Include;
  cad.rebuildScene();
};
const ungrayCAD = async () => {
  let cad = mapViewers.value.GetViewer();
  cad.graymanage.grayType = GrayColorType.Exclude;
  cad.rebuildScene();
};
const maptest = () => {
  cadmapviewer.test();
};
const toMap = () => {
  cadmapviewer.syncViewToMap();
};
const toCAD = () => {
  cadmapviewer.syncViewToCad();
};
const moveCAD = () => {
  btnSet.checkView = !btnSet.checkView;
  btnSet.checkView ? cadmapviewer.checkView() : cadmapviewer.freezeMap();
};
const hideTiff = () => {
  cadmapviewer.hideTiff(btnSet.hideTiff);
};
// 调用后端接口新增key和value
const addKeyAndValue = (dataObject) => {
  //TODO: 调用后端接口新增key和value
  console.log("调用后端接口新增key和value---入参", dataObject);
  return new Promise((resolve, reject) => {
    apiUpdateProjectAttribute(dataObject)
      .then((res) => {
        console.log("调用后端接口新增key和value---出参", res);
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

/**
 * 将复杂对象转换为接口所需的字符串格式
 * @param obj 要转换的对象
 * @returns 符合接口要求的字符串
 */
const objectToAttributeValue = (obj) => {
  // 使用 JSON.stringify 将对象转换为字符串
  return JSON.stringify(obj);
};

/**
 * 构建符合接口要求的参数
 * @param projectCode 项目代码
 * @param pmin pmin对象
 * @param pmax pmax对象
 * @returns 符合接口要求的请求参数
 */
const buildAttributeParams = (projectCode, pmin, pmax) => {
  return {
    attributeList: [
      {
        attributeKey: "pmin",
        attributeValue: objectToAttributeValue(pmin),
      },
      {
        attributeKey: "pmax",
        attributeValue: objectToAttributeValue(pmax),
      },
    ],
    projectCode,
  };
};

// 加载项目数据
const handleData = async (data, hasCoords = true, hasPminPmax = false) => {
  nextTick(async () => {
    projInf.pid = data.code;
    // projInf.urlCad = data.cadUrl;
    projInf.urlTiff = data.splitImagePrefix;
    projInf.urlShp = "";
    projInf.lnglat = [data.longitude, data.latitude];
    projInf.urlGeoJson = data.recognitionResult;
    projInf.offset = data.offset
      ? new Vector2(data.offset[0], data.offset[1])
      : new Vector2(0, 0);
    // 设置pmin和pmax (如果存在)
    if (data?.pmin && data?.pmax) {
      projInf.pmin = data.pmin;
      projInf.pmax = data.pmax;
      hasPminPmax = true;
    } else {
      hasPminPmax = false;
    }

    resboxFlag.value = false;

    if (!cadmapviewer) {
      cadapp = mapViewers.value.GetViewer();
      cadmapviewer = new OpenlayerCad(mapboxviewer.value, cadapp);

      if (hasPminPmax) {
        console.log("存在pmin和pmax，直接使用");

        await cadmapviewer.initMap(projInf, hasCoords, hasPminPmax);
      } else {
        console.log("不存在pmin和pmax，加载CAD后获取");
        // 先加载CAD文件
        let cadbuff = await fetchFileAsBlob(projInf.urlCad);
        await cadapp.Load({
          url: cadbuff,
          progressCbk: () => {
            console.log("------------------>", "cad加载完成");
          },
        });

        // 获取pmin和pmax
        const pRange = await cadmapviewer.getPRange();
        if (pRange && pRange.pmin && pRange.pmax) {
          projInf.pmin = pRange.pmin;
          projInf.pmax = pRange.pmax;
          console.log("获取到pmin和pmax", projInf.pmin, projInf.pmax);
        } else {
          console.error("获取pmin和pmax失败");
        }

        // 初始化地图
        await cadmapviewer.initMap(projInf, hasCoords, false);

        // 调用接口保存pmin和pmax
        if (projInf.pmin && projInf.pmax) {
          const params = buildAttributeParams(
            data.code,
            projInf.pmin,
            projInf.pmax
          );
          console.log("调用接口---params", params);
          try {
            const res = await addKeyAndValue(params);
            if (res.code === 200) {
              console.log("调用后端接口新增key和value成功");
            } else {
              console.log("调用后端接口新增key和value失败");
            }
          } catch (error) {
            console.error("调用后端接口新增key和value出错", error);
          }
        }
      }

      //TODO: 后期在考虑，是否存在经纬度，获取CAD文本，并调用AI工作流，获取经纬度，并保存
      if (!hasCoords) {
        emits("cadTexts", cadmapviewer.cadTexts);
      }
    }
  });
};

const mouseMoveCoord = (event) => {
  if (!cadapp || !cadmapviewer) return;

  const e = {
    offsetX: event.offsetX,
    offsetY: event.offsetY,
    target: event.target,
  };

  const cadCoord = screen2wcs(e, cadapp.camera);
  const mapcoord = cadmapviewer.convertCad2Mapcoord(
    new Vector2(
      cadCoord.x + globalDxf.wcsOff.x,
      cadCoord.y + globalDxf.wcsOff.y
    )
  );
  const lnglat = cadmapviewer.convertCadLnglat([mapcoord.x, mapcoord.y]);

  coordDisplay.x = mapcoord.x.toFixed(3);
  coordDisplay.y = mapcoord.y.toFixed(3);
  coordDisplay.lng = lnglat[0].toFixed(7);
  coordDisplay.lat = lnglat[1].toFixed(7);
};

defineExpose({
  onLoadCadMap,
  handleData,
  jumpTo,
  metaMatchNum,
});
</script>

<style scoped>
.mapBox {
  width: 100vw;
  /* height: 100vh; */
  height: calc(100vh - 160px);
  /* background-color: hsl(218, 17%, 27%); */
  overflow: hidden;
  position: relative;

  .btn {
    display: block;
    position: relative;
    height: 31px;
    opacity: 0.8;
    cursor: pointer;
    color: #fff;
    background-image: url(img/btn.jpeg);
    background-position: center center;
    /* 或者直接写 center */
    background-repeat: no-repeat;
    /* 防止图像重复 */
    background-size: cover;
    /* 根据你的需求调整图片大小，也可以使用 contain */
    font-size: 12px;
    text-align: center;
    line-height: 31px;
    margin-right: 10px;
    border-radius: 10px;
    min-width: 80px;
    padding: 0px 10px;

    &:hover {
      color: rgb(42, 202, 202);
      border-radius: 10px;
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
    }
  }

  .resbox {
    position: absolute;
    top: 80px;
    left: 50px;
    width: 200px;
    height: 200px;
    padding: 10px;
    background-color: #7fa9d3;
    border-radius: 10px;
    z-index: 10003;
    opacity: 0.75;

    .information {
      width: 100%;
      font-size: 20px;
      color: #fff;
      line-height: 40px;
      display: flex;
      justify-content: space-between;
    }
  }

  .view {
    width: 100vw;
    /* height: calc(100vh - 80px); */
    height: 100vh;
    position: relative;

    .mapboxviewer {
      position: absolute;
      top: 0px;
      left: 0px;
      height: 100%;
      width: 100%;
      z-index: 999;
    }

    .control_btns {
      position: absolute;
      z-index: 1003;
      display: flex;
    }

    .mapbox {
      top: 10px;
      left: 50px;
    }

    .cad {
      display: flex;
      flex-direction: column;
      top: 50px;
      right: 25px;
      border-radius: 8px;
      padding: 10px;
      background-color: #f3f2ff;
      opacity: 0;
      transition: opacity 0.5s ease;

      &:hover {
        opacity: 0.8;
      }
    }

    .llh {
      top: 10px;
      right: 10px;
      column-gap: 10px;
      align-items: center;
    }
  }
}

.centercross {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  height: 40px;
  width: 40px;
  background-image: url("img/cross.png");
  z-index: 1100;
  background-color: rgba(113, 153, 153, 0.611);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  pointer-events: none;
}

.coordinate-display {
  position: absolute;
  left: 10px;
  bottom: 10px;
  color: rgb(15, 15, 15);
  font-size: 15px;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: monospace;
  z-index: 1000;
  pointer-events: none;
}
</style>
