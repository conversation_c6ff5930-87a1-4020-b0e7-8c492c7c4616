﻿import { DxfParseScene } from "./DxfParseScene"
import { Batch } from "../batch"
import { Scene } from "three";
import { textRender } from "../entities";

export class DxfTransientSceneWasm extends DxfParseScene {
    // scene realated
    private scene: Scene;
    constructor(viewer) {
        super(viewer);
        this.scene = new Scene();
    }

    public _LoadBatch( batch) {
        const objects = new Batch(this.viewer, batch).CreateObjects()
        for (const obj of objects) {
            this.scene.add(obj)
        }
    }

    public draw(objs){
        if(!(objs instanceof Array)){
            objs=[objs]
        }
        objs.forEach(obj=>{
            this._ProcessWasmEntity(obj)
        })
        let sceneDa = this._BuildSceneDa()
        if (!sceneDa) return;

        this.batches.each(b=>{
            this._LoadBatch(b)
        })
    }
    public getScene() {
        return this.scene;
    }
    public clear(){
        this.scene.clear()
        // this.batches.clear()
        // this.bounds=null
        super.clear();
    }
}