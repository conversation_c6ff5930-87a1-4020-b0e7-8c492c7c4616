export enum ViewMod{
    None='None',
    Pick="Pick",//选择
    Draw='Draw',//绘制
    Edit='Edit',//图元编辑
}


/**
 * KeyboardEvent 
 * key:string  本项目后续统一采用,OrbitControls不做修改
 * code:string
 * keycode:number ts提示不建议使用
 */
export enum KeyboardKey{
    Enter='Enter',
    Escape='Escape',
    Delete='Delete',
    Ctrl='Control',
    Space=' ',
    Shift='Shift'
}
export enum KeyboardCode{
    Escape='Escape',
    Delete='Delete',
    Space='Space',
    ControlLeft='ControlLeft',
    ControlRight='ControlRight',
    ShiftLeft='ShiftLeft',
    ShiftRight='ShiftRight',
    Enter='Enter',

}
export enum KeyCode{
    Shift=16,
    Ctrl=17,
    LEFT= 37,
    UP= 38, 
    RIGHT= 39, 
    BOTTOM= 40, 
    DELETE=46, 
    ESCAPE=27
}

export enum MouseButton{
    Left=0,
    Middle=1,
    Right=2,
    None=-1
}

export enum CADEvents{
    LButtonDown="LButtonDown",
    LButtonUp="LButtonUp",
    RButtonDown="RButtonDown",
    RButtonUp="RButtonUp",
    MouseMove="MouseMove",
    KeyDown="KeyDown",
    KeyUp="KeyUp",
    Wheel="Wheel"
}

export enum GraphicType{
    None='None',
    Polyline='Polyline',
    Line='Line',
    Circle='Circle',
    Rect='Rect',
    Polygon='Polygon',
    Arc='Arc',
    RevCloud='RevCloud',
    Ellipse='Ellipse',
    Bspline='Bspline',
    Text='Text',
    MText='MText',
    AlignedDimension='AlignedDimension',
    RotatedDimension='RotatedDimension',
    Fence='Fence',
    Block='Block',
    Mirror="Mirror",
    Extend="Extend",
    Trim="Trim",
    Offset="Offset",
    Fillet="Fillet",
    RectangularRange="RectangularRange",
    PolygonRange="PolygonRange",
    Scale='Scale',
    Producepicture='Producepicture',
    EllipticalArc='EllipticalArc',
    move='move',
}