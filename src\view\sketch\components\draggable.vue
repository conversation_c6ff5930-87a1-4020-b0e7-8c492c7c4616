<template>
  <div class="dialog" ref="dialogRef">
    <header class="dialog-header" ref="headerRef">
      <slot name="title"></slot>
    </header>
    <div class="close" @click="btnclose">X</div>
    <div class="dialog-body">
      <slot name="body"></slot>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';

const emits = defineEmits(['draggableClose'])

const dialogRef = ref(null);
const headerRef = ref(null);

let isDragging = false;
let initialX = 0;
let initialY = 0;

const startDragging = (e) => {
  isDragging = true;
  initialX = e.clientX - dialogRef.value.offsetLeft;
  initialY = e.clientY - dialogRef.value.offsetTop;
};

const dragElement = (e) => {
  if (!isDragging) return;
  dialogRef.value.style.left = `${e.clientX - initialX}px`;
  dialogRef.value.style.top = `${e.clientY - initialY}px`;
};

const endDragging = () => {
  isDragging = false;
};
const btnclose=()=>{
  emits('draggableClose')
}

onMounted(() => {
  // 初始化位置
  dialogRef.value.style.position = 'absolute';
  dialogRef.value.style.right = '340px';
  dialogRef.value.style.top = '100px';


  // 绑定事件监听器
  headerRef.value.addEventListener('mousedown', startDragging);
  document.addEventListener('mousemove', dragElement);
  document.addEventListener('mouseup', endDragging);

  // 清理函数
  const cleanup = () => {
    if (headerRef.value) {
      headerRef.value.removeEventListener('mousedown', startDragging);
    }
    document.removeEventListener('mousemove', dragElement);
    document.removeEventListener('mouseup', endDragging);
  };

  // 在组件卸载时清理事件监听器
  onUnmounted(cleanup);
});
</script>

<style scoped>
.dialog {
  width: 350px;
  min-height: 200px;
  border: 1px solid #ccc;
  background-color: white;
}

.dialog-header {
  position: relative;
  padding: 10px;
  cursor: move;
  user-select: none; /* 阻止文本选择 */
}
.close {
   position: absolute;
   right: 5px;
   top: 5px;
   cursor: pointer;
   padding:5px 10px;
   &:hover{
    background-color: #ecf5ff;
   }
 }
.dialog-body {
  padding: 10px;
}
</style>