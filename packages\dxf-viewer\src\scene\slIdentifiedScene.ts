import { Scene ,Group,Object3D} from "three"
import { DynamicSceneType, WasmObjType } from "../constant"
import { Batch } from "../batch"
import { DxfParseScene } from "./DxfParseScene"
import { PubSub } from "emitter"
import { globalDxf, HighLMod, pickupInfo,dxfZsObj,handleidentifiedObj,dxflayers } from 'dxf-viewer'
import { Block } from "../entities/Block"
import { BatchEntityType } from '../constant'
import { Block as Block2 } from "../viewer/block"
import { splitBlockId } from "dxf-viewer/src/libs"


export class SlIdentifiedScene extends DxfParseScene{
    public identifiedGp:any
    public ids:number[]
    public entIds:Set<number>
    public scenetype='SlIdentifiedScene'
    public scene
    public lygps
    public sllayers:any
    constructor(viewer){
        super(viewer)
        this.ids = []
        this.entIds=new Set()
        // this.identifiedScene = new Scene()
        this.scene=new Scene()
        this.lygps = new Map()
        
    }

    public async createdGp(){
        this.identifiedGp=new sg.SGBaseDrawingGroup()
        let obj=handleidentifiedObj()
        // return
        let blockArr:any=[]
        let lsblockglsArr:any=[]
        if(obj.elements && obj.elements.length) {
            obj.elements.forEach(item=>{
                let lyobj={
                    bkName:item.materialComponentName,
                    bklist:[], 
                }
                item.idList.forEach(ite=>{
                    if(!ite.ids.length) return
                    let {entityId,blockId}=splitBlockId(ite.ids[0])
                    let obj:any=null
                    let scale:any=null
                    let bkName=item.materialComponentName+'%*'+ ite.hashCodeGraph
                    let bk:any=null
                    let mid0:any=null
                    if(blockId) {
                        let vlist=new sg.vector_int
                        vlist.push_back(ite.ids[0])
                        console.log(blockId,'blockIdblockIdblockId');
                        
                        let ele0 = new sg.ElementObj(vlist);
                        mid0 = sg.getElementObjRect(dxfZsObj.drawingId,ele0).getMiddlePoint()
                        console.log(mid0.x(),'mid0mid0mid0');
                        
                        obj= this.viewer.transaction.gpObj.getInsertBaseObjById(blockId,entityId)
                        let kobj1= this.viewer.transaction.gpObj.getInsertBlockById(blockId)
                        scale=kobj1.getXScale()
                    }else {
                        obj= this.viewer.transaction.gpObj.getPBaseObjById(ite.ids[0])
                        mid0= obj.getBoundingBox().getMiddlePoint()
                    }
                    const newobj=obj.copyNewOne()
                    if(blockId) {
                        let m2 = newobj.getBoundingBox().getMiddlePoint();
                        let ma = new sg.Matrix3();
                        ma.move(mid0.x() - m2.x(),mid0.y()-m2.y());
                        newobj.transform(ma);
                    }
                    // newobj.setColor(new sg.SGColor(255,0,0,255))
                    newobj.setColor(new sg.SGColor(245,154,35,255))
                    
                    bk=new sg.SGBlock(bkName,new sg.Point2d(0,0),true)
                    bk.addBaseObj(newobj)

                    lyobj.bklist.push(bkName)

                    
                    ite.ids.forEach(id=>{
                        let ibk=new sg.InsertSGBlock()
                        let str=id+'&&'
                        ibk.writeStringXData('id',str) //写入id
                        let {entityId,blockId}=splitBlockId(id)
                        let obj:any=null
                        let czb:any=null
                        
                        if(blockId) {
                            let vlist2=new sg.vector_int
                            vlist2.push_back(id)

                            let ele1 = new sg.ElementObj(vlist2);
                            czb = sg.getElementObjRect(dxfZsObj.drawingId,ele1).getMiddlePoint()
                        }else {
                            obj= this.viewer.transaction.gpObj.getPBaseObjById(id)
                            czb=obj.getBoundingBox().getMiddlePoint()//中心点坐标
                            
                        }
                        ibk.setInsertBlockName(bkName)
                        if(blockId) {
                            ibk.setInsertBlockInsertPoint(new sg.Point2d(czb.x()-mid0.x(),czb.y()-mid0.y()))
                            ibk.setInsertBlockScale(scale,scale)
                        }else {
                            ibk.setInsertBlockInsertPoint(new sg.Point2d(czb.x()-mid0.x(),czb.y()-mid0.y()))
                        }
                        
                        
                        blockArr.push({
                            block:bk,
                            InsertBlock:ibk
                        })
                    })
                })
                lsblockglsArr.push(lyobj)
            })
            
        }
        if(obj.groups && obj.groups.length) {
            obj.groups.forEach((item,index)=>{
                let lyobj={
                    bkName:item.materialComponentName,
                    bklist:[], 
                }
                item.graphGroups.forEach(ite=>{
                    let bkName=item.materialComponentName+'%*'+ ite.hashCodeGraph
                    lyobj.bklist.push(bkName)
                    let bk:any=new sg.SGBlock(bkName,new sg.Point2d(0,0),true)
                    let czb3:any=null
                    let scale2:any=null
                    if(ite.idList.length && index===0) {
                        let vlist=new sg.vector_int
                        let isblock=false
                        let objlist:any=[]
                        let bdx=new sg.Rect2d()

                        ite.idList[0].ids.forEach(id=>{
                            let {entityId,blockId}=splitBlockId(id)
                            let obj:any=null
                            if(blockId) {
                                vlist.push_back(id)
                                isblock=true
                                obj= this.viewer.transaction.gpObj.getInsertBaseObjById(blockId,entityId)
                                let kobj1= this.viewer.transaction.gpObj.getInsertBlockById(blockId)
                                scale2=kobj1.getXScale()
                            }else {
                                obj= this.viewer.transaction.gpObj.getPBaseObjById(id)
                            }
                            const newobj=obj.copyNewOne()
                            let temprect = newobj.getBoundingBox()
                            bdx.add(temprect)
                            // newobj.setColor(new sg.SGColor(255,0,0,255))
                            newobj.setColor(new sg.SGColor(245,154,35,255))
                            objlist.push(newobj)

                        })
                        let midpt = bdx.getMiddlePoint();
                        if(isblock) {
                            let ele0 = new sg.ElementObj(vlist);
                            czb3 = sg.getElementObjRect(dxfZsObj.drawingId,ele0).getMiddlePoint()
                            let ma = new sg.Matrix3();
                            ma.move(czb3.x()-midpt.x(),czb3.y()-midpt.y())
                            objlist.forEach(element => {
                                element.transform(ma)
                            });
                        }else {
                            czb3=midpt
                        }
                        objlist.forEach(element => {
                            bk.addBaseObj(element)
                        });
                    }

                    ite.idList.forEach(it=>{
                        let ibk=new sg.InsertSGBlock()
                        let str:any=''
                        let isblock2=false
                        ibk.setInsertBlockName(bkName)
                        it.ids.forEach(i=>{
                            let {entityId,blockId}=splitBlockId(i)
                            if(blockId) {
                                isblock2=true
                            }
                            str =str+i+'&&'
                        })
                        if(isblock2) {
                            ibk.setInsertBlockScale(scale2,scale2)
                        }
                        ibk.setInsertBlockInsertPoint(new sg.Point2d(it.middlePoint.x-czb3.x(),it.middlePoint.y-czb3.y()))
                        ibk.writeStringXData('id',str)
                        blockArr.push({
                            block:bk,
                            InsertBlock:ibk
                        })
                    })
                })
                lsblockglsArr.push(lyobj)
            })
        }
        pickupInfo.blockgls=this.mergeBkLists(lsblockglsArr)
        
        blockArr.forEach(e=>{
          if(!this.identifiedGp.isExistBlockName(e.block)) {
            this.identifiedGp.addBlock(e.block)
          }
          this.identifiedGp.addInsertBlock(e.InsertBlock)
          this.identifiedGp.calSpInsertBlockObjList(e.InsertBlock)
        })
        this.gpErgodic(this.identifiedGp) 

        await this.processEntityBygp(this.identifiedGp)
        await this.generateBlock()
        this.loadBatch()

        //  console.log(dxflayers.layers,'dxflayersdxflayers');
    }

    getScene(){
        return this.scene
    }
    async generateBlock(){
        this.batches.each(batch=>{
            if (batch.key.blockName !== null &&
                batch.key.geometryType !== BatchEntityType.BLOCK_INSTANCE &&
                batch.key.geometryType !== BatchEntityType.POINT_INSTANCE) {
                let block = this.viewer.blocks.get(batch.key.blockName)
                if (!block) {
                    block = new Block2()
                    this.viewer.blocks.set(batch.key.blockName, block)
                }
                block.PushBatch(new Batch(this.viewer, batch))
            }
        })
    }
    

    async processEntityBygp(identifiedGp){
        let gp=identifiedGp
        // 2. process blocks in current dxf
        const topBlockCount = gp.blockCount();
        for (let i = 0; i < topBlockCount; i++) {
            const sgBlock = gp.getBlock(i);
            const blockName = sgBlock.getName();
            const blockEntityCount = sgBlock.count();
            const insertCount = sgBlock.insertBlockCount();
            // console.log(blockEntityCount,insertCount)
            if (blockEntityCount + insertCount > 0) { //  (blockInsertCount[blockName] > 0)
                const basePoint = sgBlock.getBasePoint();
                let newBlock = new Block({
                    position : {
                        x : basePoint.x(),
                        y : basePoint.y()
                    },
                    name : blockName, 
                    sgBlock : sgBlock
                });
                this.blocks.set(blockName, newBlock);
                // for each block in use, process entity with block's definition context
                let defContext = newBlock.DefinitionContext();
                for (let j = 0; j < blockEntityCount; j++) {
                    let a = sgBlock.getBaseObj(j);
                    a.getDrawObjectId();
                    let entityId = a.getDrawObjectId();
                    try{
                        await this._ProcessWasmEntity(a, defContext);
                    } catch(error){
                        console.error("error with entity id: " + entityId + " in block " + i + " . Error code: " + error)
                    }
                }
                
                for (let j = 0; j < insertCount; j++) {
                    let a = sgBlock.getInsertBlock(j);
                    try{
                        this._ProcessInsertEntity(a, defContext);
                    } catch(error){
                        console.error("error with entity in block " + j + " . Error code: " + error)
                    }
                }
            }
        }

        // 3. for each inserts, process it with block's instance context
        const gpCount = gp.count()
        // console.log('图元数量:',gpCount)
        for (let i = 0; i < gpCount; i++) {
            let a = gp.getBaseObj(i);
            await this._ProcessWasmEntity(a)
        }
        this._BuildSceneDa()
    }



    public loadBatch(){
        this.batches.each(batch=>{
            this._LoadBatch(batch)
        })
        pickupInfo.blockgls.forEach(item=>{
            let trgp=new Group()
            trgp.name = item.bkName
            item.bklist.forEach(ite=>{
                let gp=this.lygps.get(ite)
                trgp.add(gp)
            })
            dxflayers.layers.push({
                color:'red',
                name: item.bkName,
                displayName:item.bkName,
                isVisible:true,
                isSLidentifiedflag:true,
            })
            this.scene.add(trgp)
        })
    }
    public _LoadBatch( batch) {
        // console.log(batch,'batchbatchbatch');
        
        if (batch.key.blockName !== null &&
            batch.key.geometryType !== BatchEntityType.BLOCK_INSTANCE &&
            batch.key.geometryType !== BatchEntityType.POINT_INSTANCE) {
            /* Block definition. */
            return
        }
        // const objects = new Batch(this.viewer, this.sceneDa, batch).CreateObjects()
        const objects = new Batch(this.viewer,  batch).CreateObjects()
        let trgp=new Group()
        for (const obj of objects) {
            trgp.add(obj)
        }
        this.lygps.set(batch.key.blockName, trgp)

        // debugger
        // let trgp=new Group()
        // trgp.name = batch.key.blockName
        // dxflayers.layers.push({
        //     color:'red',
        //     name: batch.key.blockName,
        //     displayName:batch.key.blockName,
        //     isVisible:true,
        //     isSLidentifiedflag:true,
        // })

        
        // for (const obj of objects) {
        //     //创建three里面的 Group 放入obj ，通过管控 Group达到图层管理效果
        //     trgp.add(obj)
        // }
        // this.scene.add(trgp)
        
    }

    public layersVisible(name,hide){
        this.scene.traverse(function (child) {
            if (child.name === name) {
                child.visible=hide
            }
        });
    
        this.viewer.Render();
    }
    public layersALLVisible(hide){
        this.scene.traverse(function (child) {
            child.visible=hide
        });
    
        this.viewer.Render();
    }

    public gpErgodic(identifiedGp){
        let selectIds=pickupInfo.pickupIdlist
        const vecint = new sg.vector_int
        let insertCount=identifiedGp.insertBlockCount()
        for (let j = 0; j < insertCount; j++) {
            let ibk = identifiedGp.getInsertBlock(j);
            let arr=this.parseString(ibk.getStringXData('id'))
            // console.log(arr,'ibkibk');
            let missing = false;
            selectIds.some(item => {
                if (!arr.includes(item)) {
                    missing = true;
                    return true; // 停止 some 循环
                }
            });
            if(missing) {
                vecint.push_back(ibk.getDrawObjectId())
            }
            
        }
    }

    private parseString(str) {
        return str.split('&&').map(s => s.trim()).filter(Boolean).map(Number);
    }
    private mergeBkLists(items) {
        return items.reduce((acc, item) => {
            const existingItem = acc.find(x => x.bkName === item.bkName);
            
            if (existingItem) {
                // 如果已存在相同的bkName，合并bklist
                existingItem.bklist.push(...item.bklist);
            } else {
                // 如果不存在，添加新的项
                acc.push({ bkName: item.bkName, bklist: [...item.bklist] });
            }
            
            return acc;
        }, []);
    }

    override clear(){
        this.scene.clear()
        // clear structures from parent
        this.sceneDa = null
        this.batches.clear()
        this.layers.clear()
    }
}