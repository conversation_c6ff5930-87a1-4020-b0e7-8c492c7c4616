import { Vector3 } from "three"
import { screen2wcs, wcs2screen } from "../controls"
import { DxfViewer } from "../viewer"

export interface ITipInfo{
    tip:string,
    refPoint?:Vector3,
    pos?:any,
    length?:any,
    radius?:any,
}
export const globalTip=reactive({
    tipconf:{
        valid:false,
        istip:true,
        isPos:false,
        isLengthTip:false,
        isRadiusTip:false,
        tipmsg:'选择对象',
        inputPosX:'',
        inputPosY:'',
        inputLen:100,
        inputposLen:{x:0,y:60},
        inputRad:'100',
        inputposRad:{x:0,y:60},
        inputIdx:1,
        refPoint:new Vector3()
    }
})
const axisX=new Vector3(1,0,0)
export class PopTips{//弹窗
    public refPoint:Vector3=new Vector3()
    constructor(){

    }
    public tip(opt:ITipInfo){
        const tipconf=globalTip.tipconf
        tipconf.istip=true
        tipconf.tipmsg=opt.tip
        tipconf.isPos='pos' in opt;
        tipconf.isLengthTip='length' in opt;
        tipconf.isRadiusTip='radius' in opt;
        ('refPoint' in opt)&&(tipconf.refPoint = opt.refPoint!)
    }
    public dispose(){
        Object.assign(globalTip.tipconf,{
            istip:false,
            isPos:false,
            isLengthTip:false,
            isRadiusTip:false,
        })
    }
    public onmousemove(event:PointerEvent,viewer:DxfViewer){
        let refPoint=globalTip.tipconf.refPoint
        let wcs=screen2wcs(event,viewer.camera)
        let mid=wcs.clone().add(refPoint).multiplyScalar(0.5)
        let pos=wcs2screen(mid,viewer.camera,viewer.canvas)
        globalTip.tipconf.inputposLen.x=pos.x
        globalTip.tipconf.inputposLen.y=pos.y
        globalTip.tipconf.inputLen=wcs.distanceTo(refPoint)
        let v12=wcs.clone().sub(refPoint)
        let ang=Math.atan2(v12.y,v12.x)*180/Math.PI
        // ang=ang<0?ang+360:ang
        let mid2=new Vector3(wcs.x,mid.y,0)
        let pos2=wcs2screen(mid2,viewer.camera,viewer.canvas)
        globalTip.tipconf.inputRad=ang.toFixed(2)
        globalTip.tipconf.inputposRad.x=pos2.x
        globalTip.tipconf.inputposRad.y=pos2.y
    }
}
export var poptip=new PopTips()