import sgddFullJS from "./SGDrawingDesign.js";
import sgddFullWasm from "./SGDrawingDesign.wasm";
// import sgddFullJS from "PriumCAD/cad-wasm/wasmloader/SGDrawingDesign.js";
// import sgddFullWasm from "PriumCAD/cad-wasm/wasmloader/SGDrawingDesign.wasm";\

// let myEvent = new CustomEvent("wasm",{
//   detail:{name:"wangpingan"}
// });

const loadSGD = ({
  mainJS = sgddFullJS,
  mainWasm = sgddFullWasm,
  worker = undefined,
} = {}) => {
  return new Promise((resolve, reject) => {
    mainJS({
      locateFile(path) {
        if (path.endsWith('.wasm')) {
          return  "sgdraw/sgdd/SGDrawingDesign.wasm";
        }
        if (path.endsWith('.worker.js') && !!worker) {
          return worker;
        }
        return path;
      },
      // ...module
    }).then(async sgdd => {
      // console.log(sgdd)
      resolve(sgdd);
    });
  });
};

export async function initSGD() {
  let sg=await loadSGD()
  window.sg=sg
  // new sg.DrawingPatternManager.init()
  // sg.initSGDrawing('font\\')
  sg.initSGDrawing('font/')

  window.postMessage({ type: 'Wasm', payload: 'Loading completed' }, '*');

  // window.dispatchEvent(myEvent);

  // exInit(sg)
}

function exInit(sg){
  console.log(sg)

  let m=sg.fibonacci(10)
  
}




