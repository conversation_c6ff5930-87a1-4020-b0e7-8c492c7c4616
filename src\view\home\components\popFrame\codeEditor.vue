<template>
    <div class="code_editor">
        <div class="head">
            <div class="title">代码编辑器</div>
            <svg-icon iconClass="run" w=20 h=20 style="margin-top: -2px;" @click="runScript"></svg-icon>
            <div class="close" @click="onClose">X</div>
        </div>
        <div class="code_editor_body">
            <Codemirror v-model:value="code" :options="cmOptions" border ref="cmRef" height="100%" @change="onChange"
                @input="onInput" @ready="onReady" :border="true" class="cm-component"></Codemirror>
        </div>
    </div>
</template>

<script setup>
import "codemirror/mode/javascript/javascript.js"
import Codemirror from "codemirror-editor-vue3"
// import { oneDark } from "@codemirror/theme-one-dark";
import "codemirror/theme/xq-dark.css"
import "codemirror/theme/ayu-dark.css"
import "codemirror/theme/material-palenight.css"

import { PubSub } from 'emitter';
import { blockBox, viewlayout } from 'dxf-viewer'
const onClose = () => {
    viewlayout.visCodeEditor = false
}
const code = ref(`

import { a } from './a.js'
console.log(a)
var b = new sg.Point2d(1, 2)
console.log(b)
var i = 0;
for (; i < 9; i++) {
    console.log(i);
}
/*测试折叠*/
function findSequence(goal) {
    function find(start, history) {
        if (start == goal)
            return history;
        else if (start > goal)
            return null;
        else
            return find(start + 5, "(" + history + " + 5)") ||
                find(start * 3, "(" + history + " * 3)");
    }
    return find(1, "1");
}
`)
const cmRef = ref()
const cmOptions = {
    mode: "javascript",
    theme: "material-palenight",
    readOnly: false,
    lineNumbers: true,
    lineWiseCopyCut: true,
    // gutters: ["CodeMirror-lint-markers"],
    gutters: ["tomorrow-night-bright"],
    lint: true,
    // extensions: [oneDark]
}
const onChange = (val, cm) => {
    console.log(val)
    console.log(cm.getValue())
}

const onInput = (val) => {
    console.log(val)
}

const importScript = async (url, callback) => {
    let say = await import(url)
    console.log(say)
}
const loadScript = (url, callback) => {
    var script = document.createElement('script');
    script.type = "module";
    if (script.readyState) {//IE兼容
        script.onreadystatechange = function () {
            if (script.readyState == "loaded"
                || script.readyState == "complete") {
                script.onreadystatechange = null;
                callback();
            }
        };
    } else {//其他浏览器
        script.onload = function () {
            callback();
        };
    }
    script.innerHTML = url;
    document.getElementsByTagName('head')[0].appendChild(script);
}
const runScript = () => {
    loadScript(code.value)
}

const onReady = (cm) => {
    console.log(cm.focus())
}
</script>

<style lang="scss" scoped>
.code_editor {
    width: 500px;
    height: 100%;
    flex: 0 0 auto;
    background-color: #575655;

    .head {
        display: flex;
        justify-content: space-between;

        .title {
            flex: auto;
            text-align: center;
        }

        .close {
            width: 30px;
            text-align: center;
            font-weight: 500;
            padding: 0px 8px 5px 8px;
            cursor: pointer;

            &:hover {
                background-color: #57aaff;
            }
        }
    }

    .code_editor_body {
        height: calc(100% - 25px);

        // background: red;
        .cm-component {
            font-family: monospace;
            line-height: 20px;
            font-size: 18px;
        }

    }
}
</style>