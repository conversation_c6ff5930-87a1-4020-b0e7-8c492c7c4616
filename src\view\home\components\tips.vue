<template>
  <div style="pointer-events: none;">
    <div class="cadtips f14 b1" :style="styCadTip" v-if="tipconf.istip && tipconf.valid">
      <div class="">{{ tipconf.tipmsg }}</div>
      <el-input class="cadinput" type="text" v-if="tipconf.isPos" v-model="tipconf.inputPosX" ref="tippos1"
        autocomplete=“off”></el-input>
      <el-input class="cadinput" type="text" v-if="tipconf.isPos" v-model="tipconf.inputPosY" ref="tippos2"
        autocomplete=“off”></el-input>
    </div>
    <div class="cadtips b1" :style="styInputLen" v-if="tipconf.isLengthTip">
      <el-input class="cadinput" type="text" v-model="tipconf.inputLen" ref="tipInputLen" autocomplete=“off”></el-input>
    </div>
    <div class="cadtips b1" :style="styInputRad" v-if="tipconf.isRadiusTip">
      <div class="">{{ tipconf.inputRad }}°</div>
    </div>
  </div>


</template>

<script setup>
import { globalDxf, globalTip } from 'dxf-viewer'
const tippos1 = ref()
const tippos2 = ref()
const tipInputLen = ref()
const { screenpos } = globalDxf
const { tipconf } = globalTip
const styCadTip = computed(() => {
  let left = (screenpos.x + 60) + 'px'
  let top = (screenpos.y + 20) + 'px'
  return {
    top,
    left,
  }
})
const styInputLen = computed(() => {
  let left = (tipconf.inputposLen.x + 30) + 'px'
  let top = (tipconf.inputposLen.y - 50) + 'px'
  return {
    top,
    left,
  }
})
const styInputRad = computed(() => {
  let left = (tipconf.inputposRad.x + 30) + 'px'
  let top = (tipconf.inputposRad.y) + 'px'
  return {
    top,
    left,
  }
})

onMounted(() => {

})
document.onkeydown = (e) => {
  e.stopPropagation()
  if (e.key == "Tab") {
    e.preventDefault()
    if (tipconf.isPos) {
      tipconf.inputIdx = (tipconf.inputIdx + 1) % 2
      if (tipconf.inputIdx == 0) {
        tippos1.value.select()
        tippos1.value.focus()
      } else if (tipconf.inputIdx == 1) {
        tippos2.value.select()
        tippos2.value.focus()
      }
    } else if (tipconf.isLengthTip) {
      tipInputLen.value.select()
      tipInputLen.value.focus()
    }
  }

}
</script>

<style lang="scss" scoped>
.cadtips {
  position: absolute !important;
  top: 0px;
  right: 10px;
  z-index: 99999;
  width: fit-content;
  font-size: 14px;
  color: white;
  width: fit-content;
  display: flex;
}

.cadinput {
  height: 24px;
  line-height: 24px;
  width: 60px;
}

.b1 {
  border: 1px dashed #818b7b
}

.el-input,
.el-input--small,
.el-input__wrapper,
.el-input__inner {
  background-color: #7d7f7caf;
  color: white;
  height: 20px;
  line-height: 20px;
  text-align: center;
  padding: 0px;
}
</style>