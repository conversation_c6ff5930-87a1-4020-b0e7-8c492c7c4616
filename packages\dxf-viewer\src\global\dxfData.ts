import { PubSub } from "emitter"
import { initDxfBaseData } from "../global"



export const showProps=reactive({
    layer:{
        name:"",
        displayName:"",
        isVisible:true,
        isFreeze:true,
        isLock:true,
        color:'',
        linestyle:''
    },
    color:{
        name:"",
        color:""
    },
    lineStyle:{
        name:"",
    },
    isBackIdRender:false
})

export const exportAppointDxfData=(gp,id,sgcolor,vecint)=>{
  let gpstr=gp.serializeToStringCo(id,true,sgcolor,vecint)
  return gpstr
}


export interface intextBox{
    textpop:boolean,
    value:string,
    rotationAngle:string | number,
    position:any,
    height:string | number,
}
export const textBox:intextBox=reactive({
    textpop:false,
    value:'',
    rotationAngle:0,
    position:null,
    height:'',
})

export interface inblock{
    blockPop:boolean,
    value:string,
    blockList:any,
}
export const blockBox:inblock=reactive({
    blockPop:false,
    value:'',
    blockList:[],
})

export const viewlayout=reactive({
    visCodeEditor:false,
    visBlockGallery:false,
})
export const viewCmdInput=reactive({
    CmdInputValue:'',
})


export const DxfviewerCad=ref('') //CAD全局VIEWER






























//todo 此方法需要从 dynamicScene里面取得
const selectedIdsChanged=({type,ids})=>{
    console.log(type,ids)
}

PubSub.default.sub('selectionIdsChanged',selectedIdsChanged)

export const addQuantityCalculationLayer = (viewer,name,SGColor) => {
    const gp=viewer.transaction.gpObj
    const dxfbase=gp.getDxfBaseData()
    const layer = new sg.SGLayer()
    layer.setName(name)
    layer.setColor(SGColor)
    dxfbase.addLayer(layer)
    // initDxfBaseData(viewer.transaction.gpObj)
  }