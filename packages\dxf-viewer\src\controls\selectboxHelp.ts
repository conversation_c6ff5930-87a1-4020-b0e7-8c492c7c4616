import { Group,BufferAttribute ,Vector3,BufferGeometry,MeshBasicMaterial,LineBasicMaterial, DoubleSide, Mesh, Line} from "three";
import { DxfViewer } from "../viewer";
import { screen2wcs } from "./coordinateSystem";
import { debounce } from '../libs'

export class SelectboxHelp extends Group{
    public viewer: DxfViewer 
    public _v1:any;//模型坐标
    public _v2:any;
    public _screenV1:any;//屏幕坐标
    public _screenV2:any;
    public line:any;
    public mesh:any;
    public isSelecting:boolean=false
    constructor(viewer){//
        super()
        this.viewer=viewer
		this.line=this._createLine()
		this.mesh=this._createFace()

		this.add(this.line)
		this.add(this.mesh)
    }
	
    public setV1(event){
        this._screenV1={x:event.offsetX,y:event.offsetY}
        this._v1=screen2wcs(event,this.viewer.camera)
        this.isSelecting=true
    }

    public setV2(event){
        this._screenV2={x:event.offsetX,y:event.offsetY}
        this._v2=screen2wcs(event,this.viewer.camera)
        this.getmove()
        this.show()
    }
    public getmove(){
        let {vertices}=this.getvertices()
        this.line.geometry=new BufferGeometry().setFromPoints(vertices);
        // this.line.geometry.setFromPoints(verticesoBJ.verticesLi) //放大丢失 todo
        this.mesh.geometry=new BufferGeometry().setFromPoints(vertices);
        this.mesh.geometry.setIndex([0, 1, 2, 0, 2, 3])
        // this.mesh.geometry.setAttribute('position', new BufferAttribute(verticesoBJ.verticesBuf, 3)); //放大丢失 todo
        // this.mesh.geometry.attributes.position.needsUpdate=true
        let color=this._v2.x<this._v1.x?'green':'blue'
        this.mesh.material.color.set(color)        
    }

	public del(){
        this.isSelecting=false
		this.viewer.transientSceneWasm.scene.remove(this)
		this.viewer.Render()
	}

    public show(){
		this.viewer.transientSceneWasm.scene.add(this)
		this.viewer.Render()
    }

	private getvertices() {
        const pointA = this._v1
		const pointC = this._v2
		const pointB = new Vector3(pointC.x, pointA.y, pointA.z);
		const pointD = new Vector3(pointA.x, pointC.y, pointC.z);
		// 定义矩形的所有顶点
		const vertices = [pointA,pointB,pointC,pointD,pointA];

        // const verticesBuf = new Float32Array([
        //     pointA.x, pointA.y, pointA.z,
        //     pointB.x, pointB.y, pointB.z,
        //     pointC.x, pointC.y, pointC.z,
        //     pointD.x, pointD.y, pointD.z
        // ]);
        
        return {
            vertices,
            // verticesBuf
        }
    }

	private _createFace(){
        const geometry = new BufferGeometry().setFromPoints([]);
        geometry.setIndex([0, 1, 2, 0, 2, 3]);
        const material = new MeshBasicMaterial({
                side:DoubleSide,
                color: 'blue',
                transparent: true,
                opacity: 0.35 // 设置透明度，例如 0.5 表示半透明
        });
        const mesh = new Mesh(geometry, material); 
        return mesh
    }
	private _createLine(){
		const geometry = new BufferGeometry().setFromPoints([]);
        const material = new LineBasicMaterial({ color:'#fff'});
        const line= new Line(geometry, material);
		return line
	}
    public dispose(){
 
    }
}