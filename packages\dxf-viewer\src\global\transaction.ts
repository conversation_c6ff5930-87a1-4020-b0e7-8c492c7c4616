import { PubSub } from 'emitter';
import { pickupInfo } from "./indentify"
import { DxfViewer } from '../viewer';

function addtransaction(){
    return function (target,name,descriptor){
        var oldValue = descriptor.value;
        descriptor.value = function() {
            this.gpObj.getTransactionManager().beginTransaction()
            console.log(arguments,'arguments')
            oldValue.apply(this, arguments);
            this.gpObj.getTransactionManager().commit();
            if(arguments[1])this.gpRender();
            return
        };
        return descriptor;
    }
}

export class Transaction {
    static _sgDBG

    static get sgBDG(){//sg上下文=SGBaseDrawingGroup
        return Transaction._sgDBG
    }
    static _instance
    static get instance(){
        return this._instance
    }
    public dxfview:any
    
    public _gpObj:sg.SGBaseDrawingGroup=new sg.SGBaseDrawingGroup()
    public set gpObj(gp:sg.SGBaseDrawingGroup){
        this._gpObj=gp
        this._gpObj.initTransactionManager()
        Transaction._sgDBG=gp
    }
    public get gpObj(){
        return this._gpObj
    }

    constructor(viewer:DxfViewer){
        Transaction._instance=this
        this.dxfview=viewer
        this.gpObj=new sg.SGBaseDrawingGroup()
        PubSub.default.sub("wasmundo", this.prveStep.bind(this));
        PubSub.default.sub("wasmredo", this.nextStep.bind(this));
        PubSub.default.sub('copyarray', this.copyarray.bind(this));
    }


    @addtransaction()
    public addBlock1(obj,refresh){//加载块
        if(obj instanceof Array){
            console.log(obj,'addBlock1addBlock1addBlock1');
            
            obj.forEach(e=>{
                if(!this.gpObj.isExistBlockName(e.block)) {
                    this.gpObj.addBlock(e.block)
                }
                this.gpObj.addInsertBlock(e.InsertBlock)
                
                
                // this.gpObj.calSpInsertBlockObjList(e.InsertBlock)
            })
            console.log(this.gpObj.count(),'addBlock1addBlock1');
        }else{
            if(!this.gpObj.isExistBlockName(obj.block)) {
                this.gpObj.addBlock(obj.block)
            }
            this.gpObj.addInsertBlock(obj.InsertBlock)
            this.gpObj.calSpInsertBlockObjList(obj.InsertBlock)
        }

    }
    public addBlock(obj,refresh=true){
        this.addBlock1(obj,refresh)
    }

    @addtransaction()
    public copyarray1(layout,refresh){
        let {nrow,ncol,x,y}=layout
        pickupInfo.pickupIdlist.forEach(id=>{
            let o=this.gpObj.getPBaseObjById(id)
            for(let i=0;i<nrow;i++){
                for(let j=0;j<ncol;j++){
                    if(i+j>0){
                        let newobj=o.copyNewOne()
                        let trans=new sg.Matrix3()
                        trans.move(x*i,y*j)
                        newobj.transform(trans)
                        this.gpObj.addBaseObj(newobj)
                    }
                }
            }
        })
    }
    public copyarray(layout,refresh=true){
        this.copyarray1(layout,refresh)
    }

    public async add(objs){
        console.log('addaddaddaddaddaddaddaddaddaddadd');
        if(!(objs instanceof Array)){
            objs=[objs]
        }
        this.gpObj.getTransactionManager().beginTransaction()
        objs.forEach(obj=>{
            this.gpObj.addBaseObj(obj)
        })
        this.gpObj.getTransactionManager().commit();
        await this.dxfview.dxfScene.addEntities(objs)
        this.dxfview.Render()
    }

    public async del(ids){
        let arr= new sg.vector_int()
        ids.forEach(ite=> arr.push_back(ite))
        this.gpObj.getTransactionManager().beginTransaction()
        this.gpObj.deleteBaseObjByIds(arr)
        this.gpObj.getTransactionManager().commit();
        await this.dxfview.dxfScene.updateTransactions({del:ids})
        this.dxfview.Render()
    }

    public async prveStep(){
        console.log('prveStep');
        if(!this.gpObj) return
        if(!this.gpObj.getTransactionManager().canUndo()) return
        let actions=await this.gpObj.getTransactionManager().undo();
        await this.dxfview.dxfScene.updateTransactions(getTransactions(actions))
        this.dxfview.Render()
    }

    public async nextStep(){
        console.log('nextStep');
        if(!this.gpObj) return
        if(!this.gpObj.getTransactionManager().canRedo()) return
        let actions=await this.gpObj.getTransactionManager().redo()
        await this.dxfview.dxfScene.updateTransactions(getTransactions(actions))
        this.dxfview.Render()
    }

    public gpRender(){
        console.log('执行gpRender');
        
        this.dxfview.rebuildScene()
    }
}

const getTransactions=(actions)=>{
    let del = sgVector1Ary(actions.m_addIds);
    let add = sgVector1Ary(actions.m_deleteIds);
    let modify = sgVector1Ary(actions.m_modifyIds)
    return {add,del,modify}
}

const sgVector1Ary=(vectors:sg.vector_int)=>{
    let li:number[]=[]
    for(let i=0;i<vectors.size();i++){
        li.push(vectors.get(i))
    }
    return li
}
