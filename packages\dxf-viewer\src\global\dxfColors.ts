import { pickupInfo } from "./indentify"
import { Transaction } from "./transaction"
import { weakprops } from "../xData"

export const colorlist=[{    
    label:'图层',
    color:[255,255,255],
},{
    label:'块',
    color:[255,255,255],
},{
    label:'白色',
    color:[255,255,255],
},{
    label:'红色',
    color:[255,0,0],
},{
    label:'橙色',
    color:[255,200,0],
},{
    label:'黄色',
    color:[255,255,0],
},{
    label:'绿色',
    color:[0,125,0],
},{
    label:'青色',
    color:[0,255,255],
},{
    label:'蓝色',
    color:[0,0,255],
},{
    label:'紫色',
    color:[125,0,125],
}]

export const dxfColor=reactive({
    curColor:{    
        label:'同图层',
        color:[255,255,255]
    }
})

export const currentColor=ref([255,255,255])
export const changeSelectedColor_2=(e)=>{
    currentColor.value=e.color
    // pickupInfo.pickupIdlist.forEach(id=>{
    //     let {obj,properties,xdata}=weakprops.getProp(id)
    //     let [r,g,b]=e.color
    //     obj.setColor(new sg.SGColor(r,g,b,255))
    // })
    // Transaction.instance.gpRender()
}