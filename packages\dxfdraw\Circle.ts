import { BufferGeometry, EllipseCurve, LineBasicMaterial, LineLoop, Vector3,Color } from "three"
import { DrawHelp } from "./drawHelp"
import { singal } from "emitter"
import { DrawState, globalDxf, GraphicType, poptip, } from "dxf-viewer/src"


export class Circle extends DrawHelp {
    public pnts:Vector3[]=[]
    public mesh:any
    public count:number=0
    public iscomplete:boolean=false
    // public color:string='white'

    constructor(viewer) {
        // 调用父类的构造函数以初始化继承的属性
        super(viewer);
        const geometry = new BufferGeometry().setFromPoints([]);
        let colorArray = this.color; // 如果是Vue的ref，则需要.value来获取实际值
        // 将RGB数组转换为Three.js Color对象
        let color = new Color().setRGB(colorArray[0] / 255, colorArray[1] / 255, colorArray[2] / 255);
        const material = new LineBasicMaterial({ color:color});
        // 创建线条对象，使用LineLoop可以让首尾相连形成闭合的圆
        this.mesh = new LineLoop(geometry, material);
        this.viewer.transientSceneWasm.scene.add(this.mesh)
        this.init()
    }
    public async init(){
        let p0=await this.pointPick({tip:"指定圆心",tipInput:'',tipPos:''})
        this.setV(p0)
        let p1=await this.radiusPick({tip:'指定圆的半径',refPoint:this.pnts[0]})
        this.setV(p1)
        this.complete()
    }
    setV(v:Vector3){
        this.pnts.push(v)
    }
    complete(){
        let {x,y}=globalDxf.wcsOff
        this.viewer.transientSceneWasm.scene.remove(this.mesh)
        // this.mesh=null
        poptip.dispose()
        let r1=this.pnts[0].distanceTo(this.pnts[1]);
        let ac=new sg.Circle2d(new sg.Point2d(this.pnts[0].x + x, this.pnts[0].y + y), r1)
        let els:any=new sg.SGObjCurve()
        els.setpCurve(ac)
        let [r,g,b]=this.color
        els.setColor(new sg.SGColor(r,g,b,255))
        els.setLineType(this.lineType)
        this.viewer.transaction.add(els)
        globalDxf.operation=GraphicType.None
        globalDxf.isdraw = false
        this.isSnap=false
        setTimeout(()=>{
            globalDxf.drawstate=DrawState.Pick
        })
        // this.onCancel()
    }
    
    public override onLButtonDown( event:PointerEvent ) : boolean{
        singal.pickPoint.dispatch(event)        
        return true;
    }
    //鼠标移动
    public override onMouseMove( event:PointerEvent ) : boolean{
        super.onMouseMove(event)
        if(this.pnts.length>0){
            let secpt = this.getEventPoint(event)
            this.drawTempGraphic(secpt)
        }
        return true;
    }
    public drawTempGraphic(v:Vector3){
        let r=this.pnts[0].distanceTo(v);
        let curve = new EllipseCurve(this.pnts[0].x, this.pnts[0].y,  r,r, 0, 2 * Math.PI, false,0);
        let vertices=curve.getPoints(64)
        this.mesh.geometry=new BufferGeometry().setFromPoints(vertices);
    }
}