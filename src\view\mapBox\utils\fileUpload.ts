import SparkMD5 from 'spark-md5'

/**
 * 文件上传工具类
 */
export default class FileUploadUtils {
  /**
   * 使用Web Worker计算文件的MD5哈希值
   * @param file 要计算哈希的文件
   * @param progressCallback 进度回调函数
   * @returns Promise<string> 文件的MD5哈希值
   */
  static calculateFileHash(
    file: File,
    progressCallback?: (progress: number) => void,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // 创建Web Worker实例
        const workerPath = new URL('./hashWorker.js', import.meta.url).href;
        const worker = new Worker(workerPath);
        
        // 监听Worker消息
        worker.onmessage = (e) => {
          const { type, data } = e.data;
          
          switch (type) {
            case 'START':
              // 开始计算
              if (progressCallback) progressCallback(0);
              break;
            
            case 'PROGRESS':
              // 更新进度
              if (progressCallback) progressCallback(data);
              break;
            
            case 'COMPLETE':
              // 计算完成
              if (progressCallback) progressCallback(100);
              resolve(data);
              worker.terminate(); // 终止Worker
              break;
            
            case 'ERROR':
              reject(new Error(data));
              worker.terminate(); // 终止Worker
              break;
          }
        };
        
        // Worker出错处理
        worker.onerror = (e) => {
          console.error('Web Worker计算哈希出错:', e);
          reject(new Error('哈希计算失败'));
          worker.terminate();
          
          // 如果Worker失败，降级使用主线程计算
          console.log('降级到主线程计算哈希...');
          this.calculateFileHashInMainThread(file, progressCallback)
            .then(resolve)
            .catch(reject);
        };
        
        // 发送文件数据给Worker处理
        worker.postMessage({
          file,
          chunkSize: 2097152, // 2MB分片大小
        });
        
      } catch (error) {
        console.error('Web Worker初始化失败，降级到主线程计算:', error);
        // Worker不可用时，使用主线程计算
        this.calculateFileHashInMainThread(file, progressCallback)
          .then(resolve)
          .catch(reject);
      }
    });
  }
  
  /**
   * 在主线程中计算文件的MD5哈希值（降级备用）
   * @param file 要计算哈希的文件
   * @param progressCallback 进度回调函数
   * @returns Promise<string> 文件的MD5哈希值
   */
  static calculateFileHashInMainThread(
    file: File,
    progressCallback?: (progress: number) => void,
  ): Promise<string> {
    return new Promise((resolve) => {
      const chunkSize = 2097152 // 2MB
      const chunks = Math.ceil(file.size / chunkSize)
      let currentChunk = 0
      const spark = new SparkMD5.ArrayBuffer()
      const fileReader = new FileReader()

      fileReader.onload = (e) => {
        if (e.target?.result) {
          spark.append(e.target.result as ArrayBuffer)
          currentChunk++

          if (progressCallback) {
            const progress = Math.floor((currentChunk / chunks) * 100)
            progressCallback(progress)
          }

          if (currentChunk < chunks) {
            loadNext()
          } else {
            const hash = spark.end()
            resolve(hash)
          }
        }
      }

      fileReader.onerror = () => {
        console.error('文件读取失败')
        resolve('')
      }

      const loadNext = () => {
        const start = currentChunk * chunkSize
        const end = start + chunkSize >= file.size ? file.size : start + chunkSize
        fileReader.readAsArrayBuffer(file.slice(start, end))
      }

      loadNext()
    });
  }

  /**
   * 根据文件大小计算最佳分片大小
   * @param fileSize 文件大小(字节)
   * @returns number 最佳分片大小(MB)
   *
   * 规则：
   * - 小于 5MB 时不分片（返回0）
   * - 5-20MB 时分片大小 1MB
   * - 20-50MB 时分片大小 2MB
   * - 50-100MB 时分片大小 4MB
   * - 100-200MB 时分片大小 6MB
   * - 200-500MB 时分片大小 10MB
   * - 500MB-1GB 及以上时分片大小 20MB
   */
  static calculateOptimalChunkSize(fileSize: number): number {
    const MB = 1024 * 1024

    if (fileSize < 5 * MB) {
      return 0 // 不分片
    } else if (fileSize < 20 * MB) {
      return 1 // 1MB
    } else if (fileSize < 50 * MB) {
      return 2 // 2MB
    } else if (fileSize < 100 * MB) {
      return 4 // 4MB
    } else if (fileSize < 200 * MB) {
      return 6 // 6MB
    } else if (fileSize < 500 * MB) {
      return 10 // 10MB
    } else {
      return 20 // 20MB
    }
  }

  /**
   * 分割文件为多个块
   * @param file 要分割的文件
   * @param chunkSize 分片大小(MB)
   * @returns Blob[] 分片数组
   */
  static createFileChunks(file: File, chunkSize: number): Blob[] {
    const chunkByteSize = chunkSize * 1024 * 1024
    const chunkCount = Math.ceil(file.size / chunkByteSize)
    const chunks: Blob[] = []

    for (let i = 0; i < chunkCount; i++) {
      const start = i * chunkByteSize
      const end = Math.min(file.size, start + chunkByteSize)
      const chunk = file.slice(start, end)
      chunks.push(chunk)
    }

    return chunks
  }

  /**
   * 格式化文件大小显示
   * @param size 文件大小(字节)
   * @returns string 格式化后的文件大小
   */
  static formatFileSize(size: number): string {
    if (size < 1024) {
      return size + ' B'
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' KB'
    } else if (size < 1024 * 1024 * 1024) {
      return (size / (1024 * 1024)).toFixed(2) + ' MB'
    } else {
      return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
    }
  }

  /**
   * 从文件名获取文件扩展名
   * @param filename 文件名
   * @returns string 文件扩展名
   */
  static getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.')
    return lastDotIndex === -1 ? '' : filename.slice(lastDotIndex + 1).toLowerCase()
  }

  /**
   * 检查文件类型
   * @param file 文件对象
   * @returns boolean 是否
   */
  static isImageFile(file: File): boolean {
    const imageTypes = ['dwg', 'dxf', 'tif', 'tiff']
    return imageTypes.includes(file.type)
  }

  /**
   * 检查文件大小是否超过限制
   * @param file 文件对象
   * @param maxSize 最大文件大小(MB)
   * @returns boolean 是否超过限制
   */
  static isFileSizeExceeded(file: File, maxSize: number): boolean {
    const maxBytes = maxSize * 1024 * 1024
    return file.size > maxBytes
  }

  /**
   * 生成唯一的上传ID
   * @returns string 唯一ID
   */
  static generateUploadId(): string {
    return Date.now() + '-' + Math.random().toString(36).substr(2, 16)
  }
}
