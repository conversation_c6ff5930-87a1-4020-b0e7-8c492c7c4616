import { ClearArray } from "../libs"

export interface ILinetype{
    name:string
}

export const dxfLinetypes=reactive({
    curLinetypeIdx:0,
    curLinetype:{
        name:'Continuous' //直线
    },
    linetypes:[],//{name:''}
})


export const initDxfLinetypesData=(gp)=>{
    // let dxfbase=gp.getDxfBaseData()
    // let lineTypeCount=dxfbase.lineTypeCount()
    // let linetypes:ILinetype[]=[]
    // for(let i=0;i<lineTypeCount;i++){
    //     let linetype=dxfbase.getLineType(i)
    //     linetypes.push({
    //         name:linetype.getLinetypeName(),
    //     })
    // }
    // ClearArray(dxfLinetypes.linetypes)
    // dxfLinetypes.linetypes.push(...linetypes)
}
export const initLineTypeNams=(lineTypeNams)=>{
    setTimeout(()=>{
        let linetypes=lineTypeNams.map(item=>({name:item}))
        ClearArray(dxfLinetypes.linetypes)
        dxfLinetypes.linetypes.push(...linetypes)
    })
    // console.log(dxfLinetypes.linetypes,'dxfLinetypes.linetypes');
}