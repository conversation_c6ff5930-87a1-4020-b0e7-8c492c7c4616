# Excel 单元格合并功能说明

## 概述

本文档说明了对 `useExcel.js` 文件的修改，特别是为"常规支架组件"和"固定支架"标题行添加的特殊单元格合并功能。

## 修改内容

### 1. 新增常量定义

在 `ExcelGenerator.CONSTANTS` 中添加了特殊合并标题的定义：

```javascript
// 特殊合并规则：需要B:G合并的标题行
SPECIAL_MERGE_TITLES: ['常规支架组件', '固定支架']
```

### 2. 新增特殊合并检测方法

添加了 `isSpecialMergeTitle()` 方法来识别需要特殊处理的标题行：

```javascript
/**
 * 检查是否为特殊合并标题行（常规支架组件、固定支架）
 * @private
 * @param {Array} rowData - 行数据
 * @returns {boolean}
 */
isSpecialMergeTitle(rowData) {
  if (!Array.isArray(rowData) || rowData.length < 2) {
    return false
  }
  
  // 检查第二列（项目名称）是否包含特殊合并标题
  const projectName = String(rowData[1] || '').trim()
  return ExcelGenerator.CONSTANTS.SPECIAL_MERGE_TITLES.includes(projectName)
}
```

### 3. 优化内容行处理逻辑

修改了 `addContentRows()` 方法，增强了合并单元格的处理逻辑：

```javascript
// 处理合并单元格逻辑
if (isSpecialMerge) {
  // 特殊标题行：常规支架组件、固定支架 - 合并B:G列
  this.mergeList.push(`B${excelRowIndex}:G${excelRowIndex}`)
} else if (isBold) {
  // 其他粗体行 - 合并B:G列
  this.mergeList.push(`B${excelRowIndex}:G${excelRowIndex}`)
}

// 设置行样式
row.eachCell((cell) => {
  // 特殊标题行使用粗体样式
  const shouldBeBold = isBold || isSpecialMerge
  this.setCellStyle(cell, { bold: shouldBeBold })
})
```

## 功能特性

### 1. 智能识别

- **精确匹配**: 只有当第二列（项目名称）完全匹配"常规支架组件"或"固定支架"时才会应用特殊合并
- **容错处理**: 支持字符串前后空格的自动去除
- **类型安全**: 对输入数据进行严格的类型检查

### 2. 合并范围

- **目标列**: B列到G列（B:G）
- **保持A列独立**: A列（序号列）不参与合并，保持独立显示
- **样式一致**: 合并后的单元格保持与其他粗体行相同的样式

### 3. 兼容性

- **向后兼容**: 不影响现有的粗体行合并逻辑
- **数据格式**: 完全兼容 `useProjectBuild.js` 生成的数据格式
- **错误处理**: 增强的错误处理机制，确保合并失败不影响整体生成

## 使用示例

### 基础使用

```javascript
import { generateExcel } from './useExcel.js'

const excelData = [
  ['一', '低压安装'],
  ['1', '常规支架组件'],  // 这行的B:G列会被合并
  ['1.1', '605Wp', '规格', '计算规则', 100, '片', '60.50MW'],
  ['2', '固定支架'],      // 这行的B:G列会被合并
  ['2.1', '610Wp', '规格', '计算规则', 200, '片', '122.00MW'],
  ['总计', '', '', '', 300, '片', '182.50MW']
]

await generateExcel(excelData, {
  filename: '光伏工程量清单.xlsx'
})
```

### 与 useProjectBuild 集成

```javascript
import useProjectBuild from './useProjectBuild.js'
import { generateExcel } from './useExcel.js'

const { 
  CBComponent, 
  FixedComponent, 
  CB_Fixed_Count,
  getExcelRowData 
} = useProjectBuild(olBoxRef)

// 初始化数据
await initialize()

// 生成Excel（自动应用特殊合并）
const excelData = getExcelRowData()
await generateExcel(excelData, {
  filename: '光伏工程量清单.xlsx'
})
```

## 数据结构要求

### 输入数据格式

每行数据应该是一个数组，包含以下列：

```javascript
[
  '序号',        // 第1列 (A列) - 不参与合并
  '项目名称',    // 第2列 (B列) - 用于识别特殊标题
  '型号规格',    // 第3列 (C列)
  '工程量计算规则', // 第4列 (D列)
  '数量',        // 第5列 (E列)
  '计量单位',    // 第6列 (F列)
  '形象进度'     // 第7列 (G列)
]
```

### 特殊标题行识别

以下数据会被识别为特殊合并标题行：

```javascript
['1', '常规支架组件']  // ✅ 会合并B:G列
['2', '固定支架']      // ✅ 会合并B:G列
['1', '其他标题']      // ❌ 不会特殊处理
['一', '低压安装']     // ❌ 按普通粗体行处理
```

## 样式效果

### 合并前
```
| A  | B      | C | D | E | F | G |
|----|--------|---|---|---|---|---|
| 1  | 常规支架组件 |   |   |   |   |   |
```

### 合并后
```
| A  |    B:G (合并区域)    |
|----|---------------------|
| 1  |     常规支架组件      |
```

## 测试验证

### 1. 功能测试

使用提供的测试文件验证功能：

```javascript
import { runAllTests } from './useExcel.test.js'
runAllTests()
```

### 2. 集成测试

使用示例文件验证集成效果：

```javascript
import { runAllExamples } from './useExcel.usage.example.js'
await runAllExamples()
```

### 3. 手动验证

1. 生成Excel文件
2. 打开文件检查"常规支架组件"和"固定支架"行
3. 确认B:G列已正确合并
4. 验证样式（粗体、边框、对齐）是否正确

## 注意事项

### 1. 数据完整性

- 确保特殊标题行的第二列包含完整的标题文本
- 避免在标题文本前后添加额外的空格或字符

### 2. 性能考虑

- 特殊合并检测的性能开销很小
- 大数据量时建议使用批量处理

### 3. 错误处理

- 如果合并失败，会在控制台输出警告但不会中断整个生成过程
- 建议在生产环境中监控合并失败的情况

## 故障排除

### 常见问题

1. **标题行没有合并**
   - 检查第二列的文本是否完全匹配"常规支架组件"或"固定支架"
   - 确认数据格式正确（数组格式，至少2列）

2. **合并失败警告**
   - 检查Excel行索引是否正确
   - 确认没有重复的合并范围

3. **样式不一致**
   - 确认使用了最新版本的代码
   - 检查是否有自定义样式覆盖了默认设置

### 调试方法

1. 启用详细日志：
```javascript
const generator = new ExcelGenerator()
console.log('合并列表:', generator.mergeList)
```

2. 验证数据格式：
```javascript
const isValid = ExcelGenerator.validateData(yourData)
console.log('数据验证结果:', isValid)
```

3. 测试特殊标题识别：
```javascript
const generator = new ExcelGenerator()
const isSpecial = generator.isSpecialMergeTitle(['1', '常规支架组件'])
console.log('特殊标题识别:', isSpecial)
```
