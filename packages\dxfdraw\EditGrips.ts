
import {  Vector3 } from "three";

// 1. drag select grip to a new world position
// 2. show transient entity, currently just the point. TODO: later the whole polyline
// 3. edit real entity with transient entity, update main scene
export class EditGrips
{
    private isEditingGrips: Boolean;
    private isMove:Boolean
    private refPoint:Vector3
    private prevPoint:Vector3
    private viewer
    constructor(viewer){
        this.isEditingGrips = false;
        this.isMove=false
        this.refPoint=new Vector3()
        this.prevPoint=new Vector3()
        this.viewer=viewer
    }

    public isEditing(){
        return this.isEditingGrips;
    }

    public startEdit(worldPoint) {
        this.isEditingGrips = true;
        this.viewer.transientScene.updateTransientScene(worldPoint);
        this.viewer.Render();
    }

    public isMoving(){
        return this.isMove
    }

    public startMove(worldPoint){
        this.isMove=true
        this.refPoint=worldPoint
        this.prevPoint=worldPoint
    }

    public onMouseMove(worldPoint){
        if(this.isEditing()){
            this.viewer.transientScene.updateTransientScene(worldPoint);
        }
        if(this.isMoving()){
            let off=worldPoint.clone().sub(this.prevPoint)
            this.viewer.transientScene.moveGrips(off)
            this.prevPoint=worldPoint
        }
        this.viewer.Render();
    }

    public onMouseUp(worldPoint){
        if(this.isEditing()){
            this.isEditingGrips = false;
            this.viewer.cleanTransientScene();
        }
        if(this.isMoving()){
            this.isMove = false;
            let off=worldPoint.clone().sub(this.refPoint)

            this.viewer.endMoveGrips({x:off.x,y:off.y});
        }
    }

}