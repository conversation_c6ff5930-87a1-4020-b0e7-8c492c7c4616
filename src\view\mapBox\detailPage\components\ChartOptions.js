import * as echarts from "echarts";

export const ProgressChartOption = (data) => {
  return {
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "horizontal",
      right: "0%",
    },
    series: [
      {
        name: "组件安装进度",
        type: "pie",
        radius: "50%",
        data: [
          {
            value: data.reduce(
              (sum, item) => sum + item.complete,
              0
            ),
            name: "已完成",
          },
          {
            value: data.reduce(
              (sum, item) => sum + (item.total - item.complete),
              0
            ),
            name: "未完成",
          },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  }

  // var value = 0.69
  // var value1 = 1;
  // var data = [value, value1];
  // return {
  //   title: [
  //     {
  //       text: '组件安装进度',
  //       x: 'center',
  //       y: '80%',
  //       textStyle: {
  //         fontSize: 14,
  //         fontWeight: '100',
  //         color: '#5dc3ea',
  //         lineHeight: 16,
  //         textAlign: 'center',
  //       },
  //     },
  //   ],
  //   series: [
  //     {
  //       type: 'liquidFill',
  //       radius: '47%',
  //       center: ['50%', '50%'],
  //       color: [
  //         {
  //           type: 'linear',
  //           x: 0,
  //           y: 0,
  //           x2: 0,
  //           y2: 1,
  //           colorStops: [
  //             {
  //               offset: 0,
  //               color: '#446bf5',
  //             },
  //             {
  //               offset: 1,
  //               color: '#2ca3e2',
  //             },
  //           ],
  //           globalCoord: false,
  //         },
  //       ],
  //       data: [value, value], // data个数代表波浪数
  //       backgroundStyle: {
  //         borderWidth: 1,
  //         color: 'RGBA(51, 66, 127, 0.7)',
  //       },
  //       label: {
  //         normal: {
  //           textStyle: {
  //             fontSize: 50,
  //             color: '#fff',
  //           },
  //         },
  //       },
  //       outline: {
  //         // show: false
  //         borderDistance: 0,
  //         itemStyle: {
  //           borderWidth: 2,
  //           borderColor: '#112165',
  //         },
  //       },
  //     },
  //   ],
  // };

}


export const PowerDistributionChartOption = (data) => {
  // 按功率规格分组数据
  const powerData = data.map((item) => ({
    name: item.name,
    label: item.name,
    value: item.WM,
  }));
  return {
    tooltip: {
      // show: true,
      // trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        return `图例:${params.name}<br/>功率:${params?.value || 0} MW`;
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: powerData.map((item) => item.name),
        axisLine: {
          show: true,
          lineStyle: {
            color: "#063374",
            width: 1,
            type: "solid",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: "#00c7ff",
          },
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: "{value} MW",
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#00c7ff",
            width: 1,
            type: "solid",
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: "#063374",
          },
        },
      },
    ],
    series: [
      {
        type: "bar",
        barWidth: "40%",
        barGap: "10%",
        data: powerData.map((item) => item.value),
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "#00fcae",
              },
              {
                offset: 1,
                color: "#006388",
              },
            ]),
            opacity: 1,
            barBorderRadius: [20, 20, 0, 0]
          },
        },
      },
    ],
  }
}