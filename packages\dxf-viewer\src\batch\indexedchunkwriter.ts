import { IndexedChunk } from "./indexchunk"

export class IndexedChunkWriter {
    public chunk:IndexedChunk
    public verticesCount:number
    public verticesOffset:number
    public numVerticesPushed:number
    constructor(chunk, verticesCount) {
        this.chunk = chunk
        this.verticesCount = verticesCount
        this.verticesOffset = this.chunk.vertices.GetSize() / 2
        this.numVerticesPushed = 0
    }

    PushVertex(v) {
        if (this.numVerticesPushed === this.verticesCount) {
            throw new Error()
        }
        this.chunk.vertices.Push(v.x)
        this.chunk.vertices.Push(v.y)
        this.numVerticesPushed++
    }

    PushIndex(idx) {
        if (idx < 0 || idx >= this.verticesCount) {
            throw new Error(`Index out of range: ${idx}/${this.verticesCount}`)
        }
        this.chunk.indices.Push(idx + this.verticesOffset)
    }

    PushEntityId(id){
        this.chunk.entityIds.Push(id)
        this.chunk.batch.ids.add(id)
    }
    PushLineSyleId(id){
        this.chunk.lineStyleIds.Push(id)
    }
    PushLineScale(scale){
        this.chunk.lineScales.Push(scale)
    }
    PushEntityColor(color){
        this.chunk.entityColors.Push(color[0])
        this.chunk.entityColors.Push(color[1])
        this.chunk.entityColors.Push(color[2])
        this.chunk.entityColors.Push(color[3])
    }
    Finish() {
        if (this.numVerticesPushed !== this.verticesCount) {
            throw new Error(`Not all vertices pushed: ${this.numVerticesPushed}/${this.verticesCount}`)
        }
    }
}