import {filterByIds, hasItBeenSelected,globalDxf,GraphicType,filterGroupsByIds,
       pickupInfo,DrawState,SlScopeType,setupCursorStyle} from 'dxf-viewer'
import { PubSub } from 'emitter';
import { splitBlockId } from "dxf-viewer"

let dxfview:any=null
export class SLcontrol {
    // public dxfview:any
    constructor(obj){
        dxfview=obj
    }
    public setState(type){
        this.init(type)
        if(type===SlScopeType.Draw || type===SlScopeType.AreaDraw || type===SlScopeType.ThreeDDraw) {
            globalDxf.isSnapOn=true
            // PubSub.default.pub("executeCommand", 'Fence');   
            dxfview.GetViewer().grapicControl.draw('Fence')
        }else if(type===SlScopeType.RectangularRange){
            // PubSub.default.pub("executeCommand", 'RectangularRange');
            dxfview.GetViewer().grapicControl.draw('RectangularRange')
        }else if(type===SlScopeType.PolygonRange){
            // PubSub.default.pub("executeCommand", 'PolygonRange');
            dxfview.GetViewer().grapicControl.draw('PolygonRange')
        }else if(type===SlScopeType.Scale){
            globalDxf.isSnapOn=true
            // PubSub.default.pub("executeCommand", 'Scale');
            dxfview.GetViewer().grapicControl.draw('Scale')
        }else if(type===SlScopeType.Producepicture){
            // PubSub.default.pub("executeCommand", 'Producepicture');
            dxfview.GetViewer().grapicControl.draw('Producepicture')
        }else {
            globalDxf.operation=GraphicType.None
            globalDxf.drawstate=DrawState.Pick
            this.clickSelect()
        }
        
        setupCursorStyle("dxfviewercontainer")
    }
    public init (type){
        pickupInfo.slcurrentstate=type
        pickupInfo.pickupFalg=type===SlScopeType.Graphicelementimg //是否生成图片
        pickupInfo.pickupAttributeFalg=type===SlScopeType.PeiDistinguish //是否获取图元属性
        PubSub.default.pub("slidentifyclear");//清除
        globalDxf.drawstate=DrawState.Drawing
        dxfview.GetViewer().selector.frameSelectSwitch=true
    }

    public clickSelect(){
        switch(pickupInfo.slcurrentstate){
            case SlScopeType.Del:
                globalDxf.highlFilter=this.SLselectedSelect
              break;
            case SlScopeType.AI:
                globalDxf.highlFilter=this.SLAISelect
              break;
            case  SlScopeType.Pickup:
                globalDxf.operation=''
                globalDxf.highlFilter=this.SLAISelect
              break;
            case  SlScopeType.PeiDistinguish:
                globalDxf.operation=''
                globalDxf.highlFilter=this.SLAISelect
                dxfview.GetViewer().selector.frameSelectSwitch=false
              break;
            case  SlScopeType.AreaPickup:
                globalDxf.operation=''
                globalDxf.highlFilter=this.SLAISelect
              break;
            case  SlScopeType.ThreeDPickup:
                globalDxf.operation=''
                globalDxf.highlFilter=this.SLAISelect
              break;
            default:
                globalDxf.highlFilter=this.SLselect
        }
    }
    public SLselectedSelect(entityId,color){
        let ele2=hasItBeenSelected(pickupInfo.hideSelectedIdObj.elements,entityId) || []
        let lsentityId2 = entityId.filter(item => !ele2.includes(item));
        let gro2=filterGroupsByIds(pickupInfo.hideSelectedIdObj.groups,lsentityId2) || []
        let arr2:any=[...ele2]
        gro2.forEach(item=>{
            item.graphGroups.forEach(itm=>{
                itm.idList.forEach(ite=>{
                    ite.ids.forEach(i=>arr2.push(i))
                })
            })
        })
        let fenceIds=entityId.filter(id=>{
           return pickupInfo.customID.includes(id)
        })
        arr2=[...arr2,...fenceIds]
        return {
            color,
            entityId:arr2,
        }
    }
    public SLselect(entityId){
        let  newEntityId2=entityId.filter(item=>!pickupInfo.selectedID.includes(item))
        return {
            color:'',
            entityId:newEntityId2,
        }
    }
    public SLAISelect=(entityId2)=>{
        let gp=dxfview.GetViewer().transaction.gpObj
        let entityId3=entityId2.filter(item=>!pickupInfo.selectedID.includes(item)) || []
        let newEntityId2:any=[]
        entityId3.forEach(id=>{
            let {entityId,blockId}=splitBlockId(id) //entityId块里面的图元ID  blockId快ID
            console.log(entityId,blockId,'entityId');
            let  entity:any=null
            if(blockId) {
                entity= gp.getInsertBaseObjById(blockId,entityId)
            }else {
                entity= gp.getPBaseObjById(id)
            }

            // console.log(entity,'entity');
            
            if(entity) {
                const type =entity.getObjType()
                let arr=[SlScopeType.Pickup,SlScopeType.PeiDistinguish]
                if (arr.includes(pickupInfo.slcurrentstate) ){//拾取和识图
                    if(type === 'SGObjCurve' || type === 'SGObjComBinCurve') {
                        newEntityId2.push(id)
                    }
                }else if(pickupInfo.slcurrentstate===SlScopeType.ThreeDPickup ){ //&& !blockId
                    if( type === 'SGObjComBinCurve') {
                        newEntityId2.push(id)
                    }else if(type === 'SGObjCurve') {
                        let type2 = sg.SGObjTool.convertToCurve(entity).getCurveType();
                        if(type2==='LineSegment2d') {
                            newEntityId2.push(id)
                        }
                    }
                }else if(pickupInfo.slcurrentstate===SlScopeType.AI) {
                    if(type === 'SGObjText') {//AI识别文字
                        newEntityId2.push(id)
                    }
                }else if(pickupInfo.slcurrentstate===SlScopeType.AreaPickup) {
                    if('SGObjCurve' == type) {
                        let type = sg.SGObjTool.convertToCurve(entity).getCurveType();
                        if(type=='Ellipse2d' || type=='Circle2d') {
                            newEntityId2.push(id)
                        }
                    }else if(type === 'SGObjComBinCurve') {
                        let tt = sg.SGObjTool.convertToComBinCurve(entity)
                        let isbh=tt.getCurve().isClosed()
                        let iszj=tt.getCurve().isSelfIntersect()
                        !iszj && isbh &&  newEntityId2.push(id)
                    }
                }
            }

        })
        return {
            color:'',
            entityId:newEntityId2,
        } 
    }
}
